!function(e){var t,n=e.babelHelpers={};n.typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n.jsx=(t="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,function(e,n,i,r){var o=e&&e.defaultProps,s=arguments.length-3;if(n||0===s||(n={}),n&&o)for(var a in o)void 0===n[a]&&(n[a]=o[a]);else n||(n=o||{});if(1===s)n.children=r;else if(s>1){for(var l=Array(s),u=0;u<s;u++)l[u]=arguments[u+3];n.children=l}return{$$typeof:t,type:e,key:void 0===i?null:""+i,ref:null,props:n,_owner:null}}),n.asyncIterator=function(e){if("function"==typeof Symbol){if(Symbol.asyncIterator){var t=e[Symbol.asyncIterator];if(null!=t)return t.call(e)}if(Symbol.iterator)return e[Symbol.iterator]()}throw new TypeError("Object is not async iterable")},n.asyncGenerator=function(){function e(e){this.value=e}function t(t){var n,i;function r(n,i){try{var s=t[n](i),a=s.value;a instanceof e?Promise.resolve(a.value).then((function(e){r("next",e)}),(function(e){r("throw",e)})):o(s.done?"return":"normal",s.value)}catch(e){o("throw",e)}}function o(e,t){switch(e){case"return":n.resolve({value:t,done:!0});break;case"throw":n.reject(t);break;default:n.resolve({value:t,done:!1})}(n=n.next)?r(n.key,n.arg):i=null}this._invoke=function(e,t){return new Promise((function(o,s){var a={key:e,arg:t,resolve:o,reject:s,next:null};i?i=i.next=a:(n=i=a,r(e,t))}))},"function"!=typeof t.return&&(this.return=void 0)}return"function"==typeof Symbol&&Symbol.asyncIterator&&(t.prototype[Symbol.asyncIterator]=function(){return this}),t.prototype.next=function(e){return this._invoke("next",e)},t.prototype.throw=function(e){return this._invoke("throw",e)},t.prototype.return=function(e){return this._invoke("return",e)},{wrap:function(e){return function(){return new t(e.apply(this,arguments))}},await:function(t){return new e(t)}}}(),n.asyncGeneratorDelegate=function(e,t){var n={},i=!1;function r(n,r){return i=!0,r=new Promise((function(t){t(e[n](r))})),{done:!1,value:t(r)}}return"function"==typeof Symbol&&Symbol.iterator&&(n[Symbol.iterator]=function(){return this}),n.next=function(e){return i?(i=!1,e):r("next",e)},"function"==typeof e.throw&&(n.throw=function(e){if(i)throw i=!1,e;return r("throw",e)}),"function"==typeof e.return&&(n.return=function(e){return r("return",e)}),n},n.asyncToGenerator=function(e){return function(){var t=e.apply(this,arguments);return new Promise((function(e,n){return function i(r,o){try{var s=t[r](o),a=s.value}catch(e){return void n(e)}if(!s.done)return Promise.resolve(a).then((function(e){i("next",e)}),(function(e){i("throw",e)}));e(a)}("next")}))}},n.classCallCheck=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},n.createClass=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),n.defineEnumerableProperties=function(e,t){for(var n in t){var i=t[n];i.configurable=i.enumerable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,n,i)}return e},n.defaults=function(e,t){for(var n=Object.getOwnPropertyNames(t),i=0;i<n.length;i++){var r=n[i],o=Object.getOwnPropertyDescriptor(t,r);o&&o.configurable&&void 0===e[r]&&Object.defineProperty(e,r,o)}return e},n.defineProperty=function(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},n.extends=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},n.get=function e(t,n,i){null===t&&(t=Function.prototype);var r=Object.getOwnPropertyDescriptor(t,n);if(void 0===r){var o=Object.getPrototypeOf(t);return null===o?void 0:e(o,n,i)}if("value"in r)return r.value;var s=r.get;return void 0!==s?s.call(i):void 0},n.inherits=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)},n.instanceof=function(e,t){return null!=t&&"undefined"!=typeof Symbol&&t[Symbol.hasInstance]?t[Symbol.hasInstance](e):e instanceof t},n.interopRequireDefault=function(e){return e&&e.__esModule?e:{default:e}},n.interopRequireWildcard=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t},n.newArrowCheck=function(e,t){if(e!==t)throw new TypeError("Cannot instantiate an arrow function")},n.objectDestructuringEmpty=function(e){if(null==e)throw new TypeError("Cannot destructure undefined")},n.objectWithoutProperties=function(e,t){var n={};for(var i in e)t.indexOf(i)>=0||Object.prototype.hasOwnProperty.call(e,i)&&(n[i]=e[i]);return n},n.possibleConstructorReturn=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t},n.selfGlobal=void 0===e?self:e,n.set=function e(t,n,i,r){var o=Object.getOwnPropertyDescriptor(t,n);if(void 0===o){var s=Object.getPrototypeOf(t);null!==s&&e(s,n,i,r)}else if("value"in o&&o.writable)o.value=i;else{var a=o.set;void 0!==a&&a.call(r,i)}return i},n.slicedToArray=function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var n=[],i=!0,r=!1,o=void 0;try{for(var s,a=e[Symbol.iterator]();!(i=(s=a.next()).done)&&(n.push(s.value),!t||n.length!==t);i=!0);}catch(e){r=!0,o=e}finally{try{!i&&a.return&&a.return()}finally{if(r)throw o}}return n}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")},n.slicedToArrayLoose=function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e)){for(var n,i=[],r=e[Symbol.iterator]();!(n=r.next()).done&&(i.push(n.value),!t||i.length!==t););return i}throw new TypeError("Invalid attempt to destructure non-iterable instance")},n.taggedTemplateLiteral=function(e,t){return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))},n.taggedTemplateLiteralLoose=function(e,t){return e.raw=t,e},n.temporalRef=function(e,t,n){if(e===n)throw new ReferenceError(t+" is not defined - temporal dead zone");return e},n.temporalUndefined={},n.toArray=function(e){return Array.isArray(e)?e:Array.from(e)},n.toConsumableArray=function(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}}("undefined"==typeof global?self:global),
/*!
 * jQuery JavaScript Library v3.2.1
 * https://jquery.com/
 *
 * Includes Sizzle.js
 * https://sizzlejs.com/
 *
 * Copyright JS Foundation and other contributors
 * Released under the MIT license
 * https://jquery.org/license
 *
 * Date: 2017-03-20T18:59Z
 */
function(e,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,(function(e,t){"use strict";var n=[],i=e.document,r=Object.getPrototypeOf,o=n.slice,s=n.concat,a=n.push,l=n.indexOf,u={},c=u.toString,f=u.hasOwnProperty,h=f.toString,d=h.call(Object),p={};function g(e,t){var n=(t=t||i).createElement("script");n.text=e,t.head.appendChild(n).parentNode.removeChild(n)}var m="3.2.1",v=function(e,t){return new v.fn.init(e,t)},y=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,b=/^-ms-/,w=/-([a-z])/g,_=function(e,t){return t.toUpperCase()};function x(e){var t=!!e&&"length"in e&&e.length,n=v.type(e);return"function"!==n&&!v.isWindow(e)&&("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}v.fn=v.prototype={jquery:m,constructor:v,length:0,toArray:function(){return o.call(this)},get:function(e){return null==e?o.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=v.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return v.each(this,e)},map:function(e){return this.pushStack(v.map(this,(function(t,n){return e.call(t,n,t)})))},slice:function(){return this.pushStack(o.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:a,sort:n.sort,splice:n.splice},v.extend=v.fn.extend=function(){var e,t,n,i,r,o,s=arguments[0]||{},a=1,l=arguments.length,u=!1;for("boolean"==typeof s&&(u=s,s=arguments[a]||{},a++),"object"==typeof s||v.isFunction(s)||(s={}),a===l&&(s=this,a--);a<l;a++)if(null!=(e=arguments[a]))for(t in e)n=s[t],s!==(i=e[t])&&(u&&i&&(v.isPlainObject(i)||(r=Array.isArray(i)))?(r?(r=!1,o=n&&Array.isArray(n)?n:[]):o=n&&v.isPlainObject(n)?n:{},s[t]=v.extend(u,o,i)):void 0!==i&&(s[t]=i));return s},v.extend({expando:"jQuery"+(m+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===v.type(e)},isWindow:function(e){return null!=e&&e===e.window},isNumeric:function(e){var t=v.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==c.call(e))&&(!(t=r(e))||"function"==typeof(n=f.call(t,"constructor")&&t.constructor)&&h.call(n)===d)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?u[c.call(e)]||"object":typeof e},globalEval:function(e){g(e)},camelCase:function(e){return e.replace(b,"ms-").replace(w,_)},each:function(e,t){var n,i=0;if(x(e))for(n=e.length;i<n&&!1!==t.call(e[i],i,e[i]);i++);else for(i in e)if(!1===t.call(e[i],i,e[i]))break;return e},trim:function(e){return null==e?"":(e+"").replace(y,"")},makeArray:function(e,t){var n=t||[];return null!=e&&(x(Object(e))?v.merge(n,"string"==typeof e?[e]:e):a.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:l.call(t,e,n)},merge:function(e,t){for(var n=+t.length,i=0,r=e.length;i<n;i++)e[r++]=t[i];return e.length=r,e},grep:function(e,t,n){for(var i=[],r=0,o=e.length,s=!n;r<o;r++)!t(e[r],r)!==s&&i.push(e[r]);return i},map:function(e,t,n){var i,r,o=0,a=[];if(x(e))for(i=e.length;o<i;o++)null!=(r=t(e[o],o,n))&&a.push(r);else for(o in e)null!=(r=t(e[o],o,n))&&a.push(r);return s.apply([],a)},guid:1,proxy:function(e,t){var n,i,r;if("string"==typeof t&&(n=e[t],t=e,e=n),v.isFunction(e))return i=o.call(arguments,2),r=function(){return e.apply(t||this,i.concat(o.call(arguments)))},r.guid=e.guid=e.guid||v.guid++,r},now:Date.now,support:p}),"function"==typeof Symbol&&(v.fn[Symbol.iterator]=n[Symbol.iterator]),v.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(e,t){u["[object "+t+"]"]=t.toLowerCase()}));var C=
/*!
 * Sizzle CSS Selector Engine v2.3.3
 * https://sizzlejs.com/
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license
 * http://jquery.org/license
 *
 * Date: 2016-08-08
 */
function(e){var t,n,i,r,o,s,a,l,u,c,f,h,d,p,g,m,v,y,b,w="sizzle"+1*new Date,_=e.document,x=0,C=0,E=se(),T=se(),S=se(),k=function(e,t){return e===t&&(f=!0),0},D={}.hasOwnProperty,A=[],N=A.pop,L=A.push,O=A.push,I=A.slice,j=function(e,t){for(var n=0,i=e.length;n<i;n++)if(e[n]===t)return n;return-1},P="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",H="[\\x20\\t\\r\\n\\f]",$="(?:\\\\.|[\\w-]|[^\0-\\xa0])+",M="\\["+H+"*("+$+")(?:"+H+"*([*^$|!~]?=)"+H+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+$+"))|)"+H+"*\\]",F=":("+$+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+M+")*)|.*)\\)|)",B=new RegExp(H+"+","g"),W=new RegExp("^"+H+"+|((?:^|[^\\\\])(?:\\\\.)*)"+H+"+$","g"),R=new RegExp("^"+H+"*,"+H+"*"),q=new RegExp("^"+H+"*([>+~]|"+H+")"+H+"*"),U=new RegExp("="+H+"*([^\\]'\"]*?)"+H+"*\\]","g"),z=new RegExp(F),Y=new RegExp("^"+$+"$"),Q={ID:new RegExp("^#("+$+")"),CLASS:new RegExp("^\\.("+$+")"),TAG:new RegExp("^("+$+"|[*])"),ATTR:new RegExp("^"+M),PSEUDO:new RegExp("^"+F),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+H+"*(even|odd|(([+-]|)(\\d*)n|)"+H+"*(?:([+-]|)"+H+"*(\\d+)|))"+H+"*\\)|)","i"),bool:new RegExp("^(?:"+P+")$","i"),needsContext:new RegExp("^"+H+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+H+"*((?:-\\d)?\\d*)"+H+"*\\)|)(?=[^-]|$)","i")},X=/^(?:input|select|textarea|button)$/i,K=/^h\d$/i,V=/^[^{]+\{\s*\[native \w/,G=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,J=/[+~]/,Z=new RegExp("\\\\([\\da-f]{1,6}"+H+"?|("+H+")|.)","ig"),ee=function(e,t,n){var i="0x"+t-65536;return i!=i||n?t:i<0?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,1023&i|56320)},te=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,ne=function(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},ie=function(){h()},re=ye((function(e){return!0===e.disabled&&("form"in e||"label"in e)}),{dir:"parentNode",next:"legend"});try{O.apply(A=I.call(_.childNodes),_.childNodes),A[_.childNodes.length].nodeType}catch(e){O={apply:A.length?function(e,t){L.apply(e,I.call(t))}:function(e,t){for(var n=e.length,i=0;e[n++]=t[i++];);e.length=n-1}}}function oe(e,t,i,r){var o,a,u,c,f,p,v,y=t&&t.ownerDocument,x=t?t.nodeType:9;if(i=i||[],"string"!=typeof e||!e||1!==x&&9!==x&&11!==x)return i;if(!r&&((t?t.ownerDocument||t:_)!==d&&h(t),t=t||d,g)){if(11!==x&&(f=G.exec(e)))if(o=f[1]){if(9===x){if(!(u=t.getElementById(o)))return i;if(u.id===o)return i.push(u),i}else if(y&&(u=y.getElementById(o))&&b(t,u)&&u.id===o)return i.push(u),i}else{if(f[2])return O.apply(i,t.getElementsByTagName(e)),i;if((o=f[3])&&n.getElementsByClassName&&t.getElementsByClassName)return O.apply(i,t.getElementsByClassName(o)),i}if(n.qsa&&!S[e+" "]&&(!m||!m.test(e))){if(1!==x)y=t,v=e;else if("object"!==t.nodeName.toLowerCase()){for((c=t.getAttribute("id"))?c=c.replace(te,ne):t.setAttribute("id",c=w),a=(p=s(e)).length;a--;)p[a]="#"+c+" "+ve(p[a]);v=p.join(","),y=J.test(e)&&ge(t.parentNode)||t}if(v)try{return O.apply(i,y.querySelectorAll(v)),i}catch(e){}finally{c===w&&t.removeAttribute("id")}}}return l(e.replace(W,"$1"),t,i,r)}function se(){var e=[];return function t(n,r){return e.push(n+" ")>i.cacheLength&&delete t[e.shift()],t[n+" "]=r}}function ae(e){return e[w]=!0,e}function le(e){var t=d.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function ue(e,t){for(var n=e.split("|"),r=n.length;r--;)i.attrHandle[n[r]]=t}function ce(e,t){var n=t&&e,i=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(i)return i;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function fe(e){return function(t){return"input"===t.nodeName.toLowerCase()&&t.type===e}}function he(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function de(e){return function(t){return"form"in t?t.parentNode&&!1===t.disabled?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&re(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function pe(e){return ae((function(t){return t=+t,ae((function(n,i){for(var r,o=e([],n.length,t),s=o.length;s--;)n[r=o[s]]&&(n[r]=!(i[r]=n[r]))}))}))}function ge(e){return e&&void 0!==e.getElementsByTagName&&e}for(t in n=oe.support={},o=oe.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},h=oe.setDocument=function(e){var t,r,s=e?e.ownerDocument||e:_;return s!==d&&9===s.nodeType&&s.documentElement?(p=(d=s).documentElement,g=!o(d),_!==d&&(r=d.defaultView)&&r.top!==r&&(r.addEventListener?r.addEventListener("unload",ie,!1):r.attachEvent&&r.attachEvent("onunload",ie)),n.attributes=le((function(e){return e.className="i",!e.getAttribute("className")})),n.getElementsByTagName=le((function(e){return e.appendChild(d.createComment("")),!e.getElementsByTagName("*").length})),n.getElementsByClassName=V.test(d.getElementsByClassName),n.getById=le((function(e){return p.appendChild(e).id=w,!d.getElementsByName||!d.getElementsByName(w).length})),n.getById?(i.filter.ID=function(e){var t=e.replace(Z,ee);return function(e){return e.getAttribute("id")===t}},i.find.ID=function(e,t){if(void 0!==t.getElementById&&g){var n=t.getElementById(e);return n?[n]:[]}}):(i.filter.ID=function(e){var t=e.replace(Z,ee);return function(e){var n=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}},i.find.ID=function(e,t){if(void 0!==t.getElementById&&g){var n,i,r,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(r=t.getElementsByName(e),i=0;o=r[i++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),i.find.TAG=n.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):n.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,i=[],r=0,o=t.getElementsByTagName(e);if("*"===e){for(;n=o[r++];)1===n.nodeType&&i.push(n);return i}return o},i.find.CLASS=n.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&g)return t.getElementsByClassName(e)},v=[],m=[],(n.qsa=V.test(d.querySelectorAll))&&(le((function(e){p.appendChild(e).innerHTML="<a id='"+w+"'></a><select id='"+w+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&m.push("[*^$]="+H+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||m.push("\\["+H+"*(?:value|"+P+")"),e.querySelectorAll("[id~="+w+"-]").length||m.push("~="),e.querySelectorAll(":checked").length||m.push(":checked"),e.querySelectorAll("a#"+w+"+*").length||m.push(".#.+[+~]")})),le((function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=d.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&m.push("name"+H+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&m.push(":enabled",":disabled"),p.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&m.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),m.push(",.*:")}))),(n.matchesSelector=V.test(y=p.matches||p.webkitMatchesSelector||p.mozMatchesSelector||p.oMatchesSelector||p.msMatchesSelector))&&le((function(e){n.disconnectedMatch=y.call(e,"*"),y.call(e,"[s!='']:x"),v.push("!=",F)})),m=m.length&&new RegExp(m.join("|")),v=v.length&&new RegExp(v.join("|")),t=V.test(p.compareDocumentPosition),b=t||V.test(p.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,i=t&&t.parentNode;return e===i||!(!i||1!==i.nodeType||!(n.contains?n.contains(i):e.compareDocumentPosition&&16&e.compareDocumentPosition(i)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},k=t?function(e,t){if(e===t)return f=!0,0;var i=!e.compareDocumentPosition-!t.compareDocumentPosition;return i||(1&(i=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!n.sortDetached&&t.compareDocumentPosition(e)===i?e===d||e.ownerDocument===_&&b(_,e)?-1:t===d||t.ownerDocument===_&&b(_,t)?1:c?j(c,e)-j(c,t):0:4&i?-1:1)}:function(e,t){if(e===t)return f=!0,0;var n,i=0,r=e.parentNode,o=t.parentNode,s=[e],a=[t];if(!r||!o)return e===d?-1:t===d?1:r?-1:o?1:c?j(c,e)-j(c,t):0;if(r===o)return ce(e,t);for(n=e;n=n.parentNode;)s.unshift(n);for(n=t;n=n.parentNode;)a.unshift(n);for(;s[i]===a[i];)i++;return i?ce(s[i],a[i]):s[i]===_?-1:a[i]===_?1:0},d):d},oe.matches=function(e,t){return oe(e,null,null,t)},oe.matchesSelector=function(e,t){if((e.ownerDocument||e)!==d&&h(e),t=t.replace(U,"='$1']"),n.matchesSelector&&g&&!S[t+" "]&&(!v||!v.test(t))&&(!m||!m.test(t)))try{var i=y.call(e,t);if(i||n.disconnectedMatch||e.document&&11!==e.document.nodeType)return i}catch(e){}return oe(t,d,null,[e]).length>0},oe.contains=function(e,t){return(e.ownerDocument||e)!==d&&h(e),b(e,t)},oe.attr=function(e,t){(e.ownerDocument||e)!==d&&h(e);var r=i.attrHandle[t.toLowerCase()],o=r&&D.call(i.attrHandle,t.toLowerCase())?r(e,t,!g):void 0;return void 0!==o?o:n.attributes||!g?e.getAttribute(t):(o=e.getAttributeNode(t))&&o.specified?o.value:null},oe.escape=function(e){return(e+"").replace(te,ne)},oe.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},oe.uniqueSort=function(e){var t,i=[],r=0,o=0;if(f=!n.detectDuplicates,c=!n.sortStable&&e.slice(0),e.sort(k),f){for(;t=e[o++];)t===e[o]&&(r=i.push(o));for(;r--;)e.splice(i[r],1)}return c=null,e},r=oe.getText=function(e){var t,n="",i=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=r(e)}else if(3===o||4===o)return e.nodeValue}else for(;t=e[i++];)n+=r(t);return n},i=oe.selectors={cacheLength:50,createPseudo:ae,match:Q,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(Z,ee),e[3]=(e[3]||e[4]||e[5]||"").replace(Z,ee),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||oe.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&oe.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return Q.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&z.test(n)&&(t=s(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(Z,ee).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=E[e+" "];return t||(t=new RegExp("(^|"+H+")"+e+"("+H+"|$)"))&&E(e,(function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")}))},ATTR:function(e,t,n){return function(i){var r=oe.attr(i,e);return null==r?"!="===t:!t||(r+="","="===t?r===n:"!="===t?r!==n:"^="===t?n&&0===r.indexOf(n):"*="===t?n&&r.indexOf(n)>-1:"$="===t?n&&r.slice(-n.length)===n:"~="===t?(" "+r.replace(B," ")+" ").indexOf(n)>-1:"|="===t&&(r===n||r.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,i,r){var o="nth"!==e.slice(0,3),s="last"!==e.slice(-4),a="of-type"===t;return 1===i&&0===r?function(e){return!!e.parentNode}:function(t,n,l){var u,c,f,h,d,p,g=o!==s?"nextSibling":"previousSibling",m=t.parentNode,v=a&&t.nodeName.toLowerCase(),y=!l&&!a,b=!1;if(m){if(o){for(;g;){for(h=t;h=h[g];)if(a?h.nodeName.toLowerCase()===v:1===h.nodeType)return!1;p=g="only"===e&&!p&&"nextSibling"}return!0}if(p=[s?m.firstChild:m.lastChild],s&&y){for(b=(d=(u=(c=(f=(h=m)[w]||(h[w]={}))[h.uniqueID]||(f[h.uniqueID]={}))[e]||[])[0]===x&&u[1])&&u[2],h=d&&m.childNodes[d];h=++d&&h&&h[g]||(b=d=0)||p.pop();)if(1===h.nodeType&&++b&&h===t){c[e]=[x,d,b];break}}else if(y&&(b=d=(u=(c=(f=(h=t)[w]||(h[w]={}))[h.uniqueID]||(f[h.uniqueID]={}))[e]||[])[0]===x&&u[1]),!1===b)for(;(h=++d&&h&&h[g]||(b=d=0)||p.pop())&&((a?h.nodeName.toLowerCase()!==v:1!==h.nodeType)||!++b||(y&&((c=(f=h[w]||(h[w]={}))[h.uniqueID]||(f[h.uniqueID]={}))[e]=[x,b]),h!==t)););return(b-=r)===i||b%i==0&&b/i>=0}}},PSEUDO:function(e,t){var n,r=i.pseudos[e]||i.setFilters[e.toLowerCase()]||oe.error("unsupported pseudo: "+e);return r[w]?r(t):r.length>1?(n=[e,e,"",t],i.setFilters.hasOwnProperty(e.toLowerCase())?ae((function(e,n){for(var i,o=r(e,t),s=o.length;s--;)e[i=j(e,o[s])]=!(n[i]=o[s])})):function(e){return r(e,0,n)}):r}},pseudos:{not:ae((function(e){var t=[],n=[],i=a(e.replace(W,"$1"));return i[w]?ae((function(e,t,n,r){for(var o,s=i(e,null,r,[]),a=e.length;a--;)(o=s[a])&&(e[a]=!(t[a]=o))})):function(e,r,o){return t[0]=e,i(t,null,o,n),t[0]=null,!n.pop()}})),has:ae((function(e){return function(t){return oe(e,t).length>0}})),contains:ae((function(e){return e=e.replace(Z,ee),function(t){return(t.textContent||t.innerText||r(t)).indexOf(e)>-1}})),lang:ae((function(e){return Y.test(e||"")||oe.error("unsupported lang: "+e),e=e.replace(Z,ee).toLowerCase(),function(t){var n;do{if(n=g?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(n=n.toLowerCase())===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}})),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===p},focus:function(e){return e===d.activeElement&&(!d.hasFocus||d.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:de(!1),disabled:de(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!i.pseudos.empty(e)},header:function(e){return K.test(e.nodeName)},input:function(e){return X.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:pe((function(){return[0]})),last:pe((function(e,t){return[t-1]})),eq:pe((function(e,t,n){return[n<0?n+t:n]})),even:pe((function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e})),odd:pe((function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e})),lt:pe((function(e,t,n){for(var i=n<0?n+t:n;--i>=0;)e.push(i);return e})),gt:pe((function(e,t,n){for(var i=n<0?n+t:n;++i<t;)e.push(i);return e}))}},i.pseudos.nth=i.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})i.pseudos[t]=fe(t);for(t in{submit:!0,reset:!0})i.pseudos[t]=he(t);function me(){}function ve(e){for(var t=0,n=e.length,i="";t<n;t++)i+=e[t].value;return i}function ye(e,t,n){var i=t.dir,r=t.next,o=r||i,s=n&&"parentNode"===o,a=C++;return t.first?function(t,n,r){for(;t=t[i];)if(1===t.nodeType||s)return e(t,n,r);return!1}:function(t,n,l){var u,c,f,h=[x,a];if(l){for(;t=t[i];)if((1===t.nodeType||s)&&e(t,n,l))return!0}else for(;t=t[i];)if(1===t.nodeType||s)if(c=(f=t[w]||(t[w]={}))[t.uniqueID]||(f[t.uniqueID]={}),r&&r===t.nodeName.toLowerCase())t=t[i]||t;else{if((u=c[o])&&u[0]===x&&u[1]===a)return h[2]=u[2];if(c[o]=h,h[2]=e(t,n,l))return!0}return!1}}function be(e){return e.length>1?function(t,n,i){for(var r=e.length;r--;)if(!e[r](t,n,i))return!1;return!0}:e[0]}function we(e,t,n,i,r){for(var o,s=[],a=0,l=e.length,u=null!=t;a<l;a++)(o=e[a])&&(n&&!n(o,i,r)||(s.push(o),u&&t.push(a)));return s}function _e(e,t,n,i,r,o){return i&&!i[w]&&(i=_e(i)),r&&!r[w]&&(r=_e(r,o)),ae((function(o,s,a,l){var u,c,f,h=[],d=[],p=s.length,g=o||function(e,t,n){for(var i=0,r=t.length;i<r;i++)oe(e,t[i],n);return n}(t||"*",a.nodeType?[a]:a,[]),m=!e||!o&&t?g:we(g,h,e,a,l),v=n?r||(o?e:p||i)?[]:s:m;if(n&&n(m,v,a,l),i)for(u=we(v,d),i(u,[],a,l),c=u.length;c--;)(f=u[c])&&(v[d[c]]=!(m[d[c]]=f));if(o){if(r||e){if(r){for(u=[],c=v.length;c--;)(f=v[c])&&u.push(m[c]=f);r(null,v=[],u,l)}for(c=v.length;c--;)(f=v[c])&&(u=r?j(o,f):h[c])>-1&&(o[u]=!(s[u]=f))}}else v=we(v===s?v.splice(p,v.length):v),r?r(null,s,v,l):O.apply(s,v)}))}function xe(e){for(var t,n,r,o=e.length,s=i.relative[e[0].type],a=s||i.relative[" "],l=s?1:0,c=ye((function(e){return e===t}),a,!0),f=ye((function(e){return j(t,e)>-1}),a,!0),h=[function(e,n,i){var r=!s&&(i||n!==u)||((t=n).nodeType?c(e,n,i):f(e,n,i));return t=null,r}];l<o;l++)if(n=i.relative[e[l].type])h=[ye(be(h),n)];else{if((n=i.filter[e[l].type].apply(null,e[l].matches))[w]){for(r=++l;r<o&&!i.relative[e[r].type];r++);return _e(l>1&&be(h),l>1&&ve(e.slice(0,l-1).concat({value:" "===e[l-2].type?"*":""})).replace(W,"$1"),n,l<r&&xe(e.slice(l,r)),r<o&&xe(e=e.slice(r)),r<o&&ve(e))}h.push(n)}return be(h)}return me.prototype=i.filters=i.pseudos,i.setFilters=new me,s=oe.tokenize=function(e,t){var n,r,o,s,a,l,u,c=T[e+" "];if(c)return t?0:c.slice(0);for(a=e,l=[],u=i.preFilter;a;){for(s in n&&!(r=R.exec(a))||(r&&(a=a.slice(r[0].length)||a),l.push(o=[])),n=!1,(r=q.exec(a))&&(n=r.shift(),o.push({value:n,type:r[0].replace(W," ")}),a=a.slice(n.length)),i.filter)!(r=Q[s].exec(a))||u[s]&&!(r=u[s](r))||(n=r.shift(),o.push({value:n,type:s,matches:r}),a=a.slice(n.length));if(!n)break}return t?a.length:a?oe.error(e):T(e,l).slice(0)},a=oe.compile=function(e,t){var n,r=[],o=[],a=S[e+" "];if(!a){for(t||(t=s(e)),n=t.length;n--;)(a=xe(t[n]))[w]?r.push(a):o.push(a);a=S(e,function(e,t){var n=t.length>0,r=e.length>0,o=function(o,s,a,l,c){var f,p,m,v=0,y="0",b=o&&[],w=[],_=u,C=o||r&&i.find.TAG("*",c),E=x+=null==_?1:Math.random()||.1,T=C.length;for(c&&(u=s===d||s||c);y!==T&&null!=(f=C[y]);y++){if(r&&f){for(p=0,s||f.ownerDocument===d||(h(f),a=!g);m=e[p++];)if(m(f,s||d,a)){l.push(f);break}c&&(x=E)}n&&((f=!m&&f)&&v--,o&&b.push(f))}if(v+=y,n&&y!==v){for(p=0;m=t[p++];)m(b,w,s,a);if(o){if(v>0)for(;y--;)b[y]||w[y]||(w[y]=N.call(l));w=we(w)}O.apply(l,w),c&&!o&&w.length>0&&v+t.length>1&&oe.uniqueSort(l)}return c&&(x=E,u=_),b};return n?ae(o):o}(o,r)),a.selector=e}return a},l=oe.select=function(e,t,n,r){var o,l,u,c,f,h="function"==typeof e&&e,d=!r&&s(e=h.selector||e);if(n=n||[],1===d.length){if((l=d[0]=d[0].slice(0)).length>2&&"ID"===(u=l[0]).type&&9===t.nodeType&&g&&i.relative[l[1].type]){if(!(t=(i.find.ID(u.matches[0].replace(Z,ee),t)||[])[0]))return n;h&&(t=t.parentNode),e=e.slice(l.shift().value.length)}for(o=Q.needsContext.test(e)?0:l.length;o--&&(u=l[o],!i.relative[c=u.type]);)if((f=i.find[c])&&(r=f(u.matches[0].replace(Z,ee),J.test(l[0].type)&&ge(t.parentNode)||t))){if(l.splice(o,1),!(e=r.length&&ve(l)))return O.apply(n,r),n;break}}return(h||a(e,d))(r,t,!g,n,!t||J.test(e)&&ge(t.parentNode)||t),n},n.sortStable=w.split("").sort(k).join("")===w,n.detectDuplicates=!!f,h(),n.sortDetached=le((function(e){return 1&e.compareDocumentPosition(d.createElement("fieldset"))})),le((function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")}))||ue("type|href|height|width",(function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)})),n.attributes&&le((function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")}))||ue("value",(function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue})),le((function(e){return null==e.getAttribute("disabled")}))||ue(P,(function(e,t,n){var i;if(!n)return!0===e[t]?t.toLowerCase():(i=e.getAttributeNode(t))&&i.specified?i.value:null})),oe}(e);v.find=C,v.expr=C.selectors,v.expr[":"]=v.expr.pseudos,v.uniqueSort=v.unique=C.uniqueSort,v.text=C.getText,v.isXMLDoc=C.isXML,v.contains=C.contains,v.escapeSelector=C.escape;var E=function(e,t,n){for(var i=[],r=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(r&&v(e).is(n))break;i.push(e)}return i},T=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},S=v.expr.match.needsContext;function k(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var D=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i,A=/^.[^:#\[\.,]*$/;function N(e,t,n){return v.isFunction(t)?v.grep(e,(function(e,i){return!!t.call(e,i,e)!==n})):t.nodeType?v.grep(e,(function(e){return e===t!==n})):"string"!=typeof t?v.grep(e,(function(e){return l.call(t,e)>-1!==n})):A.test(t)?v.filter(t,e,n):(t=v.filter(t,e),v.grep(e,(function(e){return l.call(t,e)>-1!==n&&1===e.nodeType})))}v.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?v.find.matchesSelector(i,e)?[i]:[]:v.find.matches(e,v.grep(t,(function(e){return 1===e.nodeType})))},v.fn.extend({find:function(e){var t,n,i=this.length,r=this;if("string"!=typeof e)return this.pushStack(v(e).filter((function(){for(t=0;t<i;t++)if(v.contains(r[t],this))return!0})));for(n=this.pushStack([]),t=0;t<i;t++)v.find(e,r[t],n);return i>1?v.uniqueSort(n):n},filter:function(e){return this.pushStack(N(this,e||[],!1))},not:function(e){return this.pushStack(N(this,e||[],!0))},is:function(e){return!!N(this,"string"==typeof e&&S.test(e)?v(e):e||[],!1).length}});var L,O=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(v.fn.init=function(e,t,n){var r,o;if(!e)return this;if(n=n||L,"string"==typeof e){if(!(r="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:O.exec(e))||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof v?t[0]:t,v.merge(this,v.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:i,!0)),D.test(r[1])&&v.isPlainObject(t))for(r in t)v.isFunction(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return(o=i.getElementById(r[2]))&&(this[0]=o,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):v.isFunction(e)?void 0!==n.ready?n.ready(e):e(v):v.makeArray(e,this)}).prototype=v.fn,L=v(i);var I=/^(?:parents|prev(?:Until|All))/,j={children:!0,contents:!0,next:!0,prev:!0};function P(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}v.fn.extend({has:function(e){var t=v(e,this),n=t.length;return this.filter((function(){for(var e=0;e<n;e++)if(v.contains(this,t[e]))return!0}))},closest:function(e,t){var n,i=0,r=this.length,o=[],s="string"!=typeof e&&v(e);if(!S.test(e))for(;i<r;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(s?s.index(n)>-1:1===n.nodeType&&v.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?v.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?l.call(v(e),this[0]):l.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(v.uniqueSort(v.merge(this.get(),v(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),v.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return E(e,"parentNode")},parentsUntil:function(e,t,n){return E(e,"parentNode",n)},next:function(e){return P(e,"nextSibling")},prev:function(e){return P(e,"previousSibling")},nextAll:function(e){return E(e,"nextSibling")},prevAll:function(e){return E(e,"previousSibling")},nextUntil:function(e,t,n){return E(e,"nextSibling",n)},prevUntil:function(e,t,n){return E(e,"previousSibling",n)},siblings:function(e){return T((e.parentNode||{}).firstChild,e)},children:function(e){return T(e.firstChild)},contents:function(e){return k(e,"iframe")?e.contentDocument:(k(e,"template")&&(e=e.content||e),v.merge([],e.childNodes))}},(function(e,t){v.fn[e]=function(n,i){var r=v.map(this,t,n);return"Until"!==e.slice(-5)&&(i=n),i&&"string"==typeof i&&(r=v.filter(i,r)),this.length>1&&(j[e]||v.uniqueSort(r),I.test(e)&&r.reverse()),this.pushStack(r)}}));var H=/[^\x20\t\r\n\f]+/g;function $(e){return e}function M(e){throw e}function F(e,t,n,i){var r;try{e&&v.isFunction(r=e.promise)?r.call(e).done(t).fail(n):e&&v.isFunction(r=e.then)?r.call(e,t,n):t.apply(void 0,[e].slice(i))}catch(e){n.apply(void 0,[e])}}v.Callbacks=function(e){e="string"==typeof e?function(e){var t={};return v.each(e.match(H)||[],(function(e,n){t[n]=!0})),t}(e):v.extend({},e);var t,n,i,r,o=[],s=[],a=-1,l=function(){for(r=r||e.once,i=t=!0;s.length;a=-1)for(n=s.shift();++a<o.length;)!1===o[a].apply(n[0],n[1])&&e.stopOnFalse&&(a=o.length,n=!1);e.memory||(n=!1),t=!1,r&&(o=n?[]:"")},u={add:function(){return o&&(n&&!t&&(a=o.length-1,s.push(n)),function t(n){v.each(n,(function(n,i){v.isFunction(i)?e.unique&&u.has(i)||o.push(i):i&&i.length&&"string"!==v.type(i)&&t(i)}))}(arguments),n&&!t&&l()),this},remove:function(){return v.each(arguments,(function(e,t){for(var n;(n=v.inArray(t,o,n))>-1;)o.splice(n,1),n<=a&&a--})),this},has:function(e){return e?v.inArray(e,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return r=s=[],o=n="",this},disabled:function(){return!o},lock:function(){return r=s=[],n||t||(o=n=""),this},locked:function(){return!!r},fireWith:function(e,n){return r||(n=[e,(n=n||[]).slice?n.slice():n],s.push(n),t||l()),this},fire:function(){return u.fireWith(this,arguments),this},fired:function(){return!!i}};return u},v.extend({Deferred:function(t){var n=[["notify","progress",v.Callbacks("memory"),v.Callbacks("memory"),2],["resolve","done",v.Callbacks("once memory"),v.Callbacks("once memory"),0,"resolved"],["reject","fail",v.Callbacks("once memory"),v.Callbacks("once memory"),1,"rejected"]],i="pending",r={state:function(){return i},always:function(){return o.done(arguments).fail(arguments),this},catch:function(e){return r.then(null,e)},pipe:function(){var e=arguments;return v.Deferred((function(t){v.each(n,(function(n,i){var r=v.isFunction(e[i[4]])&&e[i[4]];o[i[1]]((function(){var e=r&&r.apply(this,arguments);e&&v.isFunction(e.promise)?e.promise().progress(t.notify).done(t.resolve).fail(t.reject):t[i[0]+"With"](this,r?[e]:arguments)}))})),e=null})).promise()},then:function(t,i,r){var o=0;function s(t,n,i,r){return function(){var a=this,l=arguments,u=function(){var e,u;if(!(t<o)){if((e=i.apply(a,l))===n.promise())throw new TypeError("Thenable self-resolution");u=e&&("object"==typeof e||"function"==typeof e)&&e.then,v.isFunction(u)?r?u.call(e,s(o,n,$,r),s(o,n,M,r)):(o++,u.call(e,s(o,n,$,r),s(o,n,M,r),s(o,n,$,n.notifyWith))):(i!==$&&(a=void 0,l=[e]),(r||n.resolveWith)(a,l))}},c=r?u:function(){try{u()}catch(e){v.Deferred.exceptionHook&&v.Deferred.exceptionHook(e,c.stackTrace),t+1>=o&&(i!==M&&(a=void 0,l=[e]),n.rejectWith(a,l))}};t?c():(v.Deferred.getStackHook&&(c.stackTrace=v.Deferred.getStackHook()),e.setTimeout(c))}}return v.Deferred((function(e){n[0][3].add(s(0,e,v.isFunction(r)?r:$,e.notifyWith)),n[1][3].add(s(0,e,v.isFunction(t)?t:$)),n[2][3].add(s(0,e,v.isFunction(i)?i:M))})).promise()},promise:function(e){return null!=e?v.extend(e,r):r}},o={};return v.each(n,(function(e,t){var s=t[2],a=t[5];r[t[1]]=s.add,a&&s.add((function(){i=a}),n[3-e][2].disable,n[0][2].lock),s.add(t[3].fire),o[t[0]]=function(){return o[t[0]+"With"](this===o?void 0:this,arguments),this},o[t[0]+"With"]=s.fireWith})),r.promise(o),t&&t.call(o,o),o},when:function(e){var t=arguments.length,n=t,i=Array(n),r=o.call(arguments),s=v.Deferred(),a=function(e){return function(n){i[e]=this,r[e]=arguments.length>1?o.call(arguments):n,--t||s.resolveWith(i,r)}};if(t<=1&&(F(e,s.done(a(n)).resolve,s.reject,!t),"pending"===s.state()||v.isFunction(r[n]&&r[n].then)))return s.then();for(;n--;)F(r[n],a(n),s.reject);return s.promise()}});var B=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;v.Deferred.exceptionHook=function(t,n){e.console&&e.console.warn&&t&&B.test(t.name)&&e.console.warn("jQuery.Deferred exception: "+t.message,t.stack,n)},v.readyException=function(t){e.setTimeout((function(){throw t}))};var W=v.Deferred();function R(){i.removeEventListener("DOMContentLoaded",R),e.removeEventListener("load",R),v.ready()}v.fn.ready=function(e){return W.then(e).catch((function(e){v.readyException(e)})),this},v.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--v.readyWait:v.isReady)||(v.isReady=!0,!0!==e&&--v.readyWait>0||W.resolveWith(i,[v]))}}),v.ready.then=W.then,"complete"===i.readyState||"loading"!==i.readyState&&!i.documentElement.doScroll?e.setTimeout(v.ready):(i.addEventListener("DOMContentLoaded",R),e.addEventListener("load",R));var q=function(e,t,n,i,r,o,s){var a=0,l=e.length,u=null==n;if("object"===v.type(n))for(a in r=!0,n)q(e,t,a,n[a],!0,o,s);else if(void 0!==i&&(r=!0,v.isFunction(i)||(s=!0),u&&(s?(t.call(e,i),t=null):(u=t,t=function(e,t,n){return u.call(v(e),n)})),t))for(;a<l;a++)t(e[a],n,s?i:i.call(e[a],a,t(e[a],n)));return r?e:u?t.call(e):l?t(e[0],n):o},U=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function z(){this.expando=v.expando+z.uid++}z.uid=1,z.prototype={cache:function(e){var t=e[this.expando];return t||(t={},U(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var i,r=this.cache(e);if("string"==typeof t)r[v.camelCase(t)]=n;else for(i in t)r[v.camelCase(i)]=t[i];return r},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][v.camelCase(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,i=e[this.expando];if(void 0!==i){if(void 0!==t){n=(t=Array.isArray(t)?t.map(v.camelCase):(t=v.camelCase(t))in i?[t]:t.match(H)||[]).length;for(;n--;)delete i[t[n]]}(void 0===t||v.isEmptyObject(i))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!v.isEmptyObject(t)}};var Y=new z,Q=new z,X=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,K=/[A-Z]/g;function V(e,t,n){var i;if(void 0===n&&1===e.nodeType)if(i="data-"+t.replace(K,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(i))){try{n=function(e){return"true"===e||"false"!==e&&("null"===e?null:e===+e+""?+e:X.test(e)?JSON.parse(e):e)}(n)}catch(e){}Q.set(e,t,n)}else n=void 0;return n}v.extend({hasData:function(e){return Q.hasData(e)||Y.hasData(e)},data:function(e,t,n){return Q.access(e,t,n)},removeData:function(e,t){Q.remove(e,t)},_data:function(e,t,n){return Y.access(e,t,n)},_removeData:function(e,t){Y.remove(e,t)}}),v.fn.extend({data:function(e,t){var n,i,r,o=this[0],s=o&&o.attributes;if(void 0===e){if(this.length&&(r=Q.get(o),1===o.nodeType&&!Y.get(o,"hasDataAttrs"))){for(n=s.length;n--;)s[n]&&0===(i=s[n].name).indexOf("data-")&&(i=v.camelCase(i.slice(5)),V(o,i,r[i]));Y.set(o,"hasDataAttrs",!0)}return r}return"object"==typeof e?this.each((function(){Q.set(this,e)})):q(this,(function(t){var n;if(o&&void 0===t)return void 0!==(n=Q.get(o,e))||void 0!==(n=V(o,e))?n:void 0;this.each((function(){Q.set(this,e,t)}))}),null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each((function(){Q.remove(this,e)}))}}),v.extend({queue:function(e,t,n){var i;if(e)return t=(t||"fx")+"queue",i=Y.get(e,t),n&&(!i||Array.isArray(n)?i=Y.access(e,t,v.makeArray(n)):i.push(n)),i||[]},dequeue:function(e,t){t=t||"fx";var n=v.queue(e,t),i=n.length,r=n.shift(),o=v._queueHooks(e,t);"inprogress"===r&&(r=n.shift(),i--),r&&("fx"===t&&n.unshift("inprogress"),delete o.stop,r.call(e,(function(){v.dequeue(e,t)}),o)),!i&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return Y.get(e,n)||Y.access(e,n,{empty:v.Callbacks("once memory").add((function(){Y.remove(e,[t+"queue",n])}))})}}),v.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?v.queue(this[0],e):void 0===t?this:this.each((function(){var n=v.queue(this,e,t);v._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&v.dequeue(this,e)}))},dequeue:function(e){return this.each((function(){v.dequeue(this,e)}))},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,i=1,r=v.Deferred(),o=this,s=this.length,a=function(){--i||r.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(n=Y.get(o[s],e+"queueHooks"))&&n.empty&&(i++,n.empty.add(a));return a(),r.promise(t)}});var G=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,J=new RegExp("^(?:([+-])=|)("+G+")([a-z%]*)$","i"),Z=["Top","Right","Bottom","Left"],ee=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&v.contains(e.ownerDocument,e)&&"none"===v.css(e,"display")},te=function(e,t,n,i){var r,o,s={};for(o in t)s[o]=e.style[o],e.style[o]=t[o];for(o in r=n.apply(e,i||[]),t)e.style[o]=s[o];return r};function ne(e,t,n,i){var r,o=1,s=20,a=i?function(){return i.cur()}:function(){return v.css(e,t,"")},l=a(),u=n&&n[3]||(v.cssNumber[t]?"":"px"),c=(v.cssNumber[t]||"px"!==u&&+l)&&J.exec(v.css(e,t));if(c&&c[3]!==u){u=u||c[3],n=n||[],c=+l||1;do{c/=o=o||".5",v.style(e,t,c+u)}while(o!==(o=a()/l)&&1!==o&&--s)}return n&&(c=+c||+l||0,r=n[1]?c+(n[1]+1)*n[2]:+n[2],i&&(i.unit=u,i.start=c,i.end=r)),r}var ie={};function re(e){var t,n=e.ownerDocument,i=e.nodeName,r=ie[i];return r||(t=n.body.appendChild(n.createElement(i)),r=v.css(t,"display"),t.parentNode.removeChild(t),"none"===r&&(r="block"),ie[i]=r,r)}function oe(e,t){for(var n,i,r=[],o=0,s=e.length;o<s;o++)(i=e[o]).style&&(n=i.style.display,t?("none"===n&&(r[o]=Y.get(i,"display")||null,r[o]||(i.style.display="")),""===i.style.display&&ee(i)&&(r[o]=re(i))):"none"!==n&&(r[o]="none",Y.set(i,"display",n)));for(o=0;o<s;o++)null!=r[o]&&(e[o].style.display=r[o]);return e}v.fn.extend({show:function(){return oe(this,!0)},hide:function(){return oe(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each((function(){ee(this)?v(this).show():v(this).hide()}))}});var se=/^(?:checkbox|radio)$/i,ae=/<([a-z][^\/\0>\x20\t\r\n\f]+)/i,le=/^$|\/(?:java|ecma)script/i,ue={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function ce(e,t){var n;return n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&k(e,t)?v.merge([e],n):n}function fe(e,t){for(var n=0,i=e.length;n<i;n++)Y.set(e[n],"globalEval",!t||Y.get(t[n],"globalEval"))}ue.optgroup=ue.option,ue.tbody=ue.tfoot=ue.colgroup=ue.caption=ue.thead,ue.th=ue.td;var he,de,pe=/<|&#?\w+;/;function ge(e,t,n,i,r){for(var o,s,a,l,u,c,f=t.createDocumentFragment(),h=[],d=0,p=e.length;d<p;d++)if((o=e[d])||0===o)if("object"===v.type(o))v.merge(h,o.nodeType?[o]:o);else if(pe.test(o)){for(s=s||f.appendChild(t.createElement("div")),a=(ae.exec(o)||["",""])[1].toLowerCase(),l=ue[a]||ue._default,s.innerHTML=l[1]+v.htmlPrefilter(o)+l[2],c=l[0];c--;)s=s.lastChild;v.merge(h,s.childNodes),(s=f.firstChild).textContent=""}else h.push(t.createTextNode(o));for(f.textContent="",d=0;o=h[d++];)if(i&&v.inArray(o,i)>-1)r&&r.push(o);else if(u=v.contains(o.ownerDocument,o),s=ce(f.appendChild(o),"script"),u&&fe(s),n)for(c=0;o=s[c++];)le.test(o.type||"")&&n.push(o);return f}he=i.createDocumentFragment().appendChild(i.createElement("div")),(de=i.createElement("input")).setAttribute("type","radio"),de.setAttribute("checked","checked"),de.setAttribute("name","t"),he.appendChild(de),p.checkClone=he.cloneNode(!0).cloneNode(!0).lastChild.checked,he.innerHTML="<textarea>x</textarea>",p.noCloneChecked=!!he.cloneNode(!0).lastChild.defaultValue;var me=i.documentElement,ve=/^key/,ye=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,be=/^([^.]*)(?:\.(.+)|)/;function we(){return!0}function _e(){return!1}function xe(){try{return i.activeElement}catch(e){}}function Ce(e,t,n,i,r,o){var s,a;if("object"==typeof t){for(a in"string"!=typeof n&&(i=i||n,n=void 0),t)Ce(e,a,n,i,t[a],o);return e}if(null==i&&null==r?(r=n,i=n=void 0):null==r&&("string"==typeof n?(r=i,i=void 0):(r=i,i=n,n=void 0)),!1===r)r=_e;else if(!r)return e;return 1===o&&(s=r,r=function(e){return v().off(e),s.apply(this,arguments)},r.guid=s.guid||(s.guid=v.guid++)),e.each((function(){v.event.add(this,t,r,i,n)}))}v.event={global:{},add:function(e,t,n,i,r){var o,s,a,l,u,c,f,h,d,p,g,m=Y.get(e);if(m)for(n.handler&&(n=(o=n).handler,r=o.selector),r&&v.find.matchesSelector(me,r),n.guid||(n.guid=v.guid++),(l=m.events)||(l=m.events={}),(s=m.handle)||(s=m.handle=function(t){return void 0!==v&&v.event.triggered!==t.type?v.event.dispatch.apply(e,arguments):void 0}),u=(t=(t||"").match(H)||[""]).length;u--;)d=g=(a=be.exec(t[u])||[])[1],p=(a[2]||"").split(".").sort(),d&&(f=v.event.special[d]||{},d=(r?f.delegateType:f.bindType)||d,f=v.event.special[d]||{},c=v.extend({type:d,origType:g,data:i,handler:n,guid:n.guid,selector:r,needsContext:r&&v.expr.match.needsContext.test(r),namespace:p.join(".")},o),(h=l[d])||((h=l[d]=[]).delegateCount=0,f.setup&&!1!==f.setup.call(e,i,p,s)||e.addEventListener&&e.addEventListener(d,s)),f.add&&(f.add.call(e,c),c.handler.guid||(c.handler.guid=n.guid)),r?h.splice(h.delegateCount++,0,c):h.push(c),v.event.global[d]=!0)},remove:function(e,t,n,i,r){var o,s,a,l,u,c,f,h,d,p,g,m=Y.hasData(e)&&Y.get(e);if(m&&(l=m.events)){for(u=(t=(t||"").match(H)||[""]).length;u--;)if(d=g=(a=be.exec(t[u])||[])[1],p=(a[2]||"").split(".").sort(),d){for(f=v.event.special[d]||{},h=l[d=(i?f.delegateType:f.bindType)||d]||[],a=a[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=h.length;o--;)c=h[o],!r&&g!==c.origType||n&&n.guid!==c.guid||a&&!a.test(c.namespace)||i&&i!==c.selector&&("**"!==i||!c.selector)||(h.splice(o,1),c.selector&&h.delegateCount--,f.remove&&f.remove.call(e,c));s&&!h.length&&(f.teardown&&!1!==f.teardown.call(e,p,m.handle)||v.removeEvent(e,d,m.handle),delete l[d])}else for(d in l)v.event.remove(e,d+t[u],n,i,!0);v.isEmptyObject(l)&&Y.remove(e,"handle events")}},dispatch:function(e){var t,n,i,r,o,s,a=v.event.fix(e),l=new Array(arguments.length),u=(Y.get(this,"events")||{})[a.type]||[],c=v.event.special[a.type]||{};for(l[0]=a,t=1;t<arguments.length;t++)l[t]=arguments[t];if(a.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,a)){for(s=v.event.handlers.call(this,a,u),t=0;(r=s[t++])&&!a.isPropagationStopped();)for(a.currentTarget=r.elem,n=0;(o=r.handlers[n++])&&!a.isImmediatePropagationStopped();)a.rnamespace&&!a.rnamespace.test(o.namespace)||(a.handleObj=o,a.data=o.data,void 0!==(i=((v.event.special[o.origType]||{}).handle||o.handler).apply(r.elem,l))&&!1===(a.result=i)&&(a.preventDefault(),a.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,a),a.result}},handlers:function(e,t){var n,i,r,o,s,a=[],l=t.delegateCount,u=e.target;if(l&&u.nodeType&&!("click"===e.type&&e.button>=1))for(;u!==this;u=u.parentNode||this)if(1===u.nodeType&&("click"!==e.type||!0!==u.disabled)){for(o=[],s={},n=0;n<l;n++)void 0===s[r=(i=t[n]).selector+" "]&&(s[r]=i.needsContext?v(r,this).index(u)>-1:v.find(r,this,null,[u]).length),s[r]&&o.push(i);o.length&&a.push({elem:u,handlers:o})}return u=this,l<t.length&&a.push({elem:u,handlers:t.slice(l)}),a},addProp:function(e,t){Object.defineProperty(v.Event.prototype,e,{enumerable:!0,configurable:!0,get:v.isFunction(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[v.expando]?e:new v.Event(e)},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==xe()&&this.focus)return this.focus(),!1},delegateType:"focusin"},blur:{trigger:function(){if(this===xe()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if("checkbox"===this.type&&this.click&&k(this,"input"))return this.click(),!1},_default:function(e){return k(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},v.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},v.Event=function(e,t){if(!(this instanceof v.Event))return new v.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?we:_e,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&v.extend(this,t),this.timeStamp=e&&e.timeStamp||v.now(),this[v.expando]=!0},v.Event.prototype={constructor:v.Event,isDefaultPrevented:_e,isPropagationStopped:_e,isImmediatePropagationStopped:_e,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=we,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=we,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=we,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},v.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(e){var t=e.button;return null==e.which&&ve.test(e.type)?null!=e.charCode?e.charCode:e.keyCode:!e.which&&void 0!==t&&ye.test(e.type)?1&t?1:2&t?3:4&t?2:0:e.which}},v.event.addProp),v.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(e,t){v.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,i=e.relatedTarget,r=e.handleObj;return i&&(i===this||v.contains(this,i))||(e.type=r.origType,n=r.handler.apply(this,arguments),e.type=t),n}}})),v.fn.extend({on:function(e,t,n,i){return Ce(this,e,t,n,i)},one:function(e,t,n,i){return Ce(this,e,t,n,i,1)},off:function(e,t,n){var i,r;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,v(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof e){for(r in e)this.off(r,t,e[r]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=_e),this.each((function(){v.event.remove(this,e,n,t)}))}});var Ee=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi,Te=/<script|<style|<link/i,Se=/checked\s*(?:[^=]|=\s*.checked.)/i,ke=/^true\/(.*)/,De=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function Ae(e,t){return k(e,"table")&&k(11!==t.nodeType?t:t.firstChild,"tr")&&v(">tbody",e)[0]||e}function Ne(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Le(e){var t=ke.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function Oe(e,t){var n,i,r,o,s,a,l,u;if(1===t.nodeType){if(Y.hasData(e)&&(o=Y.access(e),s=Y.set(t,o),u=o.events))for(r in delete s.handle,s.events={},u)for(n=0,i=u[r].length;n<i;n++)v.event.add(t,r,u[r][n]);Q.hasData(e)&&(a=Q.access(e),l=v.extend({},a),Q.set(t,l))}}function Ie(e,t){var n=t.nodeName.toLowerCase();"input"===n&&se.test(e.type)?t.checked=e.checked:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}function je(e,t,n,i){t=s.apply([],t);var r,o,a,l,u,c,f=0,h=e.length,d=h-1,m=t[0],y=v.isFunction(m);if(y||h>1&&"string"==typeof m&&!p.checkClone&&Se.test(m))return e.each((function(r){var o=e.eq(r);y&&(t[0]=m.call(this,r,o.html())),je(o,t,n,i)}));if(h&&(o=(r=ge(t,e[0].ownerDocument,!1,e,i)).firstChild,1===r.childNodes.length&&(r=o),o||i)){for(l=(a=v.map(ce(r,"script"),Ne)).length;f<h;f++)u=r,f!==d&&(u=v.clone(u,!0,!0),l&&v.merge(a,ce(u,"script"))),n.call(e[f],u,f);if(l)for(c=a[a.length-1].ownerDocument,v.map(a,Le),f=0;f<l;f++)u=a[f],le.test(u.type||"")&&!Y.access(u,"globalEval")&&v.contains(c,u)&&(u.src?v._evalUrl&&v._evalUrl(u.src):g(u.textContent.replace(De,""),c))}return e}function Pe(e,t,n){for(var i,r=t?v.filter(t,e):e,o=0;null!=(i=r[o]);o++)n||1!==i.nodeType||v.cleanData(ce(i)),i.parentNode&&(n&&v.contains(i.ownerDocument,i)&&fe(ce(i,"script")),i.parentNode.removeChild(i));return e}v.extend({htmlPrefilter:function(e){return e.replace(Ee,"<$1></$2>")},clone:function(e,t,n){var i,r,o,s,a=e.cloneNode(!0),l=v.contains(e.ownerDocument,e);if(!(p.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||v.isXMLDoc(e)))for(s=ce(a),i=0,r=(o=ce(e)).length;i<r;i++)Ie(o[i],s[i]);if(t)if(n)for(o=o||ce(e),s=s||ce(a),i=0,r=o.length;i<r;i++)Oe(o[i],s[i]);else Oe(e,a);return(s=ce(a,"script")).length>0&&fe(s,!l&&ce(e,"script")),a},cleanData:function(e){for(var t,n,i,r=v.event.special,o=0;void 0!==(n=e[o]);o++)if(U(n)){if(t=n[Y.expando]){if(t.events)for(i in t.events)r[i]?v.event.remove(n,i):v.removeEvent(n,i,t.handle);n[Y.expando]=void 0}n[Q.expando]&&(n[Q.expando]=void 0)}}}),v.fn.extend({detach:function(e){return Pe(this,e,!0)},remove:function(e){return Pe(this,e)},text:function(e){return q(this,(function(e){return void 0===e?v.text(this):this.empty().each((function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)}))}),null,e,arguments.length)},append:function(){return je(this,arguments,(function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Ae(this,e).appendChild(e)}))},prepend:function(){return je(this,arguments,(function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=Ae(this,e);t.insertBefore(e,t.firstChild)}}))},before:function(){return je(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this)}))},after:function(){return je(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)}))},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(v.cleanData(ce(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map((function(){return v.clone(this,e,t)}))},html:function(e){return q(this,(function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Te.test(e)&&!ue[(ae.exec(e)||["",""])[1].toLowerCase()]){e=v.htmlPrefilter(e);try{for(;n<i;n++)1===(t=this[n]||{}).nodeType&&(v.cleanData(ce(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)}),null,e,arguments.length)},replaceWith:function(){var e=[];return je(this,arguments,(function(t){var n=this.parentNode;v.inArray(this,e)<0&&(v.cleanData(ce(this)),n&&n.replaceChild(t,this))}),e)}}),v.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(e,t){v.fn[e]=function(e){for(var n,i=[],r=v(e),o=r.length-1,s=0;s<=o;s++)n=s===o?this:this.clone(!0),v(r[s])[t](n),a.apply(i,n.get());return this.pushStack(i)}}));var He=/^margin/,$e=new RegExp("^("+G+")(?!px)[a-z%]+$","i"),Me=function(t){var n=t.ownerDocument.defaultView;return n&&n.opener||(n=e),n.getComputedStyle(t)};function Fe(e,t,n){var i,r,o,s,a=e.style;return(n=n||Me(e))&&(""!==(s=n.getPropertyValue(t)||n[t])||v.contains(e.ownerDocument,e)||(s=v.style(e,t)),!p.pixelMarginRight()&&$e.test(s)&&He.test(t)&&(i=a.width,r=a.minWidth,o=a.maxWidth,a.minWidth=a.maxWidth=a.width=s,s=n.width,a.width=i,a.minWidth=r,a.maxWidth=o)),void 0!==s?s+"":s}function Be(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function t(){if(l){l.style.cssText="box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%",l.innerHTML="",me.appendChild(a);var t=e.getComputedStyle(l);n="1%"!==t.top,s="2px"===t.marginLeft,r="4px"===t.width,l.style.marginRight="50%",o="4px"===t.marginRight,me.removeChild(a),l=null}}var n,r,o,s,a=i.createElement("div"),l=i.createElement("div");l.style&&(l.style.backgroundClip="content-box",l.cloneNode(!0).style.backgroundClip="",p.clearCloneStyle="content-box"===l.style.backgroundClip,a.style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",a.appendChild(l),v.extend(p,{pixelPosition:function(){return t(),n},boxSizingReliable:function(){return t(),r},pixelMarginRight:function(){return t(),o},reliableMarginLeft:function(){return t(),s}}))}();var We=/^(none|table(?!-c[ea]).+)/,Re=/^--/,qe={position:"absolute",visibility:"hidden",display:"block"},Ue={letterSpacing:"0",fontWeight:"400"},ze=["Webkit","Moz","ms"],Ye=i.createElement("div").style;function Qe(e){var t=v.cssProps[e];return t||(t=v.cssProps[e]=function(e){if(e in Ye)return e;for(var t=e[0].toUpperCase()+e.slice(1),n=ze.length;n--;)if((e=ze[n]+t)in Ye)return e}(e)||e),t}function Xe(e,t,n){var i=J.exec(t);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):t}function Ke(e,t,n,i,r){var o,s=0;for(o=n===(i?"border":"content")?4:"width"===t?1:0;o<4;o+=2)"margin"===n&&(s+=v.css(e,n+Z[o],!0,r)),i?("content"===n&&(s-=v.css(e,"padding"+Z[o],!0,r)),"margin"!==n&&(s-=v.css(e,"border"+Z[o]+"Width",!0,r))):(s+=v.css(e,"padding"+Z[o],!0,r),"padding"!==n&&(s+=v.css(e,"border"+Z[o]+"Width",!0,r)));return s}function Ve(e,t,n){var i,r=Me(e),o=Fe(e,t,r),s="border-box"===v.css(e,"boxSizing",!1,r);return $e.test(o)?o:(i=s&&(p.boxSizingReliable()||o===e.style[t]),"auto"===o&&(o=e["offset"+t[0].toUpperCase()+t.slice(1)]),(o=parseFloat(o)||0)+Ke(e,t,n||(s?"border":"content"),i,r)+"px")}function Ge(e,t,n,i,r){return new Ge.prototype.init(e,t,n,i,r)}v.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Fe(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:"cssFloat"},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var r,o,s,a=v.camelCase(t),l=Re.test(t),u=e.style;if(l||(t=Qe(a)),s=v.cssHooks[t]||v.cssHooks[a],void 0===n)return s&&"get"in s&&void 0!==(r=s.get(e,!1,i))?r:u[t];"string"===(o=typeof n)&&(r=J.exec(n))&&r[1]&&(n=ne(e,t,r),o="number"),null!=n&&n==n&&("number"===o&&(n+=r&&r[3]||(v.cssNumber[a]?"":"px")),p.clearCloneStyle||""!==n||0!==t.indexOf("background")||(u[t]="inherit"),s&&"set"in s&&void 0===(n=s.set(e,n,i))||(l?u.setProperty(t,n):u[t]=n))}},css:function(e,t,n,i){var r,o,s,a=v.camelCase(t);return Re.test(t)||(t=Qe(a)),(s=v.cssHooks[t]||v.cssHooks[a])&&"get"in s&&(r=s.get(e,!0,n)),void 0===r&&(r=Fe(e,t,i)),"normal"===r&&t in Ue&&(r=Ue[t]),""===n||n?(o=parseFloat(r),!0===n||isFinite(o)?o||0:r):r}}),v.each(["height","width"],(function(e,t){v.cssHooks[t]={get:function(e,n,i){if(n)return!We.test(v.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?Ve(e,t,i):te(e,qe,(function(){return Ve(e,t,i)}))},set:function(e,n,i){var r,o=i&&Me(e),s=i&&Ke(e,t,i,"border-box"===v.css(e,"boxSizing",!1,o),o);return s&&(r=J.exec(n))&&"px"!==(r[3]||"px")&&(e.style[t]=n,n=v.css(e,t)),Xe(0,n,s)}}})),v.cssHooks.marginLeft=Be(p.reliableMarginLeft,(function(e,t){if(t)return(parseFloat(Fe(e,"marginLeft"))||e.getBoundingClientRect().left-te(e,{marginLeft:0},(function(){return e.getBoundingClientRect().left})))+"px"})),v.each({margin:"",padding:"",border:"Width"},(function(e,t){v.cssHooks[e+t]={expand:function(n){for(var i=0,r={},o="string"==typeof n?n.split(" "):[n];i<4;i++)r[e+Z[i]+t]=o[i]||o[i-2]||o[0];return r}},He.test(e)||(v.cssHooks[e+t].set=Xe)})),v.fn.extend({css:function(e,t){return q(this,(function(e,t,n){var i,r,o={},s=0;if(Array.isArray(t)){for(i=Me(e),r=t.length;s<r;s++)o[t[s]]=v.css(e,t[s],!1,i);return o}return void 0!==n?v.style(e,t,n):v.css(e,t)}),e,t,arguments.length>1)}}),v.Tween=Ge,Ge.prototype={constructor:Ge,init:function(e,t,n,i,r,o){this.elem=e,this.prop=n,this.easing=r||v.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=o||(v.cssNumber[n]?"":"px")},cur:function(){var e=Ge.propHooks[this.prop];return e&&e.get?e.get(this):Ge.propHooks._default.get(this)},run:function(e){var t,n=Ge.propHooks[this.prop];return this.options.duration?this.pos=t=v.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):Ge.propHooks._default.set(this),this}},Ge.prototype.init.prototype=Ge.prototype,Ge.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=v.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){v.fx.step[e.prop]?v.fx.step[e.prop](e):1!==e.elem.nodeType||null==e.elem.style[v.cssProps[e.prop]]&&!v.cssHooks[e.prop]?e.elem[e.prop]=e.now:v.style(e.elem,e.prop,e.now+e.unit)}}},Ge.propHooks.scrollTop=Ge.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},v.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},v.fx=Ge.prototype.init,v.fx.step={};var Je,Ze,et=/^(?:toggle|show|hide)$/,tt=/queueHooks$/;function nt(){Ze&&(!1===i.hidden&&e.requestAnimationFrame?e.requestAnimationFrame(nt):e.setTimeout(nt,v.fx.interval),v.fx.tick())}function it(){return e.setTimeout((function(){Je=void 0})),Je=v.now()}function rt(e,t){var n,i=0,r={height:e};for(t=t?1:0;i<4;i+=2-t)r["margin"+(n=Z[i])]=r["padding"+n]=e;return t&&(r.opacity=r.width=e),r}function ot(e,t,n){for(var i,r=(st.tweeners[t]||[]).concat(st.tweeners["*"]),o=0,s=r.length;o<s;o++)if(i=r[o].call(n,t,e))return i}function st(e,t,n){var i,r,o=0,s=st.prefilters.length,a=v.Deferred().always((function(){delete l.elem})),l=function(){if(r)return!1;for(var t=Je||it(),n=Math.max(0,u.startTime+u.duration-t),i=1-(n/u.duration||0),o=0,s=u.tweens.length;o<s;o++)u.tweens[o].run(i);return a.notifyWith(e,[u,i,n]),i<1&&s?n:(s||a.notifyWith(e,[u,1,0]),a.resolveWith(e,[u]),!1)},u=a.promise({elem:e,props:v.extend({},t),opts:v.extend(!0,{specialEasing:{},easing:v.easing._default},n),originalProperties:t,originalOptions:n,startTime:Je||it(),duration:n.duration,tweens:[],createTween:function(t,n){var i=v.Tween(e,u.opts,t,n,u.opts.specialEasing[t]||u.opts.easing);return u.tweens.push(i),i},stop:function(t){var n=0,i=t?u.tweens.length:0;if(r)return this;for(r=!0;n<i;n++)u.tweens[n].run(1);return t?(a.notifyWith(e,[u,1,0]),a.resolveWith(e,[u,t])):a.rejectWith(e,[u,t]),this}}),c=u.props;for(!function(e,t){var n,i,r,o,s;for(n in e)if(r=t[i=v.camelCase(n)],o=e[n],Array.isArray(o)&&(r=o[1],o=e[n]=o[0]),n!==i&&(e[i]=o,delete e[n]),(s=v.cssHooks[i])&&"expand"in s)for(n in o=s.expand(o),delete e[i],o)n in e||(e[n]=o[n],t[n]=r);else t[i]=r}(c,u.opts.specialEasing);o<s;o++)if(i=st.prefilters[o].call(u,e,c,u.opts))return v.isFunction(i.stop)&&(v._queueHooks(u.elem,u.opts.queue).stop=v.proxy(i.stop,i)),i;return v.map(c,ot,u),v.isFunction(u.opts.start)&&u.opts.start.call(e,u),u.progress(u.opts.progress).done(u.opts.done,u.opts.complete).fail(u.opts.fail).always(u.opts.always),v.fx.timer(v.extend(l,{elem:e,anim:u,queue:u.opts.queue})),u}v.Animation=v.extend(st,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return ne(n.elem,e,J.exec(t),n),n}]},tweener:function(e,t){v.isFunction(e)?(t=e,e=["*"]):e=e.match(H);for(var n,i=0,r=e.length;i<r;i++)n=e[i],st.tweeners[n]=st.tweeners[n]||[],st.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var i,r,o,s,a,l,u,c,f="width"in t||"height"in t,h=this,d={},p=e.style,g=e.nodeType&&ee(e),m=Y.get(e,"fxshow");for(i in n.queue||(null==(s=v._queueHooks(e,"fx")).unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,h.always((function(){h.always((function(){s.unqueued--,v.queue(e,"fx").length||s.empty.fire()}))}))),t)if(r=t[i],et.test(r)){if(delete t[i],o=o||"toggle"===r,r===(g?"hide":"show")){if("show"!==r||!m||void 0===m[i])continue;g=!0}d[i]=m&&m[i]||v.style(e,i)}if((l=!v.isEmptyObject(t))||!v.isEmptyObject(d))for(i in f&&1===e.nodeType&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],null==(u=m&&m.display)&&(u=Y.get(e,"display")),"none"===(c=v.css(e,"display"))&&(u?c=u:(oe([e],!0),u=e.style.display||u,c=v.css(e,"display"),oe([e]))),("inline"===c||"inline-block"===c&&null!=u)&&"none"===v.css(e,"float")&&(l||(h.done((function(){p.display=u})),null==u&&(c=p.display,u="none"===c?"":c)),p.display="inline-block")),n.overflow&&(p.overflow="hidden",h.always((function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]}))),l=!1,d)l||(m?"hidden"in m&&(g=m.hidden):m=Y.access(e,"fxshow",{display:u}),o&&(m.hidden=!g),g&&oe([e],!0),h.done((function(){for(i in g||oe([e]),Y.remove(e,"fxshow"),d)v.style(e,i,d[i])}))),l=ot(g?m[i]:0,i,h),i in m||(m[i]=l.start,g&&(l.end=l.start,l.start=0))}],prefilter:function(e,t){t?st.prefilters.unshift(e):st.prefilters.push(e)}}),v.speed=function(e,t,n){var i=e&&"object"==typeof e?v.extend({},e):{complete:n||!n&&t||v.isFunction(e)&&e,duration:e,easing:n&&t||t&&!v.isFunction(t)&&t};return v.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration in v.fx.speeds?i.duration=v.fx.speeds[i.duration]:i.duration=v.fx.speeds._default),null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){v.isFunction(i.old)&&i.old.call(this),i.queue&&v.dequeue(this,i.queue)},i},v.fn.extend({fadeTo:function(e,t,n,i){return this.filter(ee).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(e,t,n,i){var r=v.isEmptyObject(e),o=v.speed(t,n,i),s=function(){var t=st(this,v.extend({},e),o);(r||Y.get(this,"finish"))&&t.stop(!0)};return s.finish=s,r||!1===o.queue?this.each(s):this.queue(o.queue,s)},stop:function(e,t,n){var i=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&!1!==e&&this.queue(e||"fx",[]),this.each((function(){var t=!0,r=null!=e&&e+"queueHooks",o=v.timers,s=Y.get(this);if(r)s[r]&&s[r].stop&&i(s[r]);else for(r in s)s[r]&&s[r].stop&&tt.test(r)&&i(s[r]);for(r=o.length;r--;)o[r].elem!==this||null!=e&&o[r].queue!==e||(o[r].anim.stop(n),t=!1,o.splice(r,1));!t&&n||v.dequeue(this,e)}))},finish:function(e){return!1!==e&&(e=e||"fx"),this.each((function(){var t,n=Y.get(this),i=n[e+"queue"],r=n[e+"queueHooks"],o=v.timers,s=i?i.length:0;for(n.finish=!0,v.queue(this,e,[]),r&&r.stop&&r.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<s;t++)i[t]&&i[t].finish&&i[t].finish.call(this);delete n.finish}))}}),v.each(["toggle","show","hide"],(function(e,t){var n=v.fn[t];v.fn[t]=function(e,i,r){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(rt(t,!0),e,i,r)}})),v.each({slideDown:rt("show"),slideUp:rt("hide"),slideToggle:rt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(e,t){v.fn[e]=function(e,n,i){return this.animate(t,e,n,i)}})),v.timers=[],v.fx.tick=function(){var e,t=0,n=v.timers;for(Je=v.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||v.fx.stop(),Je=void 0},v.fx.timer=function(e){v.timers.push(e),v.fx.start()},v.fx.interval=13,v.fx.start=function(){Ze||(Ze=!0,nt())},v.fx.stop=function(){Ze=null},v.fx.speeds={slow:600,fast:200,_default:400},v.fn.delay=function(t,n){return t=v.fx&&v.fx.speeds[t]||t,n=n||"fx",this.queue(n,(function(n,i){var r=e.setTimeout(n,t);i.stop=function(){e.clearTimeout(r)}}))},function(){var e=i.createElement("input"),t=i.createElement("select").appendChild(i.createElement("option"));e.type="checkbox",p.checkOn=""!==e.value,p.optSelected=t.selected,(e=i.createElement("input")).value="t",e.type="radio",p.radioValue="t"===e.value}();var at,lt=v.expr.attrHandle;v.fn.extend({attr:function(e,t){return q(this,v.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each((function(){v.removeAttr(this,e)}))}}),v.extend({attr:function(e,t,n){var i,r,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?v.prop(e,t,n):(1===o&&v.isXMLDoc(e)||(r=v.attrHooks[t.toLowerCase()]||(v.expr.match.bool.test(t)?at:void 0)),void 0!==n?null===n?void v.removeAttr(e,t):r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:(e.setAttribute(t,n+""),n):r&&"get"in r&&null!==(i=r.get(e,t))?i:null==(i=v.find.attr(e,t))?void 0:i)},attrHooks:{type:{set:function(e,t){if(!p.radioValue&&"radio"===t&&k(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,i=0,r=t&&t.match(H);if(r&&1===e.nodeType)for(;n=r[i++];)e.removeAttribute(n)}}),at={set:function(e,t,n){return!1===t?v.removeAttr(e,n):e.setAttribute(n,n),n}},v.each(v.expr.match.bool.source.match(/\w+/g),(function(e,t){var n=lt[t]||v.find.attr;lt[t]=function(e,t,i){var r,o,s=t.toLowerCase();return i||(o=lt[s],lt[s]=r,r=null!=n(e,t,i)?s:null,lt[s]=o),r}}));var ut=/^(?:input|select|textarea|button)$/i,ct=/^(?:a|area)$/i;function ft(e){return(e.match(H)||[]).join(" ")}function ht(e){return e.getAttribute&&e.getAttribute("class")||""}v.fn.extend({prop:function(e,t){return q(this,v.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each((function(){delete this[v.propFix[e]||e]}))}}),v.extend({prop:function(e,t,n){var i,r,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&v.isXMLDoc(e)||(t=v.propFix[t]||t,r=v.propHooks[t]),void 0!==n?r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:e[t]=n:r&&"get"in r&&null!==(i=r.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=v.find.attr(e,"tabindex");return t?parseInt(t,10):ut.test(e.nodeName)||ct.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),p.optSelected||(v.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),v.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){v.propFix[this.toLowerCase()]=this})),v.fn.extend({addClass:function(e){var t,n,i,r,o,s,a,l=0;if(v.isFunction(e))return this.each((function(t){v(this).addClass(e.call(this,t,ht(this)))}));if("string"==typeof e&&e)for(t=e.match(H)||[];n=this[l++];)if(r=ht(n),i=1===n.nodeType&&" "+ft(r)+" "){for(s=0;o=t[s++];)i.indexOf(" "+o+" ")<0&&(i+=o+" ");r!==(a=ft(i))&&n.setAttribute("class",a)}return this},removeClass:function(e){var t,n,i,r,o,s,a,l=0;if(v.isFunction(e))return this.each((function(t){v(this).removeClass(e.call(this,t,ht(this)))}));if(!arguments.length)return this.attr("class","");if("string"==typeof e&&e)for(t=e.match(H)||[];n=this[l++];)if(r=ht(n),i=1===n.nodeType&&" "+ft(r)+" "){for(s=0;o=t[s++];)for(;i.indexOf(" "+o+" ")>-1;)i=i.replace(" "+o+" "," ");r!==(a=ft(i))&&n.setAttribute("class",a)}return this},toggleClass:function(e,t){var n=typeof e;return"boolean"==typeof t&&"string"===n?t?this.addClass(e):this.removeClass(e):v.isFunction(e)?this.each((function(n){v(this).toggleClass(e.call(this,n,ht(this),t),t)})):this.each((function(){var t,i,r,o;if("string"===n)for(i=0,r=v(this),o=e.match(H)||[];t=o[i++];)r.hasClass(t)?r.removeClass(t):r.addClass(t);else void 0!==e&&"boolean"!==n||((t=ht(this))&&Y.set(this,"__className__",t),this.setAttribute&&this.setAttribute("class",t||!1===e?"":Y.get(this,"__className__")||""))}))},hasClass:function(e){var t,n,i=0;for(t=" "+e+" ";n=this[i++];)if(1===n.nodeType&&(" "+ft(ht(n))+" ").indexOf(t)>-1)return!0;return!1}});var dt=/\r/g;v.fn.extend({val:function(e){var t,n,i,r=this[0];return arguments.length?(i=v.isFunction(e),this.each((function(n){var r;1===this.nodeType&&(null==(r=i?e.call(this,n,v(this).val()):e)?r="":"number"==typeof r?r+="":Array.isArray(r)&&(r=v.map(r,(function(e){return null==e?"":e+""}))),(t=v.valHooks[this.type]||v.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,r,"value")||(this.value=r))}))):r?(t=v.valHooks[r.type]||v.valHooks[r.nodeName.toLowerCase()])&&"get"in t&&void 0!==(n=t.get(r,"value"))?n:"string"==typeof(n=r.value)?n.replace(dt,""):null==n?"":n:void 0}}),v.extend({valHooks:{option:{get:function(e){var t=v.find.attr(e,"value");return null!=t?t:ft(v.text(e))}},select:{get:function(e){var t,n,i,r=e.options,o=e.selectedIndex,s="select-one"===e.type,a=s?null:[],l=s?o+1:r.length;for(i=o<0?l:s?o:0;i<l;i++)if(((n=r[i]).selected||i===o)&&!n.disabled&&(!n.parentNode.disabled||!k(n.parentNode,"optgroup"))){if(t=v(n).val(),s)return t;a.push(t)}return a},set:function(e,t){for(var n,i,r=e.options,o=v.makeArray(t),s=r.length;s--;)((i=r[s]).selected=v.inArray(v.valHooks.option.get(i),o)>-1)&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),v.each(["radio","checkbox"],(function(){v.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=v.inArray(v(e).val(),t)>-1}},p.checkOn||(v.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}));var pt=/^(?:focusinfocus|focusoutblur)$/;v.extend(v.event,{trigger:function(t,n,r,o){var s,a,l,u,c,h,d,p=[r||i],g=f.call(t,"type")?t.type:t,m=f.call(t,"namespace")?t.namespace.split("."):[];if(a=l=r=r||i,3!==r.nodeType&&8!==r.nodeType&&!pt.test(g+v.event.triggered)&&(g.indexOf(".")>-1&&(m=g.split("."),g=m.shift(),m.sort()),c=g.indexOf(":")<0&&"on"+g,(t=t[v.expando]?t:new v.Event(g,"object"==typeof t&&t)).isTrigger=o?2:3,t.namespace=m.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=r),n=null==n?[t]:v.makeArray(n,[t]),d=v.event.special[g]||{},o||!d.trigger||!1!==d.trigger.apply(r,n))){if(!o&&!d.noBubble&&!v.isWindow(r)){for(u=d.delegateType||g,pt.test(u+g)||(a=a.parentNode);a;a=a.parentNode)p.push(a),l=a;l===(r.ownerDocument||i)&&p.push(l.defaultView||l.parentWindow||e)}for(s=0;(a=p[s++])&&!t.isPropagationStopped();)t.type=s>1?u:d.bindType||g,(h=(Y.get(a,"events")||{})[t.type]&&Y.get(a,"handle"))&&h.apply(a,n),(h=c&&a[c])&&h.apply&&U(a)&&(t.result=h.apply(a,n),!1===t.result&&t.preventDefault());return t.type=g,o||t.isDefaultPrevented()||d._default&&!1!==d._default.apply(p.pop(),n)||!U(r)||c&&v.isFunction(r[g])&&!v.isWindow(r)&&((l=r[c])&&(r[c]=null),v.event.triggered=g,r[g](),v.event.triggered=void 0,l&&(r[c]=l)),t.result}},simulate:function(e,t,n){var i=v.extend(new v.Event,n,{type:e,isSimulated:!0});v.event.trigger(i,null,t)}}),v.fn.extend({trigger:function(e,t){return this.each((function(){v.event.trigger(e,t,this)}))},triggerHandler:function(e,t){var n=this[0];if(n)return v.event.trigger(e,t,n,!0)}}),v.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),(function(e,t){v.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}})),v.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),p.focusin="onfocusin"in e,p.focusin||v.each({focus:"focusin",blur:"focusout"},(function(e,t){var n=function(e){v.event.simulate(t,e.target,v.event.fix(e))};v.event.special[t]={setup:function(){var i=this.ownerDocument||this,r=Y.access(i,t);r||i.addEventListener(e,n,!0),Y.access(i,t,(r||0)+1)},teardown:function(){var i=this.ownerDocument||this,r=Y.access(i,t)-1;r?Y.access(i,t,r):(i.removeEventListener(e,n,!0),Y.remove(i,t))}}}));var gt=e.location,mt=v.now(),vt=/\?/;v.parseXML=function(t){var n;if(!t||"string"!=typeof t)return null;try{n=(new e.DOMParser).parseFromString(t,"text/xml")}catch(e){n=void 0}return n&&!n.getElementsByTagName("parsererror").length||v.error("Invalid XML: "+t),n};var yt=/\[\]$/,bt=/\r?\n/g,wt=/^(?:submit|button|image|reset|file)$/i,_t=/^(?:input|select|textarea|keygen)/i;function xt(e,t,n,i){var r;if(Array.isArray(t))v.each(t,(function(t,r){n||yt.test(e)?i(e,r):xt(e+"["+("object"==typeof r&&null!=r?t:"")+"]",r,n,i)}));else if(n||"object"!==v.type(t))i(e,t);else for(r in t)xt(e+"["+r+"]",t[r],n,i)}v.param=function(e,t){var n,i=[],r=function(e,t){var n=v.isFunction(t)?t():t;i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(Array.isArray(e)||e.jquery&&!v.isPlainObject(e))v.each(e,(function(){r(this.name,this.value)}));else for(n in e)xt(n,e[n],t,r);return i.join("&")},v.fn.extend({serialize:function(){return v.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var e=v.prop(this,"elements");return e?v.makeArray(e):this})).filter((function(){var e=this.type;return this.name&&!v(this).is(":disabled")&&_t.test(this.nodeName)&&!wt.test(e)&&(this.checked||!se.test(e))})).map((function(e,t){var n=v(this).val();return null==n?null:Array.isArray(n)?v.map(n,(function(e){return{name:t.name,value:e.replace(bt,"\r\n")}})):{name:t.name,value:n.replace(bt,"\r\n")}})).get()}});var Ct=/%20/g,Et=/#.*$/,Tt=/([?&])_=[^&]*/,St=/^(.*?):[ \t]*([^\r\n]*)$/gm,kt=/^(?:GET|HEAD)$/,Dt=/^\/\//,At={},Nt={},Lt="*/".concat("*"),Ot=i.createElement("a");function It(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var i,r=0,o=t.toLowerCase().match(H)||[];if(v.isFunction(n))for(;i=o[r++];)"+"===i[0]?(i=i.slice(1)||"*",(e[i]=e[i]||[]).unshift(n)):(e[i]=e[i]||[]).push(n)}}function jt(e,t,n,i){var r={},o=e===Nt;function s(a){var l;return r[a]=!0,v.each(e[a]||[],(function(e,a){var u=a(t,n,i);return"string"!=typeof u||o||r[u]?o?!(l=u):void 0:(t.dataTypes.unshift(u),s(u),!1)})),l}return s(t.dataTypes[0])||!r["*"]&&s("*")}function Pt(e,t){var n,i,r=v.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((r[n]?e:i||(i={}))[n]=t[n]);return i&&v.extend(!0,e,i),e}Ot.href=gt.href,v.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:gt.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(gt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Lt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":v.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Pt(Pt(e,v.ajaxSettings),t):Pt(v.ajaxSettings,e)},ajaxPrefilter:It(At),ajaxTransport:It(Nt),ajax:function(t,n){"object"==typeof t&&(n=t,t=void 0),n=n||{};var r,o,s,a,l,u,c,f,h,d,p=v.ajaxSetup({},n),g=p.context||p,m=p.context&&(g.nodeType||g.jquery)?v(g):v.event,y=v.Deferred(),b=v.Callbacks("once memory"),w=p.statusCode||{},_={},x={},C="canceled",E={readyState:0,getResponseHeader:function(e){var t;if(c){if(!a)for(a={};t=St.exec(s);)a[t[1].toLowerCase()]=t[2];t=a[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return c?s:null},setRequestHeader:function(e,t){return null==c&&(e=x[e.toLowerCase()]=x[e.toLowerCase()]||e,_[e]=t),this},overrideMimeType:function(e){return null==c&&(p.mimeType=e),this},statusCode:function(e){var t;if(e)if(c)E.always(e[E.status]);else for(t in e)w[t]=[w[t],e[t]];return this},abort:function(e){var t=e||C;return r&&r.abort(t),T(0,t),this}};if(y.promise(E),p.url=((t||p.url||gt.href)+"").replace(Dt,gt.protocol+"//"),p.type=n.method||n.type||p.method||p.type,p.dataTypes=(p.dataType||"*").toLowerCase().match(H)||[""],null==p.crossDomain){u=i.createElement("a");try{u.href=p.url,u.href=u.href,p.crossDomain=Ot.protocol+"//"+Ot.host!=u.protocol+"//"+u.host}catch(e){p.crossDomain=!0}}if(p.data&&p.processData&&"string"!=typeof p.data&&(p.data=v.param(p.data,p.traditional)),jt(At,p,n,E),c)return E;for(h in(f=v.event&&p.global)&&0==v.active++&&v.event.trigger("ajaxStart"),p.type=p.type.toUpperCase(),p.hasContent=!kt.test(p.type),o=p.url.replace(Et,""),p.hasContent?p.data&&p.processData&&0===(p.contentType||"").indexOf("application/x-www-form-urlencoded")&&(p.data=p.data.replace(Ct,"+")):(d=p.url.slice(o.length),p.data&&(o+=(vt.test(o)?"&":"?")+p.data,delete p.data),!1===p.cache&&(o=o.replace(Tt,"$1"),d=(vt.test(o)?"&":"?")+"_="+mt+++d),p.url=o+d),p.ifModified&&(v.lastModified[o]&&E.setRequestHeader("If-Modified-Since",v.lastModified[o]),v.etag[o]&&E.setRequestHeader("If-None-Match",v.etag[o])),(p.data&&p.hasContent&&!1!==p.contentType||n.contentType)&&E.setRequestHeader("Content-Type",p.contentType),E.setRequestHeader("Accept",p.dataTypes[0]&&p.accepts[p.dataTypes[0]]?p.accepts[p.dataTypes[0]]+("*"!==p.dataTypes[0]?", "+Lt+"; q=0.01":""):p.accepts["*"]),p.headers)E.setRequestHeader(h,p.headers[h]);if(p.beforeSend&&(!1===p.beforeSend.call(g,E,p)||c))return E.abort();if(C="abort",b.add(p.complete),E.done(p.success),E.fail(p.error),r=jt(Nt,p,n,E)){if(E.readyState=1,f&&m.trigger("ajaxSend",[E,p]),c)return E;p.async&&p.timeout>0&&(l=e.setTimeout((function(){E.abort("timeout")}),p.timeout));try{c=!1,r.send(_,T)}catch(e){if(c)throw e;T(-1,e)}}else T(-1,"No Transport");function T(t,n,i,a){var u,h,d,_,x,C=n;c||(c=!0,l&&e.clearTimeout(l),r=void 0,s=a||"",E.readyState=t>0?4:0,u=t>=200&&t<300||304===t,i&&(_=function(e,t,n){for(var i,r,o,s,a=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===i&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i)for(r in a)if(a[r]&&a[r].test(i)){l.unshift(r);break}if(l[0]in n)o=l[0];else{for(r in n){if(!l[0]||e.converters[r+" "+l[0]]){o=r;break}s||(s=r)}o=o||s}if(o)return o!==l[0]&&l.unshift(o),n[o]}(p,E,i)),_=function(e,t,n,i){var r,o,s,a,l,u={},c=e.dataTypes.slice();if(c[1])for(s in e.converters)u[s.toLowerCase()]=e.converters[s];for(o=c.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!l&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=o,o=c.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(s=u[l+" "+o]||u["* "+o]))for(r in u)if((a=r.split(" "))[1]===o&&(s=u[l+" "+a[0]]||u["* "+a[0]])){!0===s?s=u[r]:!0!==u[r]&&(o=a[0],c.unshift(a[1]));break}if(!0!==s)if(s&&e.throws)t=s(t);else try{t=s(t)}catch(e){return{state:"parsererror",error:s?e:"No conversion from "+l+" to "+o}}}return{state:"success",data:t}}(p,_,E,u),u?(p.ifModified&&((x=E.getResponseHeader("Last-Modified"))&&(v.lastModified[o]=x),(x=E.getResponseHeader("etag"))&&(v.etag[o]=x)),204===t||"HEAD"===p.type?C="nocontent":304===t?C="notmodified":(C=_.state,h=_.data,u=!(d=_.error))):(d=C,!t&&C||(C="error",t<0&&(t=0))),E.status=t,E.statusText=(n||C)+"",u?y.resolveWith(g,[h,C,E]):y.rejectWith(g,[E,C,d]),E.statusCode(w),w=void 0,f&&m.trigger(u?"ajaxSuccess":"ajaxError",[E,p,u?h:d]),b.fireWith(g,[E,C]),f&&(m.trigger("ajaxComplete",[E,p]),--v.active||v.event.trigger("ajaxStop")))}return E},getJSON:function(e,t,n){return v.get(e,t,n,"json")},getScript:function(e,t){return v.get(e,void 0,t,"script")}}),v.each(["get","post"],(function(e,t){v[t]=function(e,n,i,r){return v.isFunction(n)&&(r=r||i,i=n,n=void 0),v.ajax(v.extend({url:e,type:t,dataType:r,data:n,success:i},v.isPlainObject(e)&&e))}})),v._evalUrl=function(e){return v.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,throws:!0})},v.fn.extend({wrapAll:function(e){var t;return this[0]&&(v.isFunction(e)&&(e=e.call(this[0])),t=v(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map((function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e})).append(this)),this},wrapInner:function(e){return v.isFunction(e)?this.each((function(t){v(this).wrapInner(e.call(this,t))})):this.each((function(){var t=v(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)}))},wrap:function(e){var t=v.isFunction(e);return this.each((function(n){v(this).wrapAll(t?e.call(this,n):e)}))},unwrap:function(e){return this.parent(e).not("body").each((function(){v(this).replaceWith(this.childNodes)})),this}}),v.expr.pseudos.hidden=function(e){return!v.expr.pseudos.visible(e)},v.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},v.ajaxSettings.xhr=function(){try{return new e.XMLHttpRequest}catch(e){}};var Ht={0:200,1223:204},$t=v.ajaxSettings.xhr();p.cors=!!$t&&"withCredentials"in $t,p.ajax=$t=!!$t,v.ajaxTransport((function(t){var n,i;if(p.cors||$t&&!t.crossDomain)return{send:function(r,o){var s,a=t.xhr();if(a.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(s in t.xhrFields)a[s]=t.xhrFields[s];for(s in t.mimeType&&a.overrideMimeType&&a.overrideMimeType(t.mimeType),t.crossDomain||r["X-Requested-With"]||(r["X-Requested-With"]="XMLHttpRequest"),r)a.setRequestHeader(s,r[s]);n=function(e){return function(){n&&(n=i=a.onload=a.onerror=a.onabort=a.onreadystatechange=null,"abort"===e?a.abort():"error"===e?"number"!=typeof a.status?o(0,"error"):o(a.status,a.statusText):o(Ht[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=n(),i=a.onerror=n("error"),void 0!==a.onabort?a.onabort=i:a.onreadystatechange=function(){4===a.readyState&&e.setTimeout((function(){n&&i()}))},n=n("abort");try{a.send(t.hasContent&&t.data||null)}catch(e){if(n)throw e}},abort:function(){n&&n()}}})),v.ajaxPrefilter((function(e){e.crossDomain&&(e.contents.script=!1)})),v.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return v.globalEval(e),e}}}),v.ajaxPrefilter("script",(function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")})),v.ajaxTransport("script",(function(e){var t,n;if(e.crossDomain)return{send:function(r,o){t=v("<script>").prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&o("error"===e.type?404:200,e.type)}),i.head.appendChild(t[0])},abort:function(){n&&n()}}}));var Mt,Ft=[],Bt=/(=)\?(?=&|$)|\?\?/;v.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Ft.pop()||v.expando+"_"+mt++;return this[e]=!0,e}}),v.ajaxPrefilter("json jsonp",(function(t,n,i){var r,o,s,a=!1!==t.jsonp&&(Bt.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&Bt.test(t.data)&&"data");if(a||"jsonp"===t.dataTypes[0])return r=t.jsonpCallback=v.isFunction(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,a?t[a]=t[a].replace(Bt,"$1"+r):!1!==t.jsonp&&(t.url+=(vt.test(t.url)?"&":"?")+t.jsonp+"="+r),t.converters["script json"]=function(){return s||v.error(r+" was not called"),s[0]},t.dataTypes[0]="json",o=e[r],e[r]=function(){s=arguments},i.always((function(){void 0===o?v(e).removeProp(r):e[r]=o,t[r]&&(t.jsonpCallback=n.jsonpCallback,Ft.push(r)),s&&v.isFunction(o)&&o(s[0]),s=o=void 0})),"script"})),p.createHTMLDocument=((Mt=i.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===Mt.childNodes.length),v.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(p.createHTMLDocument?((r=(t=i.implementation.createHTMLDocument("")).createElement("base")).href=i.location.href,t.head.appendChild(r)):t=i),s=!n&&[],(o=D.exec(e))?[t.createElement(o[1])]:(o=ge([e],t,s),s&&s.length&&v(s).remove(),v.merge([],o.childNodes)));var r,o,s},v.fn.load=function(e,t,n){var i,r,o,s=this,a=e.indexOf(" ");return a>-1&&(i=ft(e.slice(a)),e=e.slice(0,a)),v.isFunction(t)?(n=t,t=void 0):t&&"object"==typeof t&&(r="POST"),s.length>0&&v.ajax({url:e,type:r||"GET",dataType:"html",data:t}).done((function(e){o=arguments,s.html(i?v("<div>").append(v.parseHTML(e)).find(i):e)})).always(n&&function(e,t){s.each((function(){n.apply(this,o||[e.responseText,t,e])}))}),this},v.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(e,t){v.fn[t]=function(e){return this.on(t,e)}})),v.expr.pseudos.animated=function(e){return v.grep(v.timers,(function(t){return e===t.elem})).length},v.offset={setOffset:function(e,t,n){var i,r,o,s,a,l,u=v.css(e,"position"),c=v(e),f={};"static"===u&&(e.style.position="relative"),a=c.offset(),o=v.css(e,"top"),l=v.css(e,"left"),("absolute"===u||"fixed"===u)&&(o+l).indexOf("auto")>-1?(s=(i=c.position()).top,r=i.left):(s=parseFloat(o)||0,r=parseFloat(l)||0),v.isFunction(t)&&(t=t.call(e,n,v.extend({},a))),null!=t.top&&(f.top=t.top-a.top+s),null!=t.left&&(f.left=t.left-a.left+r),"using"in t?t.using.call(e,f):c.css(f)}},v.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each((function(t){v.offset.setOffset(this,e,t)}));var t,n,i,r,o=this[0];return o?o.getClientRects().length?(i=o.getBoundingClientRect(),n=(t=o.ownerDocument).documentElement,r=t.defaultView,{top:i.top+r.pageYOffset-n.clientTop,left:i.left+r.pageXOffset-n.clientLeft}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n=this[0],i={top:0,left:0};return"fixed"===v.css(n,"position")?t=n.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),k(e[0],"html")||(i=e.offset()),i={top:i.top+v.css(e[0],"borderTopWidth",!0),left:i.left+v.css(e[0],"borderLeftWidth",!0)}),{top:t.top-i.top-v.css(n,"marginTop",!0),left:t.left-i.left-v.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map((function(){for(var e=this.offsetParent;e&&"static"===v.css(e,"position");)e=e.offsetParent;return e||me}))}}),v.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(e,t){var n="pageYOffset"===t;v.fn[e]=function(i){return q(this,(function(e,i,r){var o;if(v.isWindow(e)?o=e:9===e.nodeType&&(o=e.defaultView),void 0===r)return o?o[t]:e[i];o?o.scrollTo(n?o.pageXOffset:r,n?r:o.pageYOffset):e[i]=r}),e,i,arguments.length)}})),v.each(["top","left"],(function(e,t){v.cssHooks[t]=Be(p.pixelPosition,(function(e,n){if(n)return n=Fe(e,t),$e.test(n)?v(e).position()[t]+"px":n}))})),v.each({Height:"height",Width:"width"},(function(e,t){v.each({padding:"inner"+e,content:t,"":"outer"+e},(function(n,i){v.fn[i]=function(r,o){var s=arguments.length&&(n||"boolean"!=typeof r),a=n||(!0===r||!0===o?"margin":"border");return q(this,(function(t,n,r){var o;return v.isWindow(t)?0===i.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===r?v.css(t,n,a):v.style(t,n,r,a)}),t,s?r:void 0,s)}}))})),v.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}}),v.holdReady=function(e){e?v.readyWait++:v.ready(!0)},v.isArray=Array.isArray,v.parseJSON=JSON.parse,v.nodeName=k,"function"==typeof define&&define.amd&&define("jquery",[],(function(){return v}));var Wt=e.jQuery,Rt=e.$;return v.noConflict=function(t){return e.$===v&&(e.$=Rt),t&&e.jQuery===v&&(e.jQuery=Wt),v},t||(e.jQuery=e.$=v),v})),
/**!
 * @fileOverview Kickass library to create and place poppers near their reference elements.
 * @version 1.12.6
 * @license
 * Copyright (c) 2016 Federico Zivolo and contributors
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.Popper=t()}(this,(function(){"use strict";for(var e="undefined"!=typeof window&&void 0!==window.document,t=["Edge","Trident","Firefox"],n=0,i=0;i<t.length;i+=1)if(e&&navigator.userAgent.indexOf(t[i])>=0){n=1;break}var r=e&&window.Promise?function(e){var t=!1;return function(){t||(t=!0,Promise.resolve().then((function(){t=!1,e()})))}}:function(e){var t=!1;return function(){t||(t=!0,setTimeout((function(){t=!1,e()}),n))}};function o(e){return e&&"[object Function]"==={}.toString.call(e)}function s(e,t){if(1!==e.nodeType)return[];var n=window.getComputedStyle(e,null);return t?n[t]:n}function a(e){return"HTML"===e.nodeName?e:e.parentNode||e.host}function l(e){if(!e)return window.document.body;switch(e.nodeName){case"HTML":case"BODY":return e.ownerDocument.body;case"#document":return e.body}var t=s(e),n=t.overflow,i=t.overflowX,r=t.overflowY;return/(auto|scroll)/.test(n+r+i)?e:l(a(e))}function u(e){var t=e&&e.offsetParent,n=t&&t.nodeName;return n&&"BODY"!==n&&"HTML"!==n?-1!==["TD","TABLE"].indexOf(t.nodeName)&&"static"===s(t,"position")?u(t):t:e?e.ownerDocument.documentElement:window.document.documentElement}function c(e){return null!==e.parentNode?c(e.parentNode):e}function f(e,t){if(!(e&&e.nodeType&&t&&t.nodeType))return window.document.documentElement;var n=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,i=n?e:t,r=n?t:e,o=document.createRange();o.setStart(i,0),o.setEnd(r,0);var s,a,l=o.commonAncestorContainer;if(e!==l&&t!==l||i.contains(r))return"BODY"===(a=(s=l).nodeName)||"HTML"!==a&&u(s.firstElementChild)!==s?u(l):l;var h=c(e);return h.host?f(h.host,t):f(e,c(t).host)}function h(e){var t="top"===(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"top")?"scrollTop":"scrollLeft",n=e.nodeName;if("BODY"===n||"HTML"===n){var i=e.ownerDocument.documentElement;return(e.ownerDocument.scrollingElement||i)[t]}return e[t]}function d(e,t){var n="x"===t?"Left":"Top",i="Left"===n?"Right":"Bottom";return+e["border"+n+"Width"].split("px")[0]+ +e["border"+i+"Width"].split("px")[0]}var p=void 0,g=function(){return void 0===p&&(p=-1!==navigator.appVersion.indexOf("MSIE 10")),p};function m(e,t,n,i){return Math.max(t["offset"+e],t["scroll"+e],n["client"+e],n["offset"+e],n["scroll"+e],g()?n["offset"+e]+i["margin"+("Height"===e?"Top":"Left")]+i["margin"+("Height"===e?"Bottom":"Right")]:0)}function v(){var e=window.document.body,t=window.document.documentElement,n=g()&&window.getComputedStyle(t);return{height:m("Height",e,t,n),width:m("Width",e,t,n)}}var y=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),b=function(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},w=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e};function _(e){return w({},e,{right:e.left+e.width,bottom:e.top+e.height})}function x(e){var t={};if(g())try{t=e.getBoundingClientRect();var n=h(e,"top"),i=h(e,"left");t.top+=n,t.left+=i,t.bottom+=n,t.right+=i}catch(e){}else t=e.getBoundingClientRect();var r={left:t.left,top:t.top,width:t.right-t.left,height:t.bottom-t.top},o="HTML"===e.nodeName?v():{},a=o.width||e.clientWidth||r.right-r.left,l=o.height||e.clientHeight||r.bottom-r.top,u=e.offsetWidth-a,c=e.offsetHeight-l;if(u||c){var f=s(e);u-=d(f,"x"),c-=d(f,"y"),r.width-=u,r.height-=c}return _(r)}function C(e,t){var n=g(),i="HTML"===t.nodeName,r=x(e),o=x(t),a=l(e),u=s(t),c=+u.borderTopWidth.split("px")[0],f=+u.borderLeftWidth.split("px")[0],d=_({top:r.top-o.top-c,left:r.left-o.left-f,width:r.width,height:r.height});if(d.marginTop=0,d.marginLeft=0,!n&&i){var p=+u.marginTop.split("px")[0],m=+u.marginLeft.split("px")[0];d.top-=c-p,d.bottom-=c-p,d.left-=f-m,d.right-=f-m,d.marginTop=p,d.marginLeft=m}return(n?t.contains(a):t===a&&"BODY"!==a.nodeName)&&(d=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=h(t,"top"),r=h(t,"left"),o=n?-1:1;return e.top+=i*o,e.bottom+=i*o,e.left+=r*o,e.right+=r*o,e}(d,t)),d}function E(e){var t=e.nodeName;return"BODY"!==t&&"HTML"!==t&&("fixed"===s(e,"position")||E(a(e)))}function T(e,t,n,i){var r={top:0,left:0},o=f(e,t);if("viewport"===i)r=function(e){var t=e.ownerDocument.documentElement,n=C(e,t),i=Math.max(t.clientWidth,window.innerWidth||0),r=Math.max(t.clientHeight,window.innerHeight||0),o=h(t),s=h(t,"left");return _({top:o-n.top+n.marginTop,left:s-n.left+n.marginLeft,width:i,height:r})}(o);else{var s=void 0;"scrollParent"===i?"BODY"===(s=l(a(e))).nodeName&&(s=e.ownerDocument.documentElement):s="window"===i?e.ownerDocument.documentElement:i;var u=C(s,o);if("HTML"!==s.nodeName||E(o))r=u;else{var c=v(),d=c.height,p=c.width;r.top+=u.top-u.marginTop,r.bottom=d+u.top,r.left+=u.left-u.marginLeft,r.right=p+u.left}}return r.left+=n,r.top+=n,r.right-=n,r.bottom-=n,r}function S(e,t,n,i,r){var o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;if(-1===e.indexOf("auto"))return e;var s=T(n,i,o,r),a={top:{width:s.width,height:t.top-s.top},right:{width:s.right-t.right,height:s.height},bottom:{width:s.width,height:s.bottom-t.bottom},left:{width:t.left-s.left,height:s.height}},l=Object.keys(a).map((function(e){return w({key:e},a[e],{area:(t=a[e],t.width*t.height)});var t})).sort((function(e,t){return t.area-e.area})),u=l.filter((function(e){var t=e.width,i=e.height;return t>=n.clientWidth&&i>=n.clientHeight})),c=u.length>0?u[0].key:l[0].key,f=e.split("-")[1];return c+(f?"-"+f:"")}function k(e,t,n){return C(n,f(t,n))}function D(e){var t=window.getComputedStyle(e),n=parseFloat(t.marginTop)+parseFloat(t.marginBottom),i=parseFloat(t.marginLeft)+parseFloat(t.marginRight);return{width:e.offsetWidth+i,height:e.offsetHeight+n}}function A(e){var t={left:"right",right:"left",bottom:"top",top:"bottom"};return e.replace(/left|right|bottom|top/g,(function(e){return t[e]}))}function N(e,t,n){n=n.split("-")[0];var i=D(e),r={width:i.width,height:i.height},o=-1!==["right","left"].indexOf(n),s=o?"top":"left",a=o?"left":"top",l=o?"height":"width",u=o?"width":"height";return r[s]=t[s]+t[l]/2-i[l]/2,r[a]=n===a?t[a]-i[u]:t[A(a)],r}function L(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function O(e,t,n){return(void 0===n?e:e.slice(0,function(e,t,n){if(Array.prototype.findIndex)return e.findIndex((function(e){return e[t]===n}));var i=L(e,(function(e){return e[t]===n}));return e.indexOf(i)}(e,"name",n))).forEach((function(e){e.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var n=e.function||e.fn;e.enabled&&o(n)&&(t.offsets.popper=_(t.offsets.popper),t.offsets.reference=_(t.offsets.reference),t=n(t,e))})),t}function I(){if(!this.state.isDestroyed){var e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};e.offsets.reference=k(this.state,this.popper,this.reference),e.placement=S(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.offsets.popper=N(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position="absolute",e=O(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e))}}function j(e,t){return e.some((function(e){var n=e.name;return e.enabled&&n===t}))}function P(e){for(var t=[!1,"ms","Webkit","Moz","O"],n=e.charAt(0).toUpperCase()+e.slice(1),i=0;i<t.length-1;i++){var r=t[i],o=r?""+r+n:e;if(void 0!==window.document.body.style[o])return o}return null}function H(){return this.state.isDestroyed=!0,j(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.left="",this.popper.style.position="",this.popper.style.top="",this.popper.style[P("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function $(e){var t=e.ownerDocument;return t?t.defaultView:window}function M(e,t,n,i){var r="BODY"===e.nodeName,o=r?e.ownerDocument.defaultView:e;o.addEventListener(t,n,{passive:!0}),r||M(l(o.parentNode),t,n,i),i.push(o)}function F(e,t,n,i){n.updateBound=i,$(e).addEventListener("resize",n.updateBound,{passive:!0});var r=l(e);return M(r,"scroll",n.updateBound,n.scrollParents),n.scrollElement=r,n.eventsEnabled=!0,n}function B(){this.state.eventsEnabled||(this.state=F(this.reference,this.options,this.state,this.scheduleUpdate))}function W(){var e,t;this.state.eventsEnabled&&(window.cancelAnimationFrame(this.scheduleUpdate),this.state=(e=this.reference,t=this.state,$(e).removeEventListener("resize",t.updateBound),t.scrollParents.forEach((function(e){e.removeEventListener("scroll",t.updateBound)})),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t))}function R(e){return""!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function q(e,t){Object.keys(t).forEach((function(n){var i="";-1!==["width","height","top","right","bottom","left"].indexOf(n)&&R(t[n])&&(i="px"),e.style[n]=t[n]+i}))}function U(e,t,n){var i=L(e,(function(e){return e.name===t})),r=!!i&&e.some((function(e){return e.name===n&&e.enabled&&e.order<i.order}));if(!r){var o="`"+t+"`",s="`"+n+"`";console.warn(s+" modifier is required by "+o+" modifier in order to work, be sure to include it before "+o+"!")}return r}var z=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],Y=z.slice(3);function Q(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=Y.indexOf(e),i=Y.slice(n+1).concat(Y.slice(0,n));return t?i.reverse():i}var X="flip",K="clockwise",V="counterclockwise";function G(e,t,n,i){var r=[0,0],o=-1!==["right","left"].indexOf(i),s=e.split(/(\+|\-)/).map((function(e){return e.trim()})),a=s.indexOf(L(s,(function(e){return-1!==e.search(/,|\s/)})));s[a]&&-1===s[a].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var l=/\s*,\s*|\s+/,u=-1!==a?[s.slice(0,a).concat([s[a].split(l)[0]]),[s[a].split(l)[1]].concat(s.slice(a+1))]:[s];return u=u.map((function(e,i){var r=(1===i?!o:o)?"height":"width",s=!1;return e.reduce((function(e,t){return""===e[e.length-1]&&-1!==["+","-"].indexOf(t)?(e[e.length-1]=t,s=!0,e):s?(e[e.length-1]+=t,s=!1,e):e.concat(t)}),[]).map((function(e){return function(e,t,n,i){var r=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),o=+r[1],s=r[2];if(!o)return e;if(0===s.indexOf("%")){return _("%p"===s?n:i)[t]/100*o}if("vh"===s||"vw"===s)return("vh"===s?Math.max(document.documentElement.clientHeight,window.innerHeight||0):Math.max(document.documentElement.clientWidth,window.innerWidth||0))/100*o;return o}(e,r,t,n)}))})),u.forEach((function(e,t){e.forEach((function(n,i){R(n)&&(r[t]+=n*("-"===e[i-1]?-1:1))}))})),r}var J={shift:{order:100,enabled:!0,fn:function(e){var t=e.placement,n=t.split("-")[0],i=t.split("-")[1];if(i){var r=e.offsets,o=r.reference,s=r.popper,a=-1!==["bottom","top"].indexOf(n),l=a?"left":"top",u=a?"width":"height",c={start:b({},l,o[l]),end:b({},l,o[l]+o[u]-s[u])};e.offsets.popper=w({},s,c[i])}return e}},offset:{order:200,enabled:!0,fn:function(e,t){var n=t.offset,i=e.placement,r=e.offsets,o=r.popper,s=r.reference,a=i.split("-")[0],l=void 0;return l=R(+n)?[+n,0]:G(n,o,s,a),"left"===a?(o.top+=l[0],o.left-=l[1]):"right"===a?(o.top+=l[0],o.left+=l[1]):"top"===a?(o.left+=l[0],o.top-=l[1]):"bottom"===a&&(o.left+=l[0],o.top+=l[1]),e.popper=o,e},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(e,t){var n=t.boundariesElement||u(e.instance.popper);e.instance.reference===n&&(n=u(n));var i=T(e.instance.popper,e.instance.reference,t.padding,n);t.boundaries=i;var r=t.priority,o=e.offsets.popper,s={primary:function(e){var n=o[e];return o[e]<i[e]&&!t.escapeWithReference&&(n=Math.max(o[e],i[e])),b({},e,n)},secondary:function(e){var n="right"===e?"left":"top",r=o[n];return o[e]>i[e]&&!t.escapeWithReference&&(r=Math.min(o[n],i[e]-("right"===e?o.width:o.height))),b({},n,r)}};return r.forEach((function(e){var t=-1!==["left","top"].indexOf(e)?"primary":"secondary";o=w({},o,s[t](e))})),e.offsets.popper=o,e},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(e){var t=e.offsets,n=t.popper,i=t.reference,r=e.placement.split("-")[0],o=Math.floor,s=-1!==["top","bottom"].indexOf(r),a=s?"right":"bottom",l=s?"left":"top",u=s?"width":"height";return n[a]<o(i[l])&&(e.offsets.popper[l]=o(i[l])-n[u]),n[l]>o(i[a])&&(e.offsets.popper[l]=o(i[a])),e}},arrow:{order:500,enabled:!0,fn:function(e,t){if(!U(e.instance.modifiers,"arrow","keepTogether"))return e;var n=t.element;if("string"==typeof n){if(!(n=e.instance.popper.querySelector(n)))return e}else if(!e.instance.popper.contains(n))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),e;var i=e.placement.split("-")[0],r=e.offsets,o=r.popper,a=r.reference,l=-1!==["left","right"].indexOf(i),u=l?"height":"width",c=l?"Top":"Left",f=c.toLowerCase(),h=l?"left":"top",d=l?"bottom":"right",p=D(n)[u];a[d]-p<o[f]&&(e.offsets.popper[f]-=o[f]-(a[d]-p)),a[f]+p>o[d]&&(e.offsets.popper[f]+=a[f]+p-o[d]);var g=a[f]+a[u]/2-p/2,m=s(e.instance.popper,"margin"+c).replace("px",""),v=g-_(e.offsets.popper)[f]-m;return v=Math.max(Math.min(o[u]-p,v),0),e.arrowElement=n,e.offsets.arrow={},e.offsets.arrow[f]=Math.round(v),e.offsets.arrow[h]="",e},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(e,t){if(j(e.instance.modifiers,"inner"))return e;if(e.flipped&&e.placement===e.originalPlacement)return e;var n=T(e.instance.popper,e.instance.reference,t.padding,t.boundariesElement),i=e.placement.split("-")[0],r=A(i),o=e.placement.split("-")[1]||"",s=[];switch(t.behavior){case X:s=[i,r];break;case K:s=Q(i);break;case V:s=Q(i,!0);break;default:s=t.behavior}return s.forEach((function(a,l){if(i!==a||s.length===l+1)return e;i=e.placement.split("-")[0],r=A(i);var u=e.offsets.popper,c=e.offsets.reference,f=Math.floor,h="left"===i&&f(u.right)>f(c.left)||"right"===i&&f(u.left)<f(c.right)||"top"===i&&f(u.bottom)>f(c.top)||"bottom"===i&&f(u.top)<f(c.bottom),d=f(u.left)<f(n.left),p=f(u.right)>f(n.right),g=f(u.top)<f(n.top),m=f(u.bottom)>f(n.bottom),v="left"===i&&d||"right"===i&&p||"top"===i&&g||"bottom"===i&&m,y=-1!==["top","bottom"].indexOf(i),b=!!t.flipVariations&&(y&&"start"===o&&d||y&&"end"===o&&p||!y&&"start"===o&&g||!y&&"end"===o&&m);(h||v||b)&&(e.flipped=!0,(h||v)&&(i=s[l+1]),b&&(o=function(e){return"end"===e?"start":"start"===e?"end":e}(o)),e.placement=i+(o?"-"+o:""),e.offsets.popper=w({},e.offsets.popper,N(e.instance.popper,e.offsets.reference,e.placement)),e=O(e.instance.modifiers,e,"flip"))})),e},behavior:"flip",padding:5,boundariesElement:"viewport"},inner:{order:700,enabled:!1,fn:function(e){var t=e.placement,n=t.split("-")[0],i=e.offsets,r=i.popper,o=i.reference,s=-1!==["left","right"].indexOf(n),a=-1===["top","left"].indexOf(n);return r[s?"left":"top"]=o[n]-(a?r[s?"width":"height"]:0),e.placement=A(t),e.offsets.popper=_(r),e}},hide:{order:800,enabled:!0,fn:function(e){if(!U(e.instance.modifiers,"hide","preventOverflow"))return e;var t=e.offsets.reference,n=L(e.instance.modifiers,(function(e){return"preventOverflow"===e.name})).boundaries;if(t.bottom<n.top||t.left>n.right||t.top>n.bottom||t.right<n.left){if(!0===e.hide)return e;e.hide=!0,e.attributes["x-out-of-boundaries"]=""}else{if(!1===e.hide)return e;e.hide=!1,e.attributes["x-out-of-boundaries"]=!1}return e}},computeStyle:{order:850,enabled:!0,fn:function(e,t){var n=t.x,i=t.y,r=e.offsets.popper,o=L(e.instance.modifiers,(function(e){return"applyStyle"===e.name})).gpuAcceleration;void 0!==o&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var s=void 0!==o?o:t.gpuAcceleration,a=x(u(e.instance.popper)),l={position:r.position},c={left:Math.floor(r.left),top:Math.floor(r.top),bottom:Math.floor(r.bottom),right:Math.floor(r.right)},f="bottom"===n?"top":"bottom",h="right"===i?"left":"right",d=P("transform"),p=void 0,g=void 0;if(g="bottom"===f?-a.height+c.bottom:c.top,p="right"===h?-a.width+c.right:c.left,s&&d)l[d]="translate3d("+p+"px, "+g+"px, 0)",l[f]=0,l[h]=0,l.willChange="transform";else{var m="bottom"===f?-1:1,v="right"===h?-1:1;l[f]=g*m,l[h]=p*v,l.willChange=f+", "+h}var y={"x-placement":e.placement};return e.attributes=w({},y,e.attributes),e.styles=w({},l,e.styles),e.arrowStyles=w({},e.offsets.arrow,e.arrowStyles),e},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(e){var t,n;return q(e.instance.popper,e.styles),t=e.instance.popper,n=e.attributes,Object.keys(n).forEach((function(e){!1!==n[e]?t.setAttribute(e,n[e]):t.removeAttribute(e)})),e.arrowElement&&Object.keys(e.arrowStyles).length&&q(e.arrowElement,e.arrowStyles),e},onLoad:function(e,t,n,i,r){var o=k(0,t,e),s=S(n.placement,o,t,e,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return t.setAttribute("x-placement",s),q(t,{position:"absolute"}),n},gpuAcceleration:void 0}},Z={placement:"bottom",eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:J},ee=function(){function e(t,n){var i=this,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.scheduleUpdate=function(){return requestAnimationFrame(i.update)},this.update=r(this.update.bind(this)),this.options=w({},e.Defaults,s),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=t&&t.jquery?t[0]:t,this.popper=n&&n.jquery?n[0]:n,this.options.modifiers={},Object.keys(w({},e.Defaults.modifiers,s.modifiers)).forEach((function(t){i.options.modifiers[t]=w({},e.Defaults.modifiers[t]||{},s.modifiers?s.modifiers[t]:{})})),this.modifiers=Object.keys(this.options.modifiers).map((function(e){return w({name:e},i.options.modifiers[e])})).sort((function(e,t){return e.order-t.order})),this.modifiers.forEach((function(e){e.enabled&&o(e.onLoad)&&e.onLoad(i.reference,i.popper,i.options,e,i.state)})),this.update();var a=this.options.eventsEnabled;a&&this.enableEventListeners(),this.state.eventsEnabled=a}return y(e,[{key:"update",value:function(){return I.call(this)}},{key:"destroy",value:function(){return H.call(this)}},{key:"enableEventListeners",value:function(){return B.call(this)}},{key:"disableEventListeners",value:function(){return W.call(this)}}]),e}();return ee.Utils=("undefined"!=typeof window?window:global).PopperUtils,ee.placements=z,ee.Defaults=Z,ee}));
/*!
  * Bootstrap v4.0.0-beta.2 (https://getbootstrap.com)
  * Copyright 2011-2017 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
  */
var bootstrap=function(e,t,n){"use strict";t=t&&t.hasOwnProperty("default")?t.default:t,n=n&&n.hasOwnProperty("default")?n.default:n;var i=function(){var e=!1,n={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};function i(e){var n=this,i=!1;return t(this).one(r.TRANSITION_END,(function(){i=!0})),setTimeout((function(){i||r.triggerTransitionEnd(n)}),e),this}var r={TRANSITION_END:"bsTransitionEnd",getUID:function(e){do{e+=~~(1e6*Math.random())}while(document.getElementById(e));return e},getSelectorFromElement:function(e){var n=e.getAttribute("data-target");n&&"#"!==n||(n=e.getAttribute("href")||"");try{return t(document).find(n).length>0?n:null}catch(e){return null}},reflow:function(e){return e.offsetHeight},triggerTransitionEnd:function(n){t(n).trigger(e.end)},supportsTransitionEnd:function(){return Boolean(e)},isElement:function(e){return(e[0]||e).nodeType},typeCheckConfig:function(e,t,n){for(var i in n)if(Object.prototype.hasOwnProperty.call(n,i)){var o=n[i],s=t[i],a=s&&r.isElement(s)?"element":(l=s,{}.toString.call(l).match(/\s([a-zA-Z]+)/)[1].toLowerCase());if(!new RegExp(o).test(a))throw new Error(e.toUpperCase()+': Option "'+i+'" provided type "'+a+'" but expected type "'+o+'".')}var l}};return e=function(){if(window.QUnit)return!1;var e=document.createElement("bootstrap");for(var t in n)if(void 0!==e.style[t])return{end:n[t]};return!1}(),t.fn.emulateTransitionEnd=i,r.supportsTransitionEnd()&&(t.event.special[r.TRANSITION_END]={bindType:e.end,delegateType:e.end,handle:function(e){if(t(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}}),r}();function r(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var o=function(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),e};var s=function(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t},a=function(){var e="alert",n="bs.alert",r="."+n,s=t.fn[e],a={CLOSE:"close"+r,CLOSED:"closed"+r,CLICK_DATA_API:"click"+r+".data-api"},l="alert",u="fade",c="show",f=function(){function e(e){this._element=e}var r=e.prototype;return r.close=function(e){e=e||this._element;var t=this._getRootElement(e);this._triggerCloseEvent(t).isDefaultPrevented()||this._removeElement(t)},r.dispose=function(){t.removeData(this._element,n),this._element=null},r._getRootElement=function(e){var n=i.getSelectorFromElement(e),r=!1;return n&&(r=t(n)[0]),r||(r=t(e).closest("."+l)[0]),r},r._triggerCloseEvent=function(e){var n=t.Event(a.CLOSE);return t(e).trigger(n),n},r._removeElement=function(e){var n=this;t(e).removeClass(c),i.supportsTransitionEnd()&&t(e).hasClass(u)?t(e).one(i.TRANSITION_END,(function(t){return n._destroyElement(e,t)})).emulateTransitionEnd(150):this._destroyElement(e)},r._destroyElement=function(e){t(e).detach().trigger(a.CLOSED).remove()},e._jQueryInterface=function(i){return this.each((function(){var r=t(this),o=r.data(n);o||(o=new e(this),r.data(n,o)),"close"===i&&o[i](this)}))},e._handleDismiss=function(e){return function(t){t&&t.preventDefault(),e.close(this)}},o(e,null,[{key:"VERSION",get:function(){return"4.0.0-beta.2"}}]),e}();return t(document).on(a.CLICK_DATA_API,'[data-dismiss="alert"]',f._handleDismiss(new f)),t.fn[e]=f._jQueryInterface,t.fn[e].Constructor=f,t.fn[e].noConflict=function(){return t.fn[e]=s,f._jQueryInterface},f}(),l=function(){var e="button",n="bs.button",i="."+n,r=".data-api",s=t.fn[e],a="active",l="btn",u="focus",c='[data-toggle^="button"]',f='[data-toggle="buttons"]',h="input",d=".active",p=".btn",g={CLICK_DATA_API:"click"+i+r,FOCUS_BLUR_DATA_API:"focus"+i+r+" blur"+i+r},m=function(){function e(e){this._element=e}var i=e.prototype;return i.toggle=function(){var e=!0,n=!0,i=t(this._element).closest(f)[0];if(i){var r=t(this._element).find(h)[0];if(r){if("radio"===r.type)if(r.checked&&t(this._element).hasClass(a))e=!1;else{var o=t(i).find(d)[0];o&&t(o).removeClass(a)}if(e){if(r.hasAttribute("disabled")||i.hasAttribute("disabled")||r.classList.contains("disabled")||i.classList.contains("disabled"))return;r.checked=!t(this._element).hasClass(a),t(r).trigger("change")}r.focus(),n=!1}}n&&this._element.setAttribute("aria-pressed",!t(this._element).hasClass(a)),e&&t(this._element).toggleClass(a)},i.dispose=function(){t.removeData(this._element,n),this._element=null},e._jQueryInterface=function(i){return this.each((function(){var r=t(this).data(n);r||(r=new e(this),t(this).data(n,r)),"toggle"===i&&r[i]()}))},o(e,null,[{key:"VERSION",get:function(){return"4.0.0-beta.2"}}]),e}();return t(document).on(g.CLICK_DATA_API,c,(function(e){e.preventDefault();var n=e.target;t(n).hasClass(l)||(n=t(n).closest(p)),m._jQueryInterface.call(t(n),"toggle")})).on(g.FOCUS_BLUR_DATA_API,c,(function(e){var n=t(e.target).closest(p)[0];t(n).toggleClass(u,/^focus(in)?$/.test(e.type))})),t.fn[e]=m._jQueryInterface,t.fn[e].Constructor=m,t.fn[e].noConflict=function(){return t.fn[e]=s,m._jQueryInterface},m}(),u=function(){var e="carousel",n="bs.carousel",r="."+n,s=".data-api",a=t.fn[e],l={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0},u={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean"},c="next",f="prev",h="left",d="right",p={SLIDE:"slide"+r,SLID:"slid"+r,KEYDOWN:"keydown"+r,MOUSEENTER:"mouseenter"+r,MOUSELEAVE:"mouseleave"+r,TOUCHEND:"touchend"+r,LOAD_DATA_API:"load"+r+s,CLICK_DATA_API:"click"+r+s},g="carousel",m="active",v="slide",y="carousel-item-right",b="carousel-item-left",w="carousel-item-next",_="carousel-item-prev",x=".active",C=".active.carousel-item",E=".carousel-item",T=".carousel-item-next, .carousel-item-prev",S=".carousel-indicators",k="[data-slide], [data-slide-to]",D='[data-ride="carousel"]',A=function(){function s(e,n){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this._config=this._getConfig(n),this._element=t(e)[0],this._indicatorsElement=t(this._element).find(S)[0],this._addEventListeners()}var a=s.prototype;return a.next=function(){this._isSliding||this._slide(c)},a.nextWhenVisible=function(){!document.hidden&&t(this._element).is(":visible")&&"hidden"!==t(this._element).css("visibility")&&this.next()},a.prev=function(){this._isSliding||this._slide(f)},a.pause=function(e){e||(this._isPaused=!0),t(this._element).find(T)[0]&&i.supportsTransitionEnd()&&(i.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},a.cycle=function(e){e||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},a.to=function(e){var n=this;this._activeElement=t(this._element).find(C)[0];var i=this._getItemIndex(this._activeElement);if(!(e>this._items.length-1||e<0))if(this._isSliding)t(this._element).one(p.SLID,(function(){return n.to(e)}));else{if(i===e)return this.pause(),void this.cycle();var r=e>i?c:f;this._slide(r,this._items[e])}},a.dispose=function(){t(this._element).off(r),t.removeData(this._element,n),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},a._getConfig=function(n){return n=t.extend({},l,n),i.typeCheckConfig(e,n,u),n},a._addEventListeners=function(){var e=this;this._config.keyboard&&t(this._element).on(p.KEYDOWN,(function(t){return e._keydown(t)})),"hover"===this._config.pause&&(t(this._element).on(p.MOUSEENTER,(function(t){return e.pause(t)})).on(p.MOUSELEAVE,(function(t){return e.cycle(t)})),"ontouchstart"in document.documentElement&&t(this._element).on(p.TOUCHEND,(function(){e.pause(),e.touchTimeout&&clearTimeout(e.touchTimeout),e.touchTimeout=setTimeout((function(t){return e.cycle(t)}),500+e._config.interval)})))},a._keydown=function(e){if(!/input|textarea/i.test(e.target.tagName))switch(e.which){case 37:e.preventDefault(),this.prev();break;case 39:e.preventDefault(),this.next();break;default:return}},a._getItemIndex=function(e){return this._items=t.makeArray(t(e).parent().find(E)),this._items.indexOf(e)},a._getItemByDirection=function(e,t){var n=e===c,i=e===f,r=this._getItemIndex(t),o=this._items.length-1;if((i&&0===r||n&&r===o)&&!this._config.wrap)return t;var s=(r+(e===f?-1:1))%this._items.length;return-1===s?this._items[this._items.length-1]:this._items[s]},a._triggerSlideEvent=function(e,n){var i=this._getItemIndex(e),r=this._getItemIndex(t(this._element).find(C)[0]),o=t.Event(p.SLIDE,{relatedTarget:e,direction:n,from:r,to:i});return t(this._element).trigger(o),o},a._setActiveIndicatorElement=function(e){if(this._indicatorsElement){t(this._indicatorsElement).find(x).removeClass(m);var n=this._indicatorsElement.children[this._getItemIndex(e)];n&&t(n).addClass(m)}},a._slide=function(e,n){var r,o,s,a=this,l=t(this._element).find(C)[0],u=this._getItemIndex(l),f=n||l&&this._getItemByDirection(e,l),g=this._getItemIndex(f),x=Boolean(this._interval);if(e===c?(r=b,o=w,s=h):(r=y,o=_,s=d),f&&t(f).hasClass(m))this._isSliding=!1;else if(!this._triggerSlideEvent(f,s).isDefaultPrevented()&&l&&f){this._isSliding=!0,x&&this.pause(),this._setActiveIndicatorElement(f);var E=t.Event(p.SLID,{relatedTarget:f,direction:s,from:u,to:g});i.supportsTransitionEnd()&&t(this._element).hasClass(v)?(t(f).addClass(o),i.reflow(f),t(l).addClass(r),t(f).addClass(r),t(l).one(i.TRANSITION_END,(function(){t(f).removeClass(r+" "+o).addClass(m),t(l).removeClass(m+" "+o+" "+r),a._isSliding=!1,setTimeout((function(){return t(a._element).trigger(E)}),0)})).emulateTransitionEnd(600)):(t(l).removeClass(m),t(f).addClass(m),this._isSliding=!1,t(this._element).trigger(E)),x&&this.cycle()}},s._jQueryInterface=function(e){return this.each((function(){var i=t(this).data(n),r=t.extend({},l,t(this).data());"object"==typeof e&&t.extend(r,e);var o="string"==typeof e?e:r.slide;if(i||(i=new s(this,r),t(this).data(n,i)),"number"==typeof e)i.to(e);else if("string"==typeof o){if(void 0===i[o])throw new Error('No method named "'+o+'"');i[o]()}else r.interval&&(i.pause(),i.cycle())}))},s._dataApiClickHandler=function(e){var r=i.getSelectorFromElement(this);if(r){var o=t(r)[0];if(o&&t(o).hasClass(g)){var a=t.extend({},t(o).data(),t(this).data()),l=this.getAttribute("data-slide-to");l&&(a.interval=!1),s._jQueryInterface.call(t(o),a),l&&t(o).data(n).to(l),e.preventDefault()}}},o(s,null,[{key:"VERSION",get:function(){return"4.0.0-beta.2"}},{key:"Default",get:function(){return l}}]),s}();return t(document).on(p.CLICK_DATA_API,k,A._dataApiClickHandler),t(window).on(p.LOAD_DATA_API,(function(){t(D).each((function(){var e=t(this);A._jQueryInterface.call(e,e.data())}))})),t.fn[e]=A._jQueryInterface,t.fn[e].Constructor=A,t.fn[e].noConflict=function(){return t.fn[e]=a,A._jQueryInterface},A}(),c=function(){var e="collapse",n="bs.collapse",r="."+n,s=t.fn[e],a={toggle:!0,parent:""},l={toggle:"boolean",parent:"(string|element)"},u={SHOW:"show"+r,SHOWN:"shown"+r,HIDE:"hide"+r,HIDDEN:"hidden"+r,CLICK_DATA_API:"click"+r+".data-api"},c="show",f="collapse",h="collapsing",d="collapsed",p="width",g="height",m=".show, .collapsing",v='[data-toggle="collapse"]',y=function(){function r(e,n){this._isTransitioning=!1,this._element=e,this._config=this._getConfig(n),this._triggerArray=t.makeArray(t('[data-toggle="collapse"][href="#'+e.id+'"],[data-toggle="collapse"][data-target="#'+e.id+'"]'));for(var r=t(v),o=0;o<r.length;o++){var s=r[o],a=i.getSelectorFromElement(s);null!==a&&t(a).filter(e).length>0&&this._triggerArray.push(s)}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}var s=r.prototype;return s.toggle=function(){t(this._element).hasClass(c)?this.hide():this.show()},s.show=function(){var e,o,s=this;if(!this._isTransitioning&&!t(this._element).hasClass(c)&&(this._parent&&((e=t.makeArray(t(this._parent).children().children(m))).length||(e=null)),!(e&&(o=t(e).data(n))&&o._isTransitioning))){var a=t.Event(u.SHOW);if(t(this._element).trigger(a),!a.isDefaultPrevented()){e&&(r._jQueryInterface.call(t(e),"hide"),o||t(e).data(n,null));var l=this._getDimension();t(this._element).removeClass(f).addClass(h),this._element.style[l]=0,this._triggerArray.length&&t(this._triggerArray).removeClass(d).attr("aria-expanded",!0),this.setTransitioning(!0);var p=function(){t(s._element).removeClass(h).addClass(f).addClass(c),s._element.style[l]="",s.setTransitioning(!1),t(s._element).trigger(u.SHOWN)};if(i.supportsTransitionEnd()){var g="scroll"+(l[0].toUpperCase()+l.slice(1));t(this._element).one(i.TRANSITION_END,p).emulateTransitionEnd(600),this._element.style[l]=this._element[g]+"px"}else p()}}},s.hide=function(){var e=this;if(!this._isTransitioning&&t(this._element).hasClass(c)){var n=t.Event(u.HIDE);if(t(this._element).trigger(n),!n.isDefaultPrevented()){var r=this._getDimension();if(this._element.style[r]=this._element.getBoundingClientRect()[r]+"px",i.reflow(this._element),t(this._element).addClass(h).removeClass(f).removeClass(c),this._triggerArray.length)for(var o=0;o<this._triggerArray.length;o++){var s=this._triggerArray[o],a=i.getSelectorFromElement(s);if(null!==a)t(a).hasClass(c)||t(s).addClass(d).attr("aria-expanded",!1)}this.setTransitioning(!0);var l=function(){e.setTransitioning(!1),t(e._element).removeClass(h).addClass(f).trigger(u.HIDDEN)};this._element.style[r]="",i.supportsTransitionEnd()?t(this._element).one(i.TRANSITION_END,l).emulateTransitionEnd(600):l()}}},s.setTransitioning=function(e){this._isTransitioning=e},s.dispose=function(){t.removeData(this._element,n),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},s._getConfig=function(n){return(n=t.extend({},a,n)).toggle=Boolean(n.toggle),i.typeCheckConfig(e,n,l),n},s._getDimension=function(){return t(this._element).hasClass(p)?p:g},s._getParent=function(){var e=this,n=null;i.isElement(this._config.parent)?(n=this._config.parent,void 0!==this._config.parent.jquery&&(n=this._config.parent[0])):n=t(this._config.parent)[0];var o='[data-toggle="collapse"][data-parent="'+this._config.parent+'"]';return t(n).find(o).each((function(t,n){e._addAriaAndCollapsedClass(r._getTargetFromElement(n),[n])})),n},s._addAriaAndCollapsedClass=function(e,n){if(e){var i=t(e).hasClass(c);n.length&&t(n).toggleClass(d,!i).attr("aria-expanded",i)}},r._getTargetFromElement=function(e){var n=i.getSelectorFromElement(e);return n?t(n)[0]:null},r._jQueryInterface=function(e){return this.each((function(){var i=t(this),o=i.data(n),s=t.extend({},a,i.data(),"object"==typeof e&&e);if(!o&&s.toggle&&/show|hide/.test(e)&&(s.toggle=!1),o||(o=new r(this,s),i.data(n,o)),"string"==typeof e){if(void 0===o[e])throw new Error('No method named "'+e+'"');o[e]()}}))},o(r,null,[{key:"VERSION",get:function(){return"4.0.0-beta.2"}},{key:"Default",get:function(){return a}}]),r}();return t(document).on(u.CLICK_DATA_API,v,(function(e){"A"===e.currentTarget.tagName&&e.preventDefault();var r=t(this),o=i.getSelectorFromElement(this);t(o).each((function(){var e=t(this),i=e.data(n)?"toggle":r.data();y._jQueryInterface.call(e,i)}))})),t.fn[e]=y._jQueryInterface,t.fn[e].Constructor=y,t.fn[e].noConflict=function(){return t.fn[e]=s,y._jQueryInterface},y}(),f=function(){if(void 0===n)throw new Error("Bootstrap dropdown require Popper.js (https://popper.js.org)");var e="dropdown",r="bs.dropdown",s="."+r,a=".data-api",l=t.fn[e],u=new RegExp("38|40|27"),c={HIDE:"hide"+s,HIDDEN:"hidden"+s,SHOW:"show"+s,SHOWN:"shown"+s,CLICK:"click"+s,CLICK_DATA_API:"click"+s+a,KEYDOWN_DATA_API:"keydown"+s+a,KEYUP_DATA_API:"keyup"+s+a},f="disabled",h="show",d="dropup",p="dropdown-menu-right",g="dropdown-menu-left",m='[data-toggle="dropdown"]',v=".dropdown form",y=".dropdown-menu",b=".navbar-nav",w=".dropdown-menu .dropdown-item:not(.disabled)",_="top-start",x="top-end",C="bottom-start",E="bottom-end",T={offset:0,flip:!0},S={offset:"(number|string|function)",flip:"boolean"},k=function(){function a(e,t){this._element=e,this._popper=null,this._config=this._getConfig(t),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}var l=a.prototype;return l.toggle=function(){if(!this._element.disabled&&!t(this._element).hasClass(f)){var e=a._getParentFromElement(this._element),i=t(this._menu).hasClass(h);if(a._clearMenus(),!i){var r={relatedTarget:this._element},o=t.Event(c.SHOW,r);if(t(e).trigger(o),!o.isDefaultPrevented()){var s=this._element;t(e).hasClass(d)&&(t(this._menu).hasClass(g)||t(this._menu).hasClass(p))&&(s=e),this._popper=new n(s,this._menu,this._getPopperConfig()),"ontouchstart"in document.documentElement&&!t(e).closest(b).length&&t("body").children().on("mouseover",null,t.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),t(this._menu).toggleClass(h),t(e).toggleClass(h).trigger(t.Event(c.SHOWN,r))}}}},l.dispose=function(){t.removeData(this._element,r),t(this._element).off(s),this._element=null,this._menu=null,null!==this._popper&&this._popper.destroy(),this._popper=null},l.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},l._addEventListeners=function(){var e=this;t(this._element).on(c.CLICK,(function(t){t.preventDefault(),t.stopPropagation(),e.toggle()}))},l._getConfig=function(n){return n=t.extend({},this.constructor.Default,t(this._element).data(),n),i.typeCheckConfig(e,n,this.constructor.DefaultType),n},l._getMenuElement=function(){if(!this._menu){var e=a._getParentFromElement(this._element);this._menu=t(e).find(y)[0]}return this._menu},l._getPlacement=function(){var e=t(this._element).parent(),n=C;return e.hasClass(d)?(n=_,t(this._menu).hasClass(p)&&(n=x)):t(this._menu).hasClass(p)&&(n=E),n},l._detectNavbar=function(){return t(this._element).closest(".navbar").length>0},l._getPopperConfig=function(){var e=this,n={};"function"==typeof this._config.offset?n.fn=function(n){return n.offsets=t.extend({},n.offsets,e._config.offset(n.offsets)||{}),n}:n.offset=this._config.offset;var i={placement:this._getPlacement(),modifiers:{offset:n,flip:{enabled:this._config.flip}}};return this._inNavbar&&(i.modifiers.applyStyle={enabled:!this._inNavbar}),i},a._jQueryInterface=function(e){return this.each((function(){var n=t(this).data(r);if(n||(n=new a(this,"object"==typeof e?e:null),t(this).data(r,n)),"string"==typeof e){if(void 0===n[e])throw new Error('No method named "'+e+'"');n[e]()}}))},a._clearMenus=function(e){if(!e||3!==e.which&&("keyup"!==e.type||9===e.which))for(var n=t.makeArray(t(m)),i=0;i<n.length;i++){var o=a._getParentFromElement(n[i]),s=t(n[i]).data(r),l={relatedTarget:n[i]};if(s){var u=s._menu;if(t(o).hasClass(h)&&!(e&&("click"===e.type&&/input|textarea/i.test(e.target.tagName)||"keyup"===e.type&&9===e.which)&&t.contains(o,e.target))){var f=t.Event(c.HIDE,l);t(o).trigger(f),f.isDefaultPrevented()||("ontouchstart"in document.documentElement&&t("body").children().off("mouseover",null,t.noop),n[i].setAttribute("aria-expanded","false"),t(u).removeClass(h),t(o).removeClass(h).trigger(t.Event(c.HIDDEN,l)))}}}},a._getParentFromElement=function(e){var n,r=i.getSelectorFromElement(e);return r&&(n=t(r)[0]),n||e.parentNode},a._dataApiKeydownHandler=function(e){if(!(!u.test(e.which)||/button/i.test(e.target.tagName)&&32===e.which||/input|textarea/i.test(e.target.tagName)||(e.preventDefault(),e.stopPropagation(),this.disabled||t(this).hasClass(f)))){var n=a._getParentFromElement(this),i=t(n).hasClass(h);if((i||27===e.which&&32===e.which)&&(!i||27!==e.which&&32!==e.which)){var r=t(n).find(w).get();if(r.length){var o=r.indexOf(e.target);38===e.which&&o>0&&o--,40===e.which&&o<r.length-1&&o++,o<0&&(o=0),r[o].focus()}}else{if(27===e.which){var s=t(n).find(m)[0];t(s).trigger("focus")}t(this).trigger("click")}}},o(a,null,[{key:"VERSION",get:function(){return"4.0.0-beta.2"}},{key:"Default",get:function(){return T}},{key:"DefaultType",get:function(){return S}}]),a}();return t(document).on(c.KEYDOWN_DATA_API,m,k._dataApiKeydownHandler).on(c.KEYDOWN_DATA_API,y,k._dataApiKeydownHandler).on(c.CLICK_DATA_API+" "+c.KEYUP_DATA_API,k._clearMenus).on(c.CLICK_DATA_API,m,(function(e){e.preventDefault(),e.stopPropagation(),k._jQueryInterface.call(t(this),"toggle")})).on(c.CLICK_DATA_API,v,(function(e){e.stopPropagation()})),t.fn[e]=k._jQueryInterface,t.fn[e].Constructor=k,t.fn[e].noConflict=function(){return t.fn[e]=l,k._jQueryInterface},k}(),h=function(){var e="modal",n="bs.modal",r="."+n,s=t.fn[e],a={backdrop:!0,keyboard:!0,focus:!0,show:!0},l={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},u={HIDE:"hide"+r,HIDDEN:"hidden"+r,SHOW:"show"+r,SHOWN:"shown"+r,FOCUSIN:"focusin"+r,RESIZE:"resize"+r,CLICK_DISMISS:"click.dismiss"+r,KEYDOWN_DISMISS:"keydown.dismiss"+r,MOUSEUP_DISMISS:"mouseup.dismiss"+r,MOUSEDOWN_DISMISS:"mousedown.dismiss"+r,CLICK_DATA_API:"click"+r+".data-api"},c="modal-scrollbar-measure",f="modal-backdrop",h="modal-open",d="fade",p="show",g=".modal-dialog",m='[data-toggle="modal"]',v='[data-dismiss="modal"]',y=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",b=".sticky-top",w=".navbar-toggler",_=function(){function s(e,n){this._config=this._getConfig(n),this._element=e,this._dialog=t(e).find(g)[0],this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._originalBodyPadding=0,this._scrollbarWidth=0}var m=s.prototype;return m.toggle=function(e){return this._isShown?this.hide():this.show(e)},m.show=function(e){var n=this;if(!this._isTransitioning&&!this._isShown){i.supportsTransitionEnd()&&t(this._element).hasClass(d)&&(this._isTransitioning=!0);var r=t.Event(u.SHOW,{relatedTarget:e});t(this._element).trigger(r),this._isShown||r.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),t(document.body).addClass(h),this._setEscapeEvent(),this._setResizeEvent(),t(this._element).on(u.CLICK_DISMISS,v,(function(e){return n.hide(e)})),t(this._dialog).on(u.MOUSEDOWN_DISMISS,(function(){t(n._element).one(u.MOUSEUP_DISMISS,(function(e){t(e.target).is(n._element)&&(n._ignoreBackdropClick=!0)}))})),this._showBackdrop((function(){return n._showElement(e)})))}},m.hide=function(e){var n=this;if(e&&e.preventDefault(),!this._isTransitioning&&this._isShown){var r=t.Event(u.HIDE);if(t(this._element).trigger(r),this._isShown&&!r.isDefaultPrevented()){this._isShown=!1;var o=i.supportsTransitionEnd()&&t(this._element).hasClass(d);o&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),t(document).off(u.FOCUSIN),t(this._element).removeClass(p),t(this._element).off(u.CLICK_DISMISS),t(this._dialog).off(u.MOUSEDOWN_DISMISS),o?t(this._element).one(i.TRANSITION_END,(function(e){return n._hideModal(e)})).emulateTransitionEnd(300):this._hideModal()}}},m.dispose=function(){t.removeData(this._element,n),t(window,document,this._element,this._backdrop).off(r),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._scrollbarWidth=null},m.handleUpdate=function(){this._adjustDialog()},m._getConfig=function(n){return n=t.extend({},a,n),i.typeCheckConfig(e,n,l),n},m._showElement=function(e){var n=this,r=i.supportsTransitionEnd()&&t(this._element).hasClass(d);this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.scrollTop=0,r&&i.reflow(this._element),t(this._element).addClass(p),this._config.focus&&this._enforceFocus();var o=t.Event(u.SHOWN,{relatedTarget:e}),s=function(){n._config.focus&&n._element.focus(),n._isTransitioning=!1,t(n._element).trigger(o)};r?t(this._dialog).one(i.TRANSITION_END,s).emulateTransitionEnd(300):s()},m._enforceFocus=function(){var e=this;t(document).off(u.FOCUSIN).on(u.FOCUSIN,(function(n){document===n.target||e._element===n.target||t(e._element).has(n.target).length||e._element.focus()}))},m._setEscapeEvent=function(){var e=this;this._isShown&&this._config.keyboard?t(this._element).on(u.KEYDOWN_DISMISS,(function(t){27===t.which&&(t.preventDefault(),e.hide())})):this._isShown||t(this._element).off(u.KEYDOWN_DISMISS)},m._setResizeEvent=function(){var e=this;this._isShown?t(window).on(u.RESIZE,(function(t){return e.handleUpdate(t)})):t(window).off(u.RESIZE)},m._hideModal=function(){var e=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._isTransitioning=!1,this._showBackdrop((function(){t(document.body).removeClass(h),e._resetAdjustments(),e._resetScrollbar(),t(e._element).trigger(u.HIDDEN)}))},m._removeBackdrop=function(){this._backdrop&&(t(this._backdrop).remove(),this._backdrop=null)},m._showBackdrop=function(e){var n=this,r=t(this._element).hasClass(d)?d:"";if(this._isShown&&this._config.backdrop){var o=i.supportsTransitionEnd()&&r;if(this._backdrop=document.createElement("div"),this._backdrop.className=f,r&&t(this._backdrop).addClass(r),t(this._backdrop).appendTo(document.body),t(this._element).on(u.CLICK_DISMISS,(function(e){n._ignoreBackdropClick?n._ignoreBackdropClick=!1:e.target===e.currentTarget&&("static"===n._config.backdrop?n._element.focus():n.hide())})),o&&i.reflow(this._backdrop),t(this._backdrop).addClass(p),!e)return;if(!o)return void e();t(this._backdrop).one(i.TRANSITION_END,e).emulateTransitionEnd(150)}else if(!this._isShown&&this._backdrop){t(this._backdrop).removeClass(p);var s=function(){n._removeBackdrop(),e&&e()};i.supportsTransitionEnd()&&t(this._element).hasClass(d)?t(this._backdrop).one(i.TRANSITION_END,s).emulateTransitionEnd(150):s()}else e&&e()},m._adjustDialog=function(){var e=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&e&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!e&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},m._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},m._checkScrollbar=function(){var e=document.body.getBoundingClientRect();this._isBodyOverflowing=e.left+e.right<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},m._setScrollbar=function(){var e=this;if(this._isBodyOverflowing){t(y).each((function(n,i){var r=t(i)[0].style.paddingRight,o=t(i).css("padding-right");t(i).data("padding-right",r).css("padding-right",parseFloat(o)+e._scrollbarWidth+"px")})),t(b).each((function(n,i){var r=t(i)[0].style.marginRight,o=t(i).css("margin-right");t(i).data("margin-right",r).css("margin-right",parseFloat(o)-e._scrollbarWidth+"px")})),t(w).each((function(n,i){var r=t(i)[0].style.marginRight,o=t(i).css("margin-right");t(i).data("margin-right",r).css("margin-right",parseFloat(o)+e._scrollbarWidth+"px")}));var n=document.body.style.paddingRight,i=t("body").css("padding-right");t("body").data("padding-right",n).css("padding-right",parseFloat(i)+this._scrollbarWidth+"px")}},m._resetScrollbar=function(){t(y).each((function(e,n){var i=t(n).data("padding-right");void 0!==i&&t(n).css("padding-right",i).removeData("padding-right")})),t(b+", "+w).each((function(e,n){var i=t(n).data("margin-right");void 0!==i&&t(n).css("margin-right",i).removeData("margin-right")}));var e=t("body").data("padding-right");void 0!==e&&t("body").css("padding-right",e).removeData("padding-right")},m._getScrollbarWidth=function(){var e=document.createElement("div");e.className=c,document.body.appendChild(e);var t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t},s._jQueryInterface=function(e,i){return this.each((function(){var r=t(this).data(n),o=t.extend({},s.Default,t(this).data(),"object"==typeof e&&e);if(r||(r=new s(this,o),t(this).data(n,r)),"string"==typeof e){if(void 0===r[e])throw new Error('No method named "'+e+'"');r[e](i)}else o.show&&r.show(i)}))},o(s,null,[{key:"VERSION",get:function(){return"4.0.0-beta.2"}},{key:"Default",get:function(){return a}}]),s}();return t(document).on(u.CLICK_DATA_API,m,(function(e){var r,o=this,s=i.getSelectorFromElement(this);s&&(r=t(s)[0]);var a=t(r).data(n)?"toggle":t.extend({},t(r).data(),t(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||e.preventDefault();var l=t(r).one(u.SHOW,(function(e){e.isDefaultPrevented()||l.one(u.HIDDEN,(function(){t(o).is(":visible")&&o.focus()}))}));_._jQueryInterface.call(t(r),a,this)})),t.fn[e]=_._jQueryInterface,t.fn[e].Constructor=_,t.fn[e].noConflict=function(){return t.fn[e]=s,_._jQueryInterface},_}(),d=function(){if(void 0===n)throw new Error("Bootstrap tooltips require Popper.js (https://popper.js.org)");var e="tooltip",r="bs.tooltip",s="."+r,a=t.fn[e],l="bs-tooltip",u=new RegExp("(^|\\s)"+l+"\\S+","g"),c={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)"},f={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},h={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip"},d="show",p="out",g={HIDE:"hide"+s,HIDDEN:"hidden"+s,SHOW:"show"+s,SHOWN:"shown"+s,INSERTED:"inserted"+s,CLICK:"click"+s,FOCUSIN:"focusin"+s,FOCUSOUT:"focusout"+s,MOUSEENTER:"mouseenter"+s,MOUSELEAVE:"mouseleave"+s},m="fade",v="show",y=".tooltip-inner",b=".arrow",w="hover",_="focus",x="click",C="manual",E=function(){function a(e,t){this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=e,this.config=this._getConfig(t),this.tip=null,this._setListeners()}var E=a.prototype;return E.enable=function(){this._isEnabled=!0},E.disable=function(){this._isEnabled=!1},E.toggleEnabled=function(){this._isEnabled=!this._isEnabled},E.toggle=function(e){if(this._isEnabled)if(e){var n=this.constructor.DATA_KEY,i=t(e.currentTarget).data(n);i||(i=new this.constructor(e.currentTarget,this._getDelegateConfig()),t(e.currentTarget).data(n,i)),i._activeTrigger.click=!i._activeTrigger.click,i._isWithActiveTrigger()?i._enter(null,i):i._leave(null,i)}else{if(t(this.getTipElement()).hasClass(v))return void this._leave(null,this);this._enter(null,this)}},E.dispose=function(){clearTimeout(this._timeout),t.removeData(this.element,this.constructor.DATA_KEY),t(this.element).off(this.constructor.EVENT_KEY),t(this.element).closest(".modal").off("hide.bs.modal"),this.tip&&t(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,null!==this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},E.show=function(){var e=this;if("none"===t(this.element).css("display"))throw new Error("Please use show on visible elements");var r=t.Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){t(this.element).trigger(r);var o=t.contains(this.element.ownerDocument.documentElement,this.element);if(r.isDefaultPrevented()||!o)return;var s=this.getTipElement(),l=i.getUID(this.constructor.NAME);s.setAttribute("id",l),this.element.setAttribute("aria-describedby",l),this.setContent(),this.config.animation&&t(s).addClass(m);var u="function"==typeof this.config.placement?this.config.placement.call(this,s,this.element):this.config.placement,c=this._getAttachment(u);this.addAttachmentClass(c);var f=!1===this.config.container?document.body:t(this.config.container);t(s).data(this.constructor.DATA_KEY,this),t.contains(this.element.ownerDocument.documentElement,this.tip)||t(s).appendTo(f),t(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new n(this.element,s,{placement:c,modifiers:{offset:{offset:this.config.offset},flip:{behavior:this.config.fallbackPlacement},arrow:{element:b}},onCreate:function(t){t.originalPlacement!==t.placement&&e._handlePopperPlacementChange(t)},onUpdate:function(t){e._handlePopperPlacementChange(t)}}),t(s).addClass(v),"ontouchstart"in document.documentElement&&t("body").children().on("mouseover",null,t.noop);var h=function(){e.config.animation&&e._fixTransition();var n=e._hoverState;e._hoverState=null,t(e.element).trigger(e.constructor.Event.SHOWN),n===p&&e._leave(null,e)};i.supportsTransitionEnd()&&t(this.tip).hasClass(m)?t(this.tip).one(i.TRANSITION_END,h).emulateTransitionEnd(a._TRANSITION_DURATION):h()}},E.hide=function(e){var n=this,r=this.getTipElement(),o=t.Event(this.constructor.Event.HIDE),s=function(){n._hoverState!==d&&r.parentNode&&r.parentNode.removeChild(r),n._cleanTipClass(),n.element.removeAttribute("aria-describedby"),t(n.element).trigger(n.constructor.Event.HIDDEN),null!==n._popper&&n._popper.destroy(),e&&e()};t(this.element).trigger(o),o.isDefaultPrevented()||(t(r).removeClass(v),"ontouchstart"in document.documentElement&&t("body").children().off("mouseover",null,t.noop),this._activeTrigger[x]=!1,this._activeTrigger[_]=!1,this._activeTrigger[w]=!1,i.supportsTransitionEnd()&&t(this.tip).hasClass(m)?t(r).one(i.TRANSITION_END,s).emulateTransitionEnd(150):s(),this._hoverState="")},E.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},E.isWithContent=function(){return Boolean(this.getTitle())},E.addAttachmentClass=function(e){t(this.getTipElement()).addClass(l+"-"+e)},E.getTipElement=function(){return this.tip=this.tip||t(this.config.template)[0],this.tip},E.setContent=function(){var e=t(this.getTipElement());this.setElementContent(e.find(y),this.getTitle()),e.removeClass(m+" "+v)},E.setElementContent=function(e,n){var i=this.config.html;"object"==typeof n&&(n.nodeType||n.jquery)?i?t(n).parent().is(e)||e.empty().append(n):e.text(t(n).text()):e[i?"html":"text"](n)},E.getTitle=function(){var e=this.element.getAttribute("data-original-title");return e||(e="function"==typeof this.config.title?this.config.title.call(this.element):this.config.title),e},E._getAttachment=function(e){return f[e.toUpperCase()]},E._setListeners=function(){var e=this;this.config.trigger.split(" ").forEach((function(n){if("click"===n)t(e.element).on(e.constructor.Event.CLICK,e.config.selector,(function(t){return e.toggle(t)}));else if(n!==C){var i=n===w?e.constructor.Event.MOUSEENTER:e.constructor.Event.FOCUSIN,r=n===w?e.constructor.Event.MOUSELEAVE:e.constructor.Event.FOCUSOUT;t(e.element).on(i,e.config.selector,(function(t){return e._enter(t)})).on(r,e.config.selector,(function(t){return e._leave(t)}))}t(e.element).closest(".modal").on("hide.bs.modal",(function(){return e.hide()}))})),this.config.selector?this.config=t.extend({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},E._fixTitle=function(){var e=typeof this.element.getAttribute("data-original-title");(this.element.getAttribute("title")||"string"!==e)&&(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},E._enter=function(e,n){var i=this.constructor.DATA_KEY;(n=n||t(e.currentTarget).data(i))||(n=new this.constructor(e.currentTarget,this._getDelegateConfig()),t(e.currentTarget).data(i,n)),e&&(n._activeTrigger["focusin"===e.type?_:w]=!0),t(n.getTipElement()).hasClass(v)||n._hoverState===d?n._hoverState=d:(clearTimeout(n._timeout),n._hoverState=d,n.config.delay&&n.config.delay.show?n._timeout=setTimeout((function(){n._hoverState===d&&n.show()}),n.config.delay.show):n.show())},E._leave=function(e,n){var i=this.constructor.DATA_KEY;(n=n||t(e.currentTarget).data(i))||(n=new this.constructor(e.currentTarget,this._getDelegateConfig()),t(e.currentTarget).data(i,n)),e&&(n._activeTrigger["focusout"===e.type?_:w]=!1),n._isWithActiveTrigger()||(clearTimeout(n._timeout),n._hoverState=p,n.config.delay&&n.config.delay.hide?n._timeout=setTimeout((function(){n._hoverState===p&&n.hide()}),n.config.delay.hide):n.hide())},E._isWithActiveTrigger=function(){for(var e in this._activeTrigger)if(this._activeTrigger[e])return!0;return!1},E._getConfig=function(n){return"number"==typeof(n=t.extend({},this.constructor.Default,t(this.element).data(),n)).delay&&(n.delay={show:n.delay,hide:n.delay}),"number"==typeof n.title&&(n.title=n.title.toString()),"number"==typeof n.content&&(n.content=n.content.toString()),i.typeCheckConfig(e,n,this.constructor.DefaultType),n},E._getDelegateConfig=function(){var e={};if(this.config)for(var t in this.config)this.constructor.Default[t]!==this.config[t]&&(e[t]=this.config[t]);return e},E._cleanTipClass=function(){var e=t(this.getTipElement()),n=e.attr("class").match(u);null!==n&&n.length>0&&e.removeClass(n.join(""))},E._handlePopperPlacementChange=function(e){this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(e.placement))},E._fixTransition=function(){var e=this.getTipElement(),n=this.config.animation;null===e.getAttribute("x-placement")&&(t(e).removeClass(m),this.config.animation=!1,this.hide(),this.show(),this.config.animation=n)},a._jQueryInterface=function(e){return this.each((function(){var n=t(this).data(r),i="object"==typeof e&&e;if((n||!/dispose|hide/.test(e))&&(n||(n=new a(this,i),t(this).data(r,n)),"string"==typeof e)){if(void 0===n[e])throw new Error('No method named "'+e+'"');n[e]()}}))},o(a,null,[{key:"VERSION",get:function(){return"4.0.0-beta.2"}},{key:"Default",get:function(){return h}},{key:"NAME",get:function(){return e}},{key:"DATA_KEY",get:function(){return r}},{key:"Event",get:function(){return g}},{key:"EVENT_KEY",get:function(){return s}},{key:"DefaultType",get:function(){return c}}]),a}();return t.fn[e]=E._jQueryInterface,t.fn[e].Constructor=E,t.fn[e].noConflict=function(){return t.fn[e]=a,E._jQueryInterface},E}(),p=function(){var e="popover",n="bs.popover",i="."+n,r=t.fn[e],a="bs-popover",l=new RegExp("(^|\\s)"+a+"\\S+","g"),u=t.extend({},d.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),c=t.extend({},d.DefaultType,{content:"(string|element|function)"}),f="fade",h="show",p=".popover-header",g=".popover-body",m={HIDE:"hide"+i,HIDDEN:"hidden"+i,SHOW:"show"+i,SHOWN:"shown"+i,INSERTED:"inserted"+i,CLICK:"click"+i,FOCUSIN:"focusin"+i,FOCUSOUT:"focusout"+i,MOUSEENTER:"mouseenter"+i,MOUSELEAVE:"mouseleave"+i},v=function(r){function d(){return r.apply(this,arguments)||this}s(d,r);var v=d.prototype;return v.isWithContent=function(){return this.getTitle()||this._getContent()},v.addAttachmentClass=function(e){t(this.getTipElement()).addClass(a+"-"+e)},v.getTipElement=function(){return this.tip=this.tip||t(this.config.template)[0],this.tip},v.setContent=function(){var e=t(this.getTipElement());this.setElementContent(e.find(p),this.getTitle()),this.setElementContent(e.find(g),this._getContent()),e.removeClass(f+" "+h)},v._getContent=function(){return this.element.getAttribute("data-content")||("function"==typeof this.config.content?this.config.content.call(this.element):this.config.content)},v._cleanTipClass=function(){var e=t(this.getTipElement()),n=e.attr("class").match(l);null!==n&&n.length>0&&e.removeClass(n.join(""))},d._jQueryInterface=function(e){return this.each((function(){var i=t(this).data(n),r="object"==typeof e?e:null;if((i||!/destroy|hide/.test(e))&&(i||(i=new d(this,r),t(this).data(n,i)),"string"==typeof e)){if(void 0===i[e])throw new Error('No method named "'+e+'"');i[e]()}}))},o(d,null,[{key:"VERSION",get:function(){return"4.0.0-beta.2"}},{key:"Default",get:function(){return u}},{key:"NAME",get:function(){return e}},{key:"DATA_KEY",get:function(){return n}},{key:"Event",get:function(){return m}},{key:"EVENT_KEY",get:function(){return i}},{key:"DefaultType",get:function(){return c}}]),d}(d);return t.fn[e]=v._jQueryInterface,t.fn[e].Constructor=v,t.fn[e].noConflict=function(){return t.fn[e]=r,v._jQueryInterface},v}(),g=function(){var e="scrollspy",n="bs.scrollspy",r="."+n,s=t.fn[e],a={offset:10,method:"auto",target:""},l={offset:"number",method:"string",target:"(string|element)"},u={ACTIVATE:"activate"+r,SCROLL:"scroll"+r,LOAD_DATA_API:"load"+r+".data-api"},c="dropdown-item",f="active",h='[data-spy="scroll"]',d=".active",p=".nav, .list-group",g=".nav-link",m=".nav-item",v=".list-group-item",y=".dropdown",b=".dropdown-item",w=".dropdown-toggle",_="offset",x="position",C=function(){function s(e,n){var i=this;this._element=e,this._scrollElement="BODY"===e.tagName?window:e,this._config=this._getConfig(n),this._selector=this._config.target+" "+g+","+this._config.target+" "+v+","+this._config.target+" "+b,this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,t(this._scrollElement).on(u.SCROLL,(function(e){return i._process(e)})),this.refresh(),this._process()}var h=s.prototype;return h.refresh=function(){var e=this,n=this._scrollElement!==this._scrollElement.window?x:_,r="auto"===this._config.method?n:this._config.method,o=r===x?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),t.makeArray(t(this._selector)).map((function(e){var n,s=i.getSelectorFromElement(e);if(s&&(n=t(s)[0]),n){var a=n.getBoundingClientRect();if(a.width||a.height)return[t(n)[r]().top+o,s]}return null})).filter((function(e){return e})).sort((function(e,t){return e[0]-t[0]})).forEach((function(t){e._offsets.push(t[0]),e._targets.push(t[1])}))},h.dispose=function(){t.removeData(this._element,n),t(this._scrollElement).off(r),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},h._getConfig=function(n){if("string"!=typeof(n=t.extend({},a,n)).target){var r=t(n.target).attr("id");r||(r=i.getUID(e),t(n.target).attr("id",r)),n.target="#"+r}return i.typeCheckConfig(e,n,l),n},h._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},h._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},h._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},h._process=function(){var e=this._getScrollTop()+this._config.offset,t=this._getScrollHeight(),n=this._config.offset+t-this._getOffsetHeight();if(this._scrollHeight!==t&&this.refresh(),e>=n){var i=this._targets[this._targets.length-1];this._activeTarget!==i&&this._activate(i)}else{if(this._activeTarget&&e<this._offsets[0]&&this._offsets[0]>0)return this._activeTarget=null,void this._clear();for(var r=this._offsets.length;r--;){this._activeTarget!==this._targets[r]&&e>=this._offsets[r]&&(void 0===this._offsets[r+1]||e<this._offsets[r+1])&&this._activate(this._targets[r])}}},h._activate=function(e){this._activeTarget=e,this._clear();var n=this._selector.split(",");n=n.map((function(t){return t+'[data-target="'+e+'"],'+t+'[href="'+e+'"]'}));var i=t(n.join(","));i.hasClass(c)?(i.closest(y).find(w).addClass(f),i.addClass(f)):(i.addClass(f),i.parents(p).prev(g+", "+v).addClass(f),i.parents(p).prev(m).children(g).addClass(f)),t(this._scrollElement).trigger(u.ACTIVATE,{relatedTarget:e})},h._clear=function(){t(this._selector).filter(d).removeClass(f)},s._jQueryInterface=function(e){return this.each((function(){var i=t(this).data(n);if(i||(i=new s(this,"object"==typeof e&&e),t(this).data(n,i)),"string"==typeof e){if(void 0===i[e])throw new Error('No method named "'+e+'"');i[e]()}}))},o(s,null,[{key:"VERSION",get:function(){return"4.0.0-beta.2"}},{key:"Default",get:function(){return a}}]),s}();return t(window).on(u.LOAD_DATA_API,(function(){for(var e=t.makeArray(t(h)),n=e.length;n--;){var i=t(e[n]);C._jQueryInterface.call(i,i.data())}})),t.fn[e]=C._jQueryInterface,t.fn[e].Constructor=C,t.fn[e].noConflict=function(){return t.fn[e]=s,C._jQueryInterface},C}(),m=function(){var e="tab",n="bs.tab",r="."+n,s=t.fn[e],a={HIDE:"hide"+r,HIDDEN:"hidden"+r,SHOW:"show"+r,SHOWN:"shown"+r,CLICK_DATA_API:"click"+r+".data-api"},l="dropdown-menu",u="active",c="disabled",f="fade",h="show",d=".dropdown",p=".nav, .list-group",g=".active",m="> li > .active",v='[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',y=".dropdown-toggle",b="> .dropdown-menu .active",w=function(){function e(e){this._element=e}var r=e.prototype;return r.show=function(){var e=this;if(!(this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&t(this._element).hasClass(u)||t(this._element).hasClass(c))){var n,r,o=t(this._element).closest(p)[0],s=i.getSelectorFromElement(this._element);if(o){var l="UL"===o.nodeName?m:g;r=(r=t.makeArray(t(o).find(l)))[r.length-1]}var f=t.Event(a.HIDE,{relatedTarget:this._element}),h=t.Event(a.SHOW,{relatedTarget:r});if(r&&t(r).trigger(f),t(this._element).trigger(h),!h.isDefaultPrevented()&&!f.isDefaultPrevented()){s&&(n=t(s)[0]),this._activate(this._element,o);var d=function(){var n=t.Event(a.HIDDEN,{relatedTarget:e._element}),i=t.Event(a.SHOWN,{relatedTarget:r});t(r).trigger(n),t(e._element).trigger(i)};n?this._activate(n,n.parentNode,d):d()}}},r.dispose=function(){t.removeData(this._element,n),this._element=null},r._activate=function(e,n,r){var o=this,s=("UL"===n.nodeName?t(n).find(m):t(n).children(g))[0],a=r&&i.supportsTransitionEnd()&&s&&t(s).hasClass(f),l=function(){return o._transitionComplete(e,s,a,r)};s&&a?t(s).one(i.TRANSITION_END,l).emulateTransitionEnd(150):l(),s&&t(s).removeClass(h)},r._transitionComplete=function(e,n,r,o){if(n){t(n).removeClass(u);var s=t(n.parentNode).find(b)[0];s&&t(s).removeClass(u),"tab"===n.getAttribute("role")&&n.setAttribute("aria-selected",!1)}if(t(e).addClass(u),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!0),r?(i.reflow(e),t(e).addClass(h)):t(e).removeClass(f),e.parentNode&&t(e.parentNode).hasClass(l)){var a=t(e).closest(d)[0];a&&t(a).find(y).addClass(u),e.setAttribute("aria-expanded",!0)}o&&o()},e._jQueryInterface=function(i){return this.each((function(){var r=t(this),o=r.data(n);if(o||(o=new e(this),r.data(n,o)),"string"==typeof i){if(void 0===o[i])throw new Error('No method named "'+i+'"');o[i]()}}))},o(e,null,[{key:"VERSION",get:function(){return"4.0.0-beta.2"}}]),e}();return t(document).on(a.CLICK_DATA_API,v,(function(e){e.preventDefault(),w._jQueryInterface.call(t(this),"show")})),t.fn[e]=w._jQueryInterface,t.fn[e].Constructor=w,t.fn[e].noConflict=function(){return t.fn[e]=s,w._jQueryInterface},w}();return function(){if(void 0===t)throw new Error("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var e=t.fn.jquery.split(" ")[0].split(".");if(e[0]<2&&e[1]<9||1===e[0]&&9===e[1]&&e[2]<1||e[0]>=4)throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}(),e.Util=i,e.Alert=a,e.Button=l,e.Carousel=u,e.Collapse=c,e.Dropdown=f,e.Modal=h,e.Popover=p,e.Scrollspy=g,e.Tab=m,e.Tooltip=d,e}({},$,Popper);
/*!
 * jQuery Mousewheel 3.1.13
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license
 * http://jquery.org/license
 */!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof exports?module.exports=e:e(jQuery)}((function(e){var t,n,i=["wheel","mousewheel","DOMMouseScroll","MozMousePixelScroll"],r="onwheel"in document||document.documentMode>=9?["wheel"]:["mousewheel","DomMouseScroll","MozMousePixelScroll"],o=Array.prototype.slice;if(e.event.fixHooks)for(var s=i.length;s;)e.event.fixHooks[i[--s]]=e.event.mouseHooks;var a=e.event.special.mousewheel={version:"3.1.12",setup:function(){if(this.addEventListener)for(var t=r.length;t;)this.addEventListener(r[--t],l,!1);else this.onmousewheel=l;e.data(this,"mousewheel-line-height",a.getLineHeight(this)),e.data(this,"mousewheel-page-height",a.getPageHeight(this))},teardown:function(){if(this.removeEventListener)for(var t=r.length;t;)this.removeEventListener(r[--t],l,!1);else this.onmousewheel=null;e.removeData(this,"mousewheel-line-height"),e.removeData(this,"mousewheel-page-height")},getLineHeight:function(t){var n=e(t),i=n["offsetParent"in e.fn?"offsetParent":"parent"]();return i.length||(i=e("body")),parseInt(i.css("fontSize"),10)||parseInt(n.css("fontSize"),10)||16},getPageHeight:function(t){return e(t).height()},settings:{adjustOldDeltas:!0,normalizeOffset:!0}};function l(i){var r,s=i||window.event,l=o.call(arguments,1),f=0,h=0,d=0,p=0,g=0;if((i=e.event.fix(s)).type="mousewheel","detail"in s&&(d=-1*s.detail),"wheelDelta"in s&&(d=s.wheelDelta),"wheelDeltaY"in s&&(d=s.wheelDeltaY),"wheelDeltaX"in s&&(h=-1*s.wheelDeltaX),"axis"in s&&s.axis===s.HORIZONTAL_AXIS&&(h=-1*d,d=0),f=0===d?h:d,"deltaY"in s&&(f=d=-1*s.deltaY),"deltaX"in s&&(h=s.deltaX,0===d&&(f=-1*h)),0!==d||0!==h){if(1===s.deltaMode){var m=e.data(this,"mousewheel-line-height");f*=m,d*=m,h*=m}else if(2===s.deltaMode){var v=e.data(this,"mousewheel-page-height");f*=v,d*=v,h*=v}if(r=Math.max(Math.abs(d),Math.abs(h)),(!n||r<n)&&(n=r,c(s,r)&&(n/=40)),c(s,r)&&(f/=40,h/=40,d/=40),f=Math[f>=1?"floor":"ceil"](f/n),h=Math[h>=1?"floor":"ceil"](h/n),d=Math[d>=1?"floor":"ceil"](d/n),a.settings.normalizeOffset&&this.getBoundingClientRect){var y=this.getBoundingClientRect();p=i.clientX-y.left,g=i.clientY-y.top}return i.deltaX=h,i.deltaY=d,i.deltaFactor=n,i.offsetX=p,i.offsetY=g,i.deltaMode=0,l.unshift(i,f,h,d),t&&clearTimeout(t),t=setTimeout(u,200),(e.event.dispatch||e.event.handle).apply(this,l)}}function u(){n=null}function c(e,t){return a.settings.adjustOldDeltas&&"mousewheel"===e.type&&t%120==0}e.fn.extend({mousewheel:function(e){return e?this.bind("mousewheel",e):this.trigger("mousewheel")},unmousewheel:function(e){return this.unbind("mousewheel",e)}})})),function(e,t){if("function"==typeof define&&define.amd)define(["jquery"],t);else if("undefined"!=typeof exports)t(require("jquery"));else{t(e.jQuery),e.jqueryAsScrollbarEs={}}}(this,(function(e){"use strict";var t,n=(t=e)&&t.__esModule?t:{default:t};var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),o={namespace:"asScrollbar",skin:null,handleSelector:null,handleTemplate:'<div class="{{handle}}"></div>',barClass:null,handleClass:null,disabledClass:"is-disabled",draggingClass:"is-dragging",hoveringClass:"is-hovering",direction:"vertical",barLength:null,handleLength:null,minHandleLength:30,maxHandleLength:null,mouseDrag:!0,touchDrag:!0,pointerDrag:!0,clickMove:!0,clickMoveStep:.3,mousewheel:!0,mousewheelSpeed:50,keyboard:!0,useCssTransforms3d:!0,useCssTransforms:!0,useCssTransitions:!0,duration:"500",easing:"ease"},s=function(e,t,n,i){var r=function(e,t){return 1-3*t+3*e},o=function(e,t){return 3*t-6*e},s=function(e){return 3*e},a=function(e,t,n){return((r(t,n)*e+o(t,n))*e+s(t))*e};return e===t&&n===i?{css:"linear",fn:function(e){return e}}:{css:"cubic-bezier("+e+","+t+","+n+","+i+")",fn:function(l){return a(function(t){for(var i,l,u,c=t,f=0;f<4;++f){var h=(i=c,3*r(l=e,u=n)*i*i+2*o(l,u)*i+s(l));if(0===h)return c;c-=(a(c,e,n)-t)/h}return c}(l),t,i)}}},a={ease:s(.25,.1,.25,1),linear:s(0,0,1,1),"ease-in":s(.42,0,1,1),"ease-out":s(0,0,.58,1),"ease-in-out":s(.42,0,.58,1)};Date.now||(Date.now=function(){return(new Date).getTime()});for(var l=["webkit","moz"],u=0;u<l.length&&!window.requestAnimationFrame;++u){var c=l[u];window.requestAnimationFrame=window[c+"RequestAnimationFrame"],window.cancelAnimationFrame=window[c+"CancelAnimationFrame"]||window[c+"CancelRequestAnimationFrame"]}if(/iP(ad|hone|od).*OS (6|7|8)/.test(window.navigator.userAgent)||!window.requestAnimationFrame||!window.cancelAnimationFrame){var f=0;window.requestAnimationFrame=function(e){var t=p(),n=Math.max(f+16,t);return setTimeout((function(){e(f=n)}),n-t)},window.cancelAnimationFrame=clearTimeout}function h(e){return"string"==typeof e&&-1!==e.indexOf("%")}function d(e){return parseFloat(e.slice(0,-1)/100,10)}function p(){return void 0!==window.performance&&window.performance.now?window.performance.now():Date.now()}var g={};!function(e){var t={transition:{end:{WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd",transition:"transitionend"}},animation:{end:{WebkitAnimation:"webkitAnimationEnd",MozAnimation:"animationend",OAnimation:"oAnimationEnd",animation:"animationend"}}},i=["webkit","Moz","O","ms"],r=(0,n.default)("<support>").get(0).style,o=function(){return Boolean(u("transform"))},s=function(){return Boolean(u("perspective"))},a=function(){return Boolean(u("transition"))},l=function(){return Boolean(u("animation"))},u=function(e,t){var o=!1,s=e.charAt(0).toUpperCase()+e.slice(1);return void 0!==r[e]&&(o=e),o||n.default.each(i,(function(e,t){return void 0===r[t+s]||(o="-"+t.toLowerCase()+"-"+s,!1)})),t?o:!!o},c=function(e){return u(e,!0)};a()&&(e.transition=new String(c("transition")),e.transition.end=t.transition.end[e.transition]),l()&&(e.animation=new String(c("animation")),e.animation.end=t.animation.end[e.animation]),o()&&(e.transform=new String(c("transform")),e.transform3d=s()),"ontouchstart"in window||window.DocumentTouch&&document instanceof window.DocumentTouch?e.touch=!0:e.touch=!1,window.PointerEvent||window.MSPointerEvent?e.pointer=!0:e.pointer=!1,e.prefixPointerEvent=function(e){return window.MSPointerEvent?"MSPointer"+e.charAt(9).toUpperCase()+e.substr(10):e}}(g);var m=function(){function e(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.$bar=(0,n.default)(t),i=this.options=n.default.extend({},o,i,this.$bar.data("options")||{}),t.direction=this.options.direction,this.classes={directionClass:i.namespace+"-"+i.direction,barClass:i.barClass?i.barClass:i.namespace,handleClass:i.handleClass?i.handleClass:i.namespace+"-handle"},"vertical"===this.options.direction?this.attributes={axis:"Y",position:"top",length:"height",clientLength:"clientHeight"}:"horizontal"===this.options.direction&&(this.attributes={axis:"X",position:"left",length:"width",clientLength:"clientWidth"}),this._states={},this._drag={time:null,pointer:null},this._frameId=null,this.handlePosition=0,this.easing=a[this.options.easing]||a.ease,this.init()}return r(e,[{key:"init",value:function(){var e=this.options;this.$handle=this.$bar.find(this.options.handleSelector),0===this.$handle.length?this.$handle=(0,n.default)(e.handleTemplate.replace(/\{\{handle\}\}/g,this.classes.handleClass)).appendTo(this.$bar):this.$handle.addClass(this.classes.handleClass),this.$bar.addClass(this.classes.barClass).addClass(this.classes.directionClass).attr("draggable",!1),e.skin&&this.$bar.addClass(e.skin),null!==e.barLength&&this.setBarLength(e.barLength),null!==e.handleLength&&this.setHandleLength(e.handleLength),this.updateLength(),this.bindEvents(),this.trigger("ready")}},{key:"trigger",value:function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];var r=[this].concat(n);this.$bar.trigger("asScrollbar::"+e,r);var o="on"+(e=e.replace(/\b\w+\b/g,(function(e){return e.substring(0,1).toUpperCase()+e.substring(1)})));"function"==typeof this.options[o]&&this.options[o].apply(this,n)}},{key:"is",value:function(e){return this._states[e]&&this._states[e]>0}},{key:"enter",value:function(e){void 0===this._states[e]&&(this._states[e]=0),this._states[e]++}},{key:"leave",value:function(e){this._states[e]--}},{key:"eventName",value:function(e){if("string"!=typeof e||""===e)return"."+this.options.namespace;for(var t=(e=e.split(" ")).length,n=0;n<t;n++)e[n]=e[n]+"."+this.options.namespace;return e.join(" ")}},{key:"bindEvents",value:function(){var e=this;this.options.mouseDrag&&(this.$handle.on(this.eventName("mousedown"),n.default.proxy(this.onDragStart,this)),this.$handle.on(this.eventName("dragstart selectstart"),(function(){return!1}))),this.options.touchDrag&&g.touch&&(this.$handle.on(this.eventName("touchstart"),n.default.proxy(this.onDragStart,this)),this.$handle.on(this.eventName("touchcancel"),n.default.proxy(this.onDragEnd,this))),this.options.pointerDrag&&g.pointer&&(this.$handle.on(this.eventName(g.prefixPointerEvent("pointerdown")),n.default.proxy(this.onDragStart,this)),this.$handle.on(this.eventName(g.prefixPointerEvent("pointercancel")),n.default.proxy(this.onDragEnd,this))),this.options.clickMove&&this.$bar.on(this.eventName("mousedown"),n.default.proxy(this.onClick,this)),this.options.mousewheel&&this.$bar.on("mousewheel",(function(t){var n=void 0;"vertical"===e.options.direction?n=t.deltaFactor*t.deltaY:"horizontal"===e.options.direction&&(n=-1*t.deltaFactor*t.deltaX);var i=e.getHandlePosition();return i<=0&&n>0||(i>=e.barLength&&n<0||(i-=e.options.mousewheelSpeed*n,e.move(i,!0),!1))})),this.$bar.on(this.eventName("mouseenter"),(function(){e.$bar.addClass(e.options.hoveringClass),e.enter("hovering"),e.trigger("hover")})),this.$bar.on(this.eventName("mouseleave"),(function(){e.$bar.removeClass(e.options.hoveringClass),e.is("hovering")&&(e.leave("hovering"),e.trigger("hovered"))})),this.options.keyboard&&(0,n.default)(document).on(this.eventName("keydown"),(function(t){if((!t.isDefaultPrevented||!t.isDefaultPrevented())&&e.is("hovering")){for(var i=document.activeElement;i.shadowRoot;)i=i.shadowRoot.activeElement;if(!(0,n.default)(i).is(":input,select,option,[contenteditable]")){var r=0,o=null;switch(t.which){case 37:case 63232:case 38:case 63233:r=-30;break;case 39:case 63234:case 40:case 63235:r=30;break;case 33:case 63276:case 32:case 34:case 63277:r=-90;break;case 35:case 63275:o="100%";break;case 36:case 63273:o=0;break;default:return}(r||null!==o)&&(r?e.moveBy(r,!0):null!==o&&e.moveTo(o,!0),t.preventDefault())}}}))}},{key:"onClick",value:function(e){if(3!==e.which&&e.target!==this.$handle[0]){this._drag.time=(new Date).getTime(),this._drag.pointer=this.pointer(e);var t=this.$handle.offset(),n=this.distance({x:t.left,y:t.top},this._drag.pointer),i=1;n>0?n-=this.handleLength:(n=Math.abs(n),i=-1),n>this.barLength*this.options.clickMoveStep&&(n=this.barLength*this.options.clickMoveStep),this.moveBy(i*n,!0)}}},{key:"onDragStart",value:function(e){var t=this;if(3!==e.which){this.$bar.addClass(this.options.draggingClass),this._drag.time=(new Date).getTime(),this._drag.pointer=this.pointer(e);var i=function(){t.enter("dragging"),t.trigger("drag")};this.options.mouseDrag&&((0,n.default)(document).on(this.eventName("mouseup"),n.default.proxy(this.onDragEnd,this)),(0,n.default)(document).one(this.eventName("mousemove"),n.default.proxy((function(){(0,n.default)(document).on(t.eventName("mousemove"),n.default.proxy(t.onDragMove,t)),i()}),this))),this.options.touchDrag&&g.touch&&((0,n.default)(document).on(this.eventName("touchend"),n.default.proxy(this.onDragEnd,this)),(0,n.default)(document).one(this.eventName("touchmove"),n.default.proxy((function(){(0,n.default)(document).on(t.eventName("touchmove"),n.default.proxy(t.onDragMove,t)),i()}),this))),this.options.pointerDrag&&g.pointer&&((0,n.default)(document).on(this.eventName(g.prefixPointerEvent("pointerup")),n.default.proxy(this.onDragEnd,this)),(0,n.default)(document).one(this.eventName(g.prefixPointerEvent("pointermove")),n.default.proxy((function(){(0,n.default)(document).on(t.eventName(g.prefixPointerEvent("pointermove")),n.default.proxy(t.onDragMove,t)),i()}),this))),(0,n.default)(document).on(this.eventName("blur"),n.default.proxy(this.onDragEnd,this))}}},{key:"onDragMove",value:function(e){var t=this.distance(this._drag.pointer,this.pointer(e));this.is("dragging")&&(e.preventDefault(),this.moveBy(t,!0))}},{key:"onDragEnd",value:function(){(0,n.default)(document).off(this.eventName("mousemove mouseup touchmove touchend pointermove pointerup MSPointerMove MSPointerUp blur")),this.$bar.removeClass(this.options.draggingClass),this.handlePosition=this.getHandlePosition(),this.is("dragging")&&(this.leave("dragging"),this.trigger("dragged"))}},{key:"pointer",value:function(e){var t={x:null,y:null};return(e=(e=e.originalEvent||e||window.event).touches&&e.touches.length?e.touches[0]:e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:e).pageX?(t.x=e.pageX,t.y=e.pageY):(t.x=e.clientX,t.y=e.clientY),t}},{key:"distance",value:function(e,t){return"vertical"===this.options.direction?t.y-e.y:t.x-e.x}},{key:"setBarLength",value:function(e,t){void 0!==e&&this.$bar.css(this.attributes.length,e),!1!==t&&this.updateLength()}},{key:"setHandleLength",value:function(e,t){void 0!==e&&(e<this.options.minHandleLength?e=this.options.minHandleLength:this.options.maxHandleLength&&e>this.options.maxHandleLength&&(e=this.options.maxHandleLength),this.$handle.css(this.attributes.length,e),!1!==t&&this.updateLength(e))}},{key:"updateLength",value:function(e,t){this.handleLength=void 0!==e?e:this.getHandleLenght(),this.barLength=void 0!==t?t:this.getBarLength()}},{key:"getBarLength",value:function(){return this.$bar[0][this.attributes.clientLength]}},{key:"getHandleLenght",value:function(){return this.$handle[0][this.attributes.clientLength]}},{key:"getHandlePosition",value:function(){var e=void 0;if(this.options.useCssTransforms&&g.transform){if(!(e=function(e){return!(!e||"matrix"!==e.substr(0,6))&&e.replace(/^.*\((.*)\)$/g,"$1").replace(/px/g,"").split(/, +/)}(this.$handle.css(g.transform))))return 0;e="X"===this.attributes.axis?e[12]||e[4]:e[13]||e[5]}else e=this.$handle.css(this.attributes.position);return parseFloat(e.replace("px",""))}},{key:"makeHandlePositionStyle",value:function(e){var t=void 0,n="0",i="0";this.options.useCssTransforms&&g.transform?("X"===this.attributes.axis?n=e+"px":i=e+"px",t=g.transform.toString(),e=this.options.useCssTransforms3d&&g.transform3d?"translate3d("+n+","+i+",0)":"translate("+n+","+i+")"):t=this.attributes.position;var r={};return r[t]=e,r}},{key:"setHandlePosition",value:function(e){var t=this.makeHandlePositionStyle(e);this.$handle.css(t),this.is("dragging")||(this.handlePosition=parseFloat(e))}},{key:"moveTo",value:function(e,t,n){var r=void 0===e?"undefined":i(e);"string"===r&&(h(e)&&(e=d(e)*(this.barLength-this.handleLength)),e=parseFloat(e),r="number"),"number"===r&&this.move(e,t,n)}},{key:"moveBy",value:function(e,t,n){var r=void 0===e?"undefined":i(e);"string"===r&&(h(e)&&(e=d(e)*(this.barLength-this.handleLength)),e=parseFloat(e),r="number"),"number"===r&&this.move(this.handlePosition+e,t,n)}},{key:"move",value:function(e,t,n){"number"!=typeof e||this.is("disabled")||(e<0?e=0:e+this.handleLength>this.barLength&&(e=this.barLength-this.handleLength),this.is("dragging")||!0===n?(this.setHandlePosition(e),t&&this.trigger("change",e/(this.barLength-this.handleLength))):this.doMove(e,this.options.duration,this.options.easing,t))}},{key:"doMove",value:function(e,t,n,i){var r=this,o=void 0;this.enter("moving"),t=t||this.options.duration,n=n||this.options.easing;var s=this.makeHandlePositionStyle(e);for(o in s)if({}.hasOwnProperty.call(s,o))break;if(this.options.useCssTransitions&&g.transition)this.enter("transition"),this.prepareTransition(o,t,n),this.$handle.one(g.transition.end,(function(){r.$handle.css(g.transition,""),i&&r.trigger("change",e/(r.barLength-r.handleLength)),r.leave("transition"),r.leave("moving")})),this.setHandlePosition(e);else{this.enter("animating");var a=p(),l=this.getHandlePosition(),u=e;this._frameId=window.requestAnimationFrame((function e(t){var n=(t-a)/r.options.duration;n>1&&(n=1),n=r.easing.fn(n);var o=parseFloat(l+n*(u-l),10);r.setHandlePosition(o),i&&r.trigger("change",o/(r.barLength-r.handleLength)),1===n?(window.cancelAnimationFrame(r._frameId),r._frameId=null,r.leave("animating"),r.leave("moving")):r._frameId=window.requestAnimationFrame(e)}))}}},{key:"prepareTransition",value:function(e,t,i,r){var o=[];e&&o.push(e),t&&(n.default.isNumeric(t)&&(t+="ms"),o.push(t)),i?o.push(i):o.push(this.easing.css),r&&o.push(r),this.$handle.css(g.transition,o.join(" "))}},{key:"enable",value:function(){this._states.disabled=0,this.$bar.removeClass(this.options.disabledClass),this.trigger("enable")}},{key:"disable",value:function(){this._states.disabled=1,this.$bar.addClass(this.options.disabledClass),this.trigger("disable")}},{key:"destroy",value:function(){this.$handle.removeClass(this.classes.handleClass),this.$bar.removeClass(this.classes.barClass).removeClass(this.classes.directionClass).attr("draggable",null),this.options.skin&&this.$bar.removeClass(this.options.skin),this.$bar.off(this.eventName()),this.$handle.off(this.eventName()),this.trigger("destroy")}}],[{key:"registerEasing",value:function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];a[e]=s.apply(void 0,n)}},{key:"getEasing",value:function(e){return a[e]}},{key:"setDefaults",value:function(e){n.default.extend(o,n.default.isPlainObject(e)&&e)}}]),e}(),v="asScrollbar",y=n.default.fn.asScrollbar,b=function(e){for(var t=arguments.length,i=Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];if("string"==typeof e){var o=e;if(/^_/.test(o))return!1;if(!/^(get)/.test(o))return this.each((function(){var e=n.default.data(this,v);e&&"function"==typeof e[o]&&e[o].apply(e,i)}));var s=this.first().data(v);if(s&&"function"==typeof s[o])return s[o].apply(s,i)}return this.each((function(){(0,n.default)(this).data(v)||(0,n.default)(this).data(v,new m(this,e))}))};n.default.fn.asScrollbar=b,n.default.asScrollbar=n.default.extend({setDefaults:m.setDefaults,registerEasing:m.registerEasing,getEasing:m.getEasing,noConflict:function(){return n.default.fn.asScrollbar=y,b}},{version:"0.5.7"})})),function(e,t){if("function"==typeof define&&define.amd)define(["jquery"],t);else if("undefined"!=typeof exports)t(require("jquery"));else{t(e.jQuery),e.jqueryAsScrollableEs={}}}(this,(function(e){"use strict";var t,n=(t=e)&&t.__esModule?t:{default:t};var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),o={namespace:"asScrollable",skin:null,contentSelector:null,containerSelector:null,enabledClass:"is-enabled",disabledClass:"is-disabled",draggingClass:"is-dragging",hoveringClass:"is-hovering",scrollingClass:"is-scrolling",direction:"vertical",showOnHover:!0,showOnBarHover:!1,duration:500,easing:"ease-in",responsive:!0,throttle:20,scrollbar:{}};function s(e){return"string"==typeof e&&-1!==e.indexOf("%")}function a(e){return e<0?e=0:e>1&&(e=1),100*parseFloat(e).toFixed(4)+"%"}function l(e){return parseFloat(e.slice(0,-1)/100,10)}var u,c,f,h=(f=void 0,c=window.navigator.userAgent,!!(u=/(?=.+Mac OS X)(?=.+Firefox)/.test(c))&&((f=/Firefox\/\d{2}\./.exec(c))&&(f=f[0].replace(/\D+/g,"")),u&&+f>23)),d="asScrollable",p=0,g=function(){function e(t,i){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.$element=(0,n.default)(t),i=this.options=n.default.extend({},o,i||{},this.$element.data("options")||{}),this.classes={wrap:i.namespace,content:i.namespace+"-content",container:i.namespace+"-container",bar:i.namespace+"-bar",barHide:i.namespace+"-bar-hide",skin:i.skin},this.attributes={vertical:{axis:"Y",overflow:"overflow-y",scroll:"scrollTop",scrollLength:"scrollHeight",pageOffset:"pageYOffset",ffPadding:"padding-right",length:"height",clientLength:"clientHeight",offset:"offsetHeight",crossLength:"width",crossClientLength:"clientWidth",crossOffset:"offsetWidth"},horizontal:{axis:"X",overflow:"overflow-x",scroll:"scrollLeft",scrollLength:"scrollWidth",pageOffset:"pageXOffset",ffPadding:"padding-bottom",length:"width",clientLength:"clientWidth",offset:"offsetWidth",crossLength:"height",crossClientLength:"clientHeight",crossOffset:"offsetHeight"}},this._states={},this.horizontal=null,this.vertical=null,this.$bar=null,this._frameId=null,this._timeoutId=null,this.instanceId=++p,this.easing=n.default.asScrollbar.getEasing(this.options.easing)||n.default.asScrollbar.getEasing("ease"),this.init()}return r(e,[{key:"init",value:function(){var e=this.$element.css("position");switch(this.options.containerSelector?(this.$container=this.$element.find(this.options.containerSelector),this.$wrap=this.$element,"static"===e&&this.$wrap.css("position","relative")):(this.$container=this.$element.wrap("<div>"),this.$wrap=this.$container.parent(),this.$wrap.height(this.$element.height()),"static"!==e?this.$wrap.css("position",e):this.$wrap.css("position","relative")),this.options.contentSelector?this.$content=this.$container.find(this.options.contentSelector):(this.$content=this.$container.wrap("<div>"),this.$container=this.$content.parent()),this.options.direction){case"vertical":this.vertical=!0;break;case"horizontal":this.horizontal=!0;break;case"both":this.horizontal=!0,this.vertical=!0;break;case"auto":var t=this.$element.css("overflow-x"),n=this.$element.css("overflow-y");"scroll"!==t&&"auto"!==t||(this.horizontal=!0),"scroll"!==n&&"auto"!==n||(this.vertical=!0)}(this.vertical||this.horizontal)&&(this.$wrap.addClass(this.classes.wrap),this.$container.addClass(this.classes.container),this.$content.addClass(this.classes.content),this.options.skin&&this.$wrap.addClass(this.classes.skin),this.$wrap.addClass(this.options.enabledClass),this.vertical&&(this.$wrap.addClass(this.classes.wrap+"-vertical"),this.initLayout("vertical"),this.createBar("vertical")),this.horizontal&&(this.$wrap.addClass(this.classes.wrap+"-horizontal"),this.initLayout("horizontal"),this.createBar("horizontal")),this.bindEvents(),this.trigger("ready"))}},{key:"bindEvents",value:function(){var e=this;if(this.options.responsive&&((0,n.default)(window).on(this.eventNameWithId("orientationchange"),(function(){e.update()})),(0,n.default)(window).on(this.eventNameWithId("resize"),this.throttle((function(){e.update()}),this.options.throttle))),this.horizontal||this.vertical){var t=this;this.$wrap.on(this.eventName("mouseenter"),(function(){t.$wrap.addClass(e.options.hoveringClass),t.enter("hovering"),t.trigger("hover")})),this.$wrap.on(this.eventName("mouseleave"),(function(){t.$wrap.removeClass(e.options.hoveringClass),t.is("hovering")&&(t.leave("hovering"),t.trigger("hovered"))})),this.options.showOnHover&&(this.options.showOnBarHover?this.$bar.on("asScrollbar::hover",(function(){t.horizontal&&t.showBar("horizontal"),t.vertical&&t.showBar("vertical")})).on("asScrollbar::hovered",(function(){t.horizontal&&t.hideBar("horizontal"),t.vertical&&t.hideBar("vertical")})):(this.$element.on(d+"::hover",n.default.proxy(this.showBar,this)),this.$element.on(d+"::hovered",n.default.proxy(this.hideBar,this)))),this.$container.on(this.eventName("scroll"),(function(){if(t.horizontal){var e=t.offsetLeft;t.offsetLeft=t.getOffset("horizontal"),e!==t.offsetLeft&&(t.trigger("scroll",t.getPercentOffset("horizontal"),"horizontal"),0===t.offsetLeft&&t.trigger("scrolltop","horizontal"),t.offsetLeft===t.getScrollLength("horizontal")&&t.trigger("scrollend","horizontal"))}if(t.vertical){var n=t.offsetTop;t.offsetTop=t.getOffset("vertical"),n!==t.offsetTop&&(t.trigger("scroll",t.getPercentOffset("vertical"),"vertical"),0===t.offsetTop&&t.trigger("scrolltop","vertical"),t.offsetTop===t.getScrollLength("vertical")&&t.trigger("scrollend","vertical"))}})),this.$element.on(d+"::scroll",(function(e,n,i,r){t.is("scrolling")||(t.enter("scrolling"),t.$wrap.addClass(t.options.scrollingClass)),n.getBarApi(r).moveTo(a(i),!1,!0),clearTimeout(t._timeoutId),t._timeoutId=setTimeout((function(){t.$wrap.removeClass(t.options.scrollingClass),t.leave("scrolling")}),200)})),this.$bar.on("asScrollbar::change",(function(e,n,i){"string"==typeof e.target.direction&&t.scrollTo(e.target.direction,a(i),!1,!0)})),this.$bar.on("asScrollbar::drag",(function(){t.$wrap.addClass(t.options.draggingClass)})).on("asScrollbar::dragged",(function(){t.$wrap.removeClass(t.options.draggingClass)}))}}},{key:"unbindEvents",value:function(){this.$wrap.off(this.eventName()),this.$element.off(d+"::scroll").off(d+"::hover").off(d+"::hovered"),this.$container.off(this.eventName()),(0,n.default)(window).off(this.eventNameWithId())}},{key:"initLayout",value:function(e){"vertical"===e&&this.$container.css("height",this.$wrap.height());var t=this.attributes[e],n=this.$container[0].parentNode[t.crossClientLength],i=this.getBrowserScrollbarWidth(e);this.$content.css(t.crossLength,n+"px"),this.$container.css(t.crossLength,i+n+"px"),0===i&&h&&this.$container.css(t.ffPadding,16)}},{key:"createBar",value:function(e){var t=n.default.extend(this.options.scrollbar,{namespace:this.classes.bar,direction:e,useCssTransitions:!1,keyboard:!1}),i=(0,n.default)("<div>");i.asScrollbar(t),this.options.showOnHover&&i.addClass(this.classes.barHide),i.appendTo(this.$wrap),this["$"+e]=i,null===this.$bar?this.$bar=i:this.$bar=this.$bar.add(i),this.updateBarHandle(e)}},{key:"trigger",value:function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];var r=[this].concat(n);this.$element.trigger(d+"::"+e,r);var o="on"+(e=e.replace(/\b\w+\b/g,(function(e){return e.substring(0,1).toUpperCase()+e.substring(1)})));"function"==typeof this.options[o]&&this.options[o].apply(this,n)}},{key:"is",value:function(e){return this._states[e]&&this._states[e]>0}},{key:"enter",value:function(e){void 0===this._states[e]&&(this._states[e]=0),this._states[e]=1}},{key:"leave",value:function(e){this._states[e]=-1}},{key:"eventName",value:function(e){if("string"!=typeof e||""===e)return"."+this.options.namespace;for(var t=(e=e.split(" ")).length,n=0;n<t;n++)e[n]=e[n]+"."+this.options.namespace;return e.join(" ")}},{key:"eventNameWithId",value:function(e){if("string"!=typeof e||""===e)return"."+this.options.namespace+"-"+this.instanceId;for(var t=(e=e.split(" ")).length,n=0;n<t;n++)e[n]=e[n]+"."+this.options.namespace+"-"+this.instanceId;return e.join(" ")}},{key:"throttle",value:function(e,t){var n=this,i=Date.now||function(){return(new Date).getTime()},r=void 0,o=void 0,s=void 0,a=void 0,l=0,u=function(){l=i(),r=null,a=e.apply(o,s),r||(o=s=null)};return function(){for(var c=arguments.length,f=Array(c),h=0;h<c;h++)f[h]=arguments[h];var d=i(),p=t-(d-l);return o=n,s=f,p<=0||p>t?(r&&(clearTimeout(r),r=null),l=d,a=e.apply(o,s),r||(o=s=null)):r||(r=setTimeout(u,p)),a}}},{key:"getBrowserScrollbarWidth",value:function(e){var t,n=this.attributes[e],i=void 0;return n.scrollbarWidth||((i=(t=document.createElement("div")).style).position="absolute",i.width="100px",i.height="100px",i.overflow="scroll",i.top="-9999px",document.body.appendChild(t),n.scrollbarWidth=t[n.offset]-t[n.clientLength],document.body.removeChild(t)),n.scrollbarWidth}},{key:"getOffset",value:function(e){var t=this.attributes[e],n=this.$container[0];return n[t.pageOffset]||n[t.scroll]}},{key:"getPercentOffset",value:function(e){return this.getOffset(e)/this.getScrollLength(e)}},{key:"getContainerLength",value:function(e){return this.$container[0][this.attributes[e].clientLength]}},{key:"getScrollLength",value:function(e){return this.$content[0][this.attributes[e].scrollLength]-this.getContainerLength(e)}},{key:"scrollTo",value:function(e,t,n,r){var o=void 0===t?"undefined":i(t);"string"===o&&(s(t)&&(t=l(t)*this.getScrollLength(e)),t=parseFloat(t),o="number"),"number"===o&&this.move(e,t,n,r)}},{key:"scrollBy",value:function(e,t,n,r){var o=void 0===t?"undefined":i(t);"string"===o&&(s(t)&&(t=l(t)*this.getScrollLength(e)),t=parseFloat(t),o="number"),"number"===o&&this.move(e,this.getOffset(e)+t,n,r)}},{key:"move",value:function(e,t,n,i){if(!0===this[e]&&"number"==typeof t){this.enter("moving"),t<0?t=0:t>this.getScrollLength(e)&&(t=this.getScrollLength(e));var r=this.attributes[e],o=this,s=function(){o.leave("moving")};if(i)this.$container[0][r.scroll]=t,!1!==n&&this.trigger("change",t/this.getScrollLength(e),e),s();else{this.enter("animating");var a=void 0!==window.performance&&window.performance.now?window.performance.now():Date.now(),l=this.getOffset(e),u=t;this._frameId=window.requestAnimationFrame((function i(c){var f=(c-a)/o.options.duration;f>1&&(f=1),f=o.easing.fn(f);var h=parseFloat(l+f*(u-l),10);o.$container[0][r.scroll]=h,!1!==n&&o.trigger("change",t/o.getScrollLength(e),e),1===f?(window.cancelAnimationFrame(o._frameId),o._frameId=null,o.leave("animating"),s()):o._frameId=window.requestAnimationFrame(i)}))}}}},{key:"scrollXto",value:function(e,t,n){return this.scrollTo("horizontal",e,t,n)}},{key:"scrollYto",value:function(e,t,n){return this.scrollTo("vertical",e,t,n)}},{key:"scrollXby",value:function(e,t,n){return this.scrollBy("horizontal",e,t,n)}},{key:"scrollYby",value:function(e,t,n){return this.scrollBy("vertical",e,t,n)}},{key:"getBar",value:function(e){return e&&this["$"+e]?this["$"+e]:this.$bar}},{key:"getBarApi",value:function(e){return this.getBar(e).data("asScrollbar")}},{key:"getBarX",value:function(){return this.getBar("horizontal")}},{key:"getBarY",value:function(){return this.getBar("vertical")}},{key:"showBar",value:function(e){this.getBar(e).removeClass(this.classes.barHide)}},{key:"hideBar",value:function(e){this.getBar(e).addClass(this.classes.barHide)}},{key:"updateBarHandle",value:function(e){var t=this.getBarApi(e);if(t){var n=this.getContainerLength(e),i=this.getScrollLength(e);i>0?(t.is("disabled")&&t.enable(),t.setHandleLength(t.getBarLength()*n/(i+n),!0)):t.disable()}}},{key:"disable",value:function(){this.is("disabled")||(this.enter("disabled"),this.$wrap.addClass(this.options.disabledClass).removeClass(this.options.enabledClass),this.unbindEvents(),this.unStyle()),this.trigger("disable")}},{key:"enable",value:function(){this.is("disabled")&&(this.leave("disabled"),this.$wrap.addClass(this.options.enabledClass).removeClass(this.options.disabledClass),this.bindEvents(),this.update()),this.trigger("enable")}},{key:"update",value:function(){this.is("disabled")||this.$element.is(":visible")&&(this.vertical&&(this.initLayout("vertical"),this.updateBarHandle("vertical")),this.horizontal&&(this.initLayout("horizontal"),this.updateBarHandle("horizontal")))}},{key:"unStyle",value:function(){this.horizontal&&(this.$container.css({height:"","padding-bottom":""}),this.$content.css({height:""})),this.vertical&&(this.$container.css({width:"",height:"","padding-right":""}),this.$content.css({width:""})),this.options.containerSelector||this.$wrap.css({height:""})}},{key:"destroy",value:function(){this.$wrap.removeClass(this.classes.wrap+"-vertical").removeClass(this.classes.wrap+"-horizontal").removeClass(this.classes.wrap).removeClass(this.options.enabledClass).removeClass(this.classes.disabledClass),this.unStyle(),this.$bar&&this.$bar.remove(),this.unbindEvents(),this.options.containerSelector?this.$container.removeClass(this.classes.container):this.$container.unwrap(),this.options.contentSelector||this.$content.unwrap(),this.$content.removeClass(this.classes.content),this.$element.data(d,null),this.trigger("destroy")}}],[{key:"setDefaults",value:function(e){n.default.extend(o,n.default.isPlainObject(e)&&e)}}]),e}(),m="asScrollable",v=n.default.fn.asScrollable,y=function(e){for(var t=arguments.length,i=Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];if("string"==typeof e){var o=e;if(/^_/.test(o))return!1;if(!/^(get)/.test(o))return this.each((function(){var e=n.default.data(this,m);e&&"function"==typeof e[o]&&e[o].apply(e,i)}));var s=this.first().data(m);if(s&&"function"==typeof s[o])return s[o].apply(s,i)}return this.each((function(){(0,n.default)(this).data(m)||(0,n.default)(this).data(m,new g(this,e))}))};n.default.fn.asScrollable=y,n.default.asScrollable=n.default.extend({setDefaults:g.setDefaults,noConflict:function(){return n.default.fn.asScrollable=v,y}},{version:"0.4.10"})})),function(e,t){if("function"==typeof define&&define.amd)define(["jquery"],t);else if("undefined"!=typeof exports)t(require("jquery"));else{t(e.jQuery),e.jqueryAsHoverScrollEs={}}}(this,(function(e){"use strict";var t,n=(t=e)&&t.__esModule?t:{default:t};var i=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r={namespace:"asHoverScroll",list:"> ul",item:"> li",exception:null,direction:"vertical",fixed:!1,mouseMove:!0,touchScroll:!0,pointerScroll:!0,useCssTransforms:!0,useCssTransforms3d:!0,boundary:10,throttle:20,onEnter:function(){$(this).siblings().removeClass("is-active"),$(this).addClass("is-active")},onLeave:function(){$(this).removeClass("is-active")}},o={};!function(e){var t={transition:{end:{WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd",transition:"transitionend"}},animation:{end:{WebkitAnimation:"webkitAnimationEnd",MozAnimation:"animationend",OAnimation:"oAnimationEnd",animation:"animationend"}}},i=["webkit","Moz","O","ms"],r=(0,n.default)("<support>").get(0).style,o=function(){return Boolean(u("transform"))},s=function(){return Boolean(u("perspective"))},a=function(){return Boolean(u("transition"))},l=function(){return Boolean(u("animation"))},u=function(e,t){var o=!1,s=e.charAt(0).toUpperCase()+e.slice(1);return void 0!==r[e]&&(o=e),o||n.default.each(i,(function(e,t){return void 0===r[t+s]||(o="-"+t.toLowerCase()+"-"+s,!1)})),t?o:!!o},c=function(e){return u(e,!0)};a()&&(e.transition=new String(c("transition")),e.transition.end=t.transition.end[e.transition]),l()&&(e.animation=new String(c("animation")),e.animation.end=t.animation.end[e.animation]),o()&&(e.transform=new String(c("transform")),e.transform3d=s()),"ontouchstart"in window||window.DocumentTouch&&document instanceof window.DocumentTouch?e.touch=!0:e.touch=!1,window.PointerEvent||window.MSPointerEvent?e.pointer=!0:e.pointer=!1,e.convertMatrixToArray=function(e){return!(!e||"matrix"!==e.substr(0,6))&&e.replace(/^.*\((.*)\)$/g,"$1").replace(/px/g,"").split(/, +/)},e.prefixPointerEvent=function(e){return window.MSPointerEvent?"MSPointer"+e.charAt(9).toUpperCase()+e.substr(10):e}}(o);var s="asHoverScroll",a=0,l=function(){function e(t,i){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.element=t,this.$element=(0,n.default)(t),this.options=n.default.extend({},r,i,this.$element.data()),this.$list=(0,n.default)(this.options.list,this.$element),this.classes={disabled:this.options.namespace+"-disabled"},"vertical"===this.options.direction?this.attributes={page:"pageY",axis:"Y",position:"top",length:"height",offset:"offsetTop",client:"clientY",clientLength:"clientHeight"}:"horizontal"===this.options.direction&&(this.attributes={page:"pageX",axis:"X",position:"left",length:"width",offset:"offsetLeft",client:"clientX",clientLength:"clientWidth"}),this._states={},this._scroll={time:null,pointer:null},this.instanceId=++a,this.trigger("init"),this.init()}return i(e,[{key:"init",value:function(){this.initPosition(),this.updateLength(),this.bindEvents()}},{key:"bindEvents",value:function(){var e=this,t=this,i=["enter"],r=[];this.options.mouseMove&&(this.$element.on(this.eventName("mousemove"),n.default.proxy(this.onMove,this)),i.push("mouseenter"),r.push("mouseleave")),this.options.touchScroll&&o.touch&&(this.$element.on(this.eventName("touchstart"),n.default.proxy(this.onScrollStart,this)),this.$element.on(this.eventName("touchcancel"),n.default.proxy(this.onScrollEnd,this))),this.options.pointerScroll&&o.pointer&&(this.$element.on(this.eventName(o.prefixPointerEvent("pointerdown")),n.default.proxy(this.onScrollStart,this)),this.$element.on(this.eventName(o.prefixPointerEvent("pointercancel")),n.default.proxy(this.onScrollEnd,this))),this.$list.on(this.eventName(i.join(" ")),this.options.item,(function(){t.is("scrolling")||t.options.onEnter.call(e)})),this.$list.on(this.eventName(r.join(" ")),this.options.item,(function(){t.is("scrolling")||t.options.onLeave.call(e)})),(0,n.default)(window).on(this.eventNameWithId("orientationchange"),(function(){t.update()})),(0,n.default)(window).on(this.eventNameWithId("resize"),this.throttle((function(){t.update()}),this.options.throttle))}},{key:"unbindEvents",value:function(){this.$element.off(this.eventName()),this.$list.off(this.eventName()),(0,n.default)(window).off(this.eventNameWithId())}},{key:"onScrollStart",value:function(e){var t=this,i=this;if(!(this.is("scrolling")||3===e.which||(0,n.default)(e.target).closest(this.options.exception).length>0)){this._scroll.time=(new Date).getTime(),this._scroll.pointer=this.pointer(e),this._scroll.start=this.getPosition(),this._scroll.moved=!1;var r=function(){t.enter("scrolling"),t.trigger("scroll")};this.options.touchScroll&&o.touch&&((0,n.default)(document).on(this.eventName("touchend"),n.default.proxy(this.onScrollEnd,this)),(0,n.default)(document).one(this.eventName("touchmove"),n.default.proxy((function(){this.is("scrolling")||((0,n.default)(document).on(i.eventName("touchmove"),n.default.proxy(this.onScrollMove,this)),r())}),this))),this.options.pointerScroll&&o.pointer&&((0,n.default)(document).on(this.eventName(o.prefixPointerEvent("pointerup")),n.default.proxy(this.onScrollEnd,this)),(0,n.default)(document).one(this.eventName(o.prefixPointerEvent("pointermove")),n.default.proxy((function(){this.is("scrolling")||((0,n.default)(document).on(i.eventName(o.prefixPointerEvent("pointermove")),n.default.proxy(this.onScrollMove,this)),r())}),this))),(0,n.default)(document).on(this.eventName("blur"),n.default.proxy(this.onScrollEnd,this)),e.preventDefault()}}},{key:"onScrollMove",value:function(e){this._scroll.updated=this.pointer(e);var t=this.distance(this._scroll.pointer,this._scroll.updated);if((Math.abs(this._scroll.pointer.x-this._scroll.updated.x)>10||Math.abs(this._scroll.pointer.y-this._scroll.updated.y)>10)&&(this._scroll.moved=!0),this.is("scrolling")){e.preventDefault();var n=this._scroll.start+t;this.canScroll()&&(n>0?n=0:n<this.containerLength-this.listLength&&(n=this.containerLength-this.listLength),this.updatePosition(n))}}},{key:"onScrollEnd",value:function(e){this._scroll.moved||(0,n.default)(e.target).trigger("tap"),this.options.touchScroll&&o.touch&&(0,n.default)(document).off(this.eventName("touchmove touchend")),this.options.pointerScroll&&o.pointer&&(0,n.default)(document).off(this.eventName(o.prefixPointerEvent("pointermove pointerup"))),(0,n.default)(document).off(this.eventName("blur")),this.leave("scrolling"),this.trigger("scrolled")}},{key:"pointer",value:function(e){var t={x:null,y:null};return(e=this.getEvent(e)).pageX&&!this.options.fixed?(t.x=e.pageX,t.y=e.pageY):(t.x=e.clientX,t.y=e.clientY),t}},{key:"getEvent",value:function(e){return e=(e=e.originalEvent||e||window.event).touches&&e.touches.length?e.touches[0]:e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:e}},{key:"distance",value:function(e,t){return"vertical"===this.options.direction?t.y-e.y:t.x-e.x}},{key:"onMove",value:function(e){if(e=this.getEvent(e),!this.is("scrolling")&&this.isMatchScroll(e)){var t,n=void 0;(t=(e[this.attributes.page]&&!this.options.fixed?e[this.attributes.page]:e[this.attributes.client])-this.element[this.attributes.offset])<this.options.boundary?n=0:(n=(t-this.options.boundary)*this.multiplier)>this.listLength-this.containerLength&&(n=this.listLength-this.containerLength),this.updatePosition(-n)}}},{key:"isMatchScroll",value:function(e){return!(this.is("disabled")||!this.canScroll())&&(!this.options.exception||0===(0,n.default)(e.target).closest(this.options.exception).length)}},{key:"canScroll",value:function(){return this.listLength>this.containerLength}},{key:"getContainerLength",value:function(){return this.element[this.attributes.clientLength]}},{key:"getListhLength",value:function(){return this.$list[0][this.attributes.clientLength]}},{key:"updateLength",value:function(){this.containerLength=this.getContainerLength(),this.listLength=this.getListhLength(),this.multiplier=(this.listLength-this.containerLength)/(this.containerLength-2*this.options.boundary)}},{key:"initPosition",value:function(){var e=this.makePositionStyle(0);this.$list.css(e)}},{key:"getPosition",value:function(){var e=void 0;if(this.options.useCssTransforms&&o.transform){if(!(e=(this.options.useCssTransforms3d&&o.transform3d,o.convertMatrixToArray(this.$list.css(o.transform)))))return 0;e="X"===this.attributes.axis?e[12]||e[4]:e[13]||e[5]}else e=this.$list.css(this.attributes.position);return parseFloat(e.replace("px",""))}},{key:"makePositionStyle",value:function(e){var t=void 0,n="0px",i="0px";this.options.useCssTransforms&&o.transform?("X"===this.attributes.axis?n=e+"px":i=e+"px",t=o.transform.toString(),e=this.options.useCssTransforms3d&&o.transform3d?"translate3d("+n+","+i+",0px)":"translate("+n+","+i+")"):t=this.attributes.position;var r={};return r[t]=e,r}},{key:"updatePosition",value:function(e){var t=this.makePositionStyle(e);this.$list.css(t)}},{key:"update",value:function(){this.is("disabled")||(this.updateLength(),this.canScroll()||this.initPosition())}},{key:"eventName",value:function(e){if("string"!=typeof e||""===e)return"."+s;for(var t=(e=e.split(" ")).length,n=0;n<t;n++)e[n]=e[n]+"."+s;return e.join(" ")}},{key:"eventNameWithId",value:function(e){if("string"!=typeof e||""===e)return"."+this.options.namespace+"-"+this.instanceId;for(var t=(e=e.split(" ")).length,n=0;n<t;n++)e[n]=e[n]+"."+this.options.namespace+"-"+this.instanceId;return e.join(" ")}},{key:"trigger",value:function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];var r=[this].concat(n);this.$element.trigger(s+"::"+e,r);var o="on"+(e=e.replace(/\b\w+\b/g,(function(e){return e.substring(0,1).toUpperCase()+e.substring(1)})));"function"==typeof this.options[o]&&this.options[o].apply(this,n)}},{key:"is",value:function(e){return this._states[e]&&this._states[e]>0}},{key:"enter",value:function(e){void 0===this._states[e]&&(this._states[e]=0),this._states[e]=1}},{key:"leave",value:function(e){this._states[e]=0}},{key:"throttle",value:function(e,t){var n=this,i=Date.now||function(){return(new Date).getTime()},r=void 0,o=void 0,s=void 0,a=void 0,l=0,u=function(){l=i(),r=null,a=e.apply(o,s),r||(o=s=null)};return function(){for(var c=arguments.length,f=Array(c),h=0;h<c;h++)f[h]=arguments[h];var d=i(),p=t-(d-l);return o=n,s=f,p<=0||p>t?(r&&(clearTimeout(r),r=null),l=d,a=e.apply(o,s),r||(o=s=null)):r||(r=setTimeout(u,p)),a}}},{key:"enable",value:function(){this.is("disabled")&&(this.leave("disabled"),this.$element.removeClass(this.classes.disabled),this.bindEvents()),this.trigger("enable")}},{key:"disable",value:function(){this.is("disabled")||(this.enter("disabled"),this.initPosition(),this.$element.addClass(this.classes.disabled),this.unbindEvents()),this.trigger("disable")}},{key:"destroy",value:function(){this.$element.removeClass(this.classes.disabled),this.unbindEvents(),this.$element.data(s,null),this.trigger("destroy")}}],[{key:"setDefaults",value:function(e){n.default.extend(r,n.default.isPlainObject(e)&&e)}}]),e}(),u="asHoverScroll",c=n.default.fn.asHoverScroll,f=function(e){for(var t=arguments.length,i=Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];if("string"==typeof e){var o=e;if(/^_/.test(o))return!1;if(!/^(get)/.test(o))return this.each((function(){var e=n.default.data(this,u);e&&"function"==typeof e[o]&&e[o].apply(e,i)}));var s=this.first().data(u);if(s&&"function"==typeof s[o])return s[o].apply(s,i)}return this.each((function(){(0,n.default)(this).data(u)||(0,n.default)(this).data(u,new l(this,e))}))};n.default.fn.asHoverScroll=f,n.default.asHoverScroll=n.default.extend({setDefaults:l.setDefaults,noConflict:function(){return n.default.fn.asHoverScroll=c,f}},{version:"0.3.6"})}));
