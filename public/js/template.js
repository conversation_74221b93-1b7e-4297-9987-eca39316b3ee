!function(e,t){if("function"==typeof define&&define.amd)define("/Component",["exports","jquery"],t);else if("undefined"!=typeof exports)t(exports,require("jquery"));else{var n={exports:{}};t(n.exports,e.jQuery),e.Component=n.exports}}(this,(function(e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=babelHelpers.interopRequireDefault(t);void 0===Object.assign&&(Object.assign=n.default.extend);var i=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};babelHelpers.classCallCheck(this,e),this.$el=t.$el?t.$el:(0,n.default)(document),this.el=this.$el[0],delete t.$el,this.options=t,this.isProcessed=!1}return babelHelpers.createClass(e,[{key:"initialize",value:function(){}},{key:"process",value:function(){}},{key:"run",value:function(){this.isProcessed||(this.initialize(),this.process()),this.isProcessed=!0}},{key:"triggerResize",value:function(){if(document.createEvent){var e=document.createEvent("Event");e.initEvent("resize",!0,!0),window.dispatchEvent(e)}else{element=document.documentElement;var t=document.createEventObject();element.fireEvent("onresize",t)}}}]),e}();e.default=i})),function(e,t){if("function"==typeof define&&define.amd)define("/Plugin",["exports","jquery"],t);else if("undefined"!=typeof exports)t(exports,require("jquery"));else{var n={exports:{}};t(n.exports,e.jQuery),e.Plugin=n.exports}}(this,(function(e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.pluginFactory=e.getDefaults=e.getPlugin=e.getPluginAPI=e.Plugin=void 0;var n=babelHelpers.interopRequireDefault(t),i={},s={},a=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};babelHelpers.classCallCheck(this,e),this.name=this.getName(),this.$el=t,this.options=n,this.isRendered=!1}return babelHelpers.createClass(e,[{key:"getName",value:function(){return"plugin"}},{key:"render",value:function(){if(!n.default.fn[this.name])return!1;this.$el[this.name](this.options)}},{key:"initialize",value:function(){if(this.isRendered)return!1;this.render(),this.isRendered=!0}}],[{key:"getDefaults",value:function(){return{}}},{key:"register",value:function(t,n){void 0!==n&&(i[t]=n,void 0!==n.api&&e.registerApi(t,n))}},{key:"registerApi",value:function(e,t){var i=t.api();if("string"==typeof i){var a=t.api().split("|"),l=a[0]+".plugin."+e,r=a[1]||"render",u=function(i){var s=(0,n.default)(this),a=s.data("pluginInstance");a||((a=new t(s,n.default.extend(!0,{},o(e),s.data()))).initialize(),s.data("pluginInstance",a)),a[r](i)};s[e]=function(e,t){t?((0,n.default)(t).off(l),(0,n.default)(t).on(l,e,u)):(0,n.default)(e).on(l,u)}}else"function"==typeof i&&(s[e]=i)}}]),e}();function l(e){return void 0!==i[e]?i[e]:(console.warn("Plugin:"+e+" has no warpped class."),!1)}function o(e){var t=l(e);return t?t.getDefaults():{}}e.Plugin=a,e.getPluginAPI=function(e){return void 0===e?s:s[e]},e.getPlugin=l,e.getDefaults=o,e.pluginFactory=function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=l(e);if(s&&void 0===s.api)return new s(t,n.default.extend(!0,{},o(e),i));if(n.default.fn[e]){var r=new a(t,i);return r.getName=function(){return e},r.name=e,r}return void 0!==s.api||console.warn("Plugin:"+e+" script is not loaded."),!1},e.default=a})),function(e,t){if("function"==typeof define&&define.amd)define("/Base",["exports","jquery","Component","Plugin"],t);else if("undefined"!=typeof exports)t(exports,require("jquery"),require("Component"),require("Plugin"));else{var n={exports:{}};t(n.exports,e.jQuery,e.Component,e.Plugin),e.Base=n.exports}}(this,(function(e,t,n,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=babelHelpers.interopRequireDefault(t),a=function(e){function t(){return babelHelpers.classCallCheck(this,t),babelHelpers.possibleConstructorReturn(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return babelHelpers.inherits(t,e),babelHelpers.createClass(t,[{key:"initializePlugins",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];(0,s.default)("[data-plugin]",e||this.$el).each((function(){var e=(0,s.default)(this),t=e.data("plugin"),n=(0,i.pluginFactory)(t,e,e.data());n&&n.initialize()}))}},{key:"initializePluginAPIs",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,t=(0,i.getPluginAPI)();for(var n in t)(0,i.getPluginAPI)(n)("[data-plugin="+n+"]",e)}}]),t}(babelHelpers.interopRequireDefault(n).default);e.default=a})),function(e,t){if("function"==typeof define&&define.amd)define("/Config",["exports"],t);else if("undefined"!=typeof exports)t(exports);else{var n={exports:{}};t(n.exports),e.Config=n.exports}}(this,(function(e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var t={fontFamily:"Noto Sans, sans-serif",primaryColor:"blue",assets:"../assets"};function n(){for(var e=t,n=function(e,t){return e[t]},i=arguments.length,s=Array(i),a=0;a<i;a++)s[a]=arguments[a];for(var l=0;l<s.length;l++){e=n(e,s[l])}return e}function i(e,i){if("primary"===e&&((e=n("primaryColor"))||(e="red")),void 0===t.colors)return null;if(void 0!==t.colors[e]){if(i&&void 0!==t.colors[e][i])return t.colors[e][i];if(void 0===i)return t.colors[e]}return null}e.get=n,e.set=function(e,n){"string"==typeof e&&void 0!==n?t[e]=n:"object"===(void 0===e?"undefined":babelHelpers.typeof(e))&&(t=$.extend(!0,{},t,e))},e.getColor=i,e.colors=function(e,t){return i(e,t)}})),function(e,t){if("function"==typeof define&&define.amd)define("/Section/Menubar",["exports","jquery","Component"],t);else if("undefined"!=typeof exports)t(exports,require("jquery"),require("Component"));else{var n={exports:{}};t(n.exports,e.jQuery,e.Component),e.SectionMenubar=n.exports}}(this,(function(e,t,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=babelHelpers.interopRequireDefault(t),s=babelHelpers.interopRequireDefault(n),a=(0,i.default)("body"),l=(0,i.default)("html"),o=function(){function e(t){babelHelpers.classCallCheck(this,e),this.$el=t,this.native=!1,this.api=null}return babelHelpers.createClass(e,[{key:"init",value:function(){a.is(".site-menubar-native")?this.native=!0:this.api||(this.api=this.$el.asScrollable({namespace:"scrollable",skin:"scrollable-inverse",direction:"vertical",contentSelector:">",containerSelector:">"}).data("asScrollable"))}},{key:"destroy",value:function(){this.api&&(this.api.destroy(),this.api=null)}},{key:"update",value:function(){this.api&&this.api.update()}},{key:"enable",value:function(){this.native||(this.api||this.init(),this.api&&this.api.enable())}},{key:"disable",value:function(){this.api&&this.api.disable()}}]),e}(),r=function(e){function t(){var e;babelHelpers.classCallCheck(this,t);for(var n=arguments.length,i=Array(n),s=0;s<n;s++)i[s]=arguments[s];var a=babelHelpers.possibleConstructorReturn(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(i)));return a.$menuBody=a.$el.children(".site-menubar-body"),a.$menu=a.$el.find("[data-plugin=menu]"),a.type="hide",a}return babelHelpers.inherits(t,e),babelHelpers.createClass(t,[{key:"initialize",value:function(){this.$menuBody.length>0?(this.initialized=!0,this.scrollable=new o(this.$menuBody),l.removeClass("css-menubar").addClass("js-menubar"),this.change(this.type)):this.initialized=!1}},{key:"getMenuApi",value:function(){return this.$menu.data("menuApi")}},{key:"update",value:function(){this.scrollable.update()}},{key:"change",value:function(e){this.initialized&&(this.reset(),this[e]())}},{key:"animate",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){};a.addClass("site-menubar-changing"),e.call(this),this.$el.trigger("changing.site.menubar");var i=this.getMenuApi();i&&i.refresh(),setTimeout((function(){n.call(t),a.removeClass("site-menubar-changing"),t.update(),t.$el.trigger("changed.site.menubar")}),500)}},{key:"reset",value:function(){a.removeClass("site-menubar-hide site-menubar-open")}},{key:"open",value:function(){this.animate((function(){a.addClass("site-menubar-open"),l.addClass("disable-scrolling")}),(function(){this.scrollable.init()})),this.type="open"}},{key:"hide",value:function(){this.animate((function(){a.addClass("site-menubar-hide"),l.removeClass("disable-scrolling")}),(function(){this.scrollable.destroy()})),this.type="hide"}}]),t}(s.default);e.default=r})),function(e,t){if("function"==typeof define&&define.amd)define("/Section/Sidebar",["exports","jquery","Base","Plugin"],t);else if("undefined"!=typeof exports)t(exports,require("jquery"),require("Base"),require("Plugin"));else{var n={exports:{}};t(n.exports,e.jQuery,e.Base,e.Plugin),e.SectionSidebar=n.exports}}(this,(function(e,t,n,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=babelHelpers.interopRequireDefault(t),a=function(e){function t(){return babelHelpers.classCallCheck(this,t),babelHelpers.possibleConstructorReturn(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return babelHelpers.inherits(t,e),babelHelpers.createClass(t,[{key:"process",value:function(){if(void 0!==s.default.slidePanel){var e=this;(0,s.default)(document).on("click",'[data-toggle="site-sidebar"]',(function(){var t=(0,s.default)(this),n="right";(0,s.default)("body").hasClass("site-menubar-flipped")&&(n="left");var a=s.default.extend({},(0,i.getDefaults)("slidePanel"),{direction:n,skin:"site-sidebar",dragTolerance:80,template:function(e){return'<div class="'+e.classes.base+" "+e.classes.base+"-"+e.direction+'">\n\t    <div class="'+e.classes.content+' site-sidebar-content"></div>\n\t    <div class="slidePanel-handler"></div>\n\t    </div>'},afterLoad:function(){var t=this;this.$panel.find(".tab-pane").asScrollable({namespace:"scrollable",contentSelector:"> div",containerSelector:"> div"}),e.initializePlugins(t.$panel),this.$panel.on("shown.bs.tab",(function(){t.$panel.find(".tab-pane.active").asScrollable("update")}))},beforeShow:function(){t.hasClass("active")||t.addClass("active")},afterHide:function(){t.hasClass("active")&&t.removeClass("active")}});if(t.hasClass("active"))s.default.slidePanel.hide();else{var l=t.data("url");l||(l=(l=t.attr("href"))&&l.replace(/.*(?=#[^\s]*$)/,"")),s.default.slidePanel.show({url:l},a)}})),(0,s.default)(document).on("click",'[data-toggle="show-chat"]',(function(){(0,s.default)("#conversation").addClass("active")})),(0,s.default)(document).on("click",'[data-toggle="close-chat"]',(function(){(0,s.default)("#conversation").removeClass("active")}))}}}]),t}(babelHelpers.interopRequireDefault(n).default);e.default=a})),function(e,t){if("function"==typeof define&&define.amd)define("/Section/PageAside",["exports","jquery","Component"],t);else if("undefined"!=typeof exports)t(exports,require("jquery"),require("Component"));else{var n={exports:{}};t(n.exports,e.jQuery,e.Component),e.SectionPageAside=n.exports}}(this,(function(e,t,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=babelHelpers.interopRequireDefault(t),s=babelHelpers.interopRequireDefault(n),a=(0,i.default)("body"),l=function(e){function t(){var e;babelHelpers.classCallCheck(this,t);for(var n=arguments.length,i=Array(n),s=0;s<n;s++)i[s]=arguments[s];var a=babelHelpers.possibleConstructorReturn(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(i)));return a.$scroll=a.$el.find(".page-aside-scroll"),a.scrollable=a.$scroll.asScrollable({namespace:"scrollable",contentSelector:"> [data-role='content']",containerSelector:"> [data-role='container']"}).data("asScrollable"),a}return babelHelpers.inherits(t,e),babelHelpers.createClass(t,[{key:"process",value:function(){var e=this;(a.is(".page-aside-fixed")||a.is(".page-aside-scroll"))&&this.$el.on("transitionend",(function(){e.scrollable.update()})),Breakpoints.on("change",(function(){var t=Breakpoints.current().name;a.is(".page-aside-fixed")||a.is(".page-aside-scroll")||("xs"===t?(e.scrollable.enable(),e.$el.on("transitionend",(function(){e.scrollable.update()}))):(e.$el.off("transitionend"),e.scrollable.disable()))})),(0,i.default)(document).on("click.pageAsideScroll",".page-aside-switch",(function(){e.$el.hasClass("open")?e.$el.removeClass("open"):(e.scrollable.update(),e.$el.addClass("open"))})),(0,i.default)(document).on("click.pageAsideScroll",'[data-toggle="collapse"]',(function(t){var n=(0,i.default)(t.target);n.is('[data-toggle="collapse"]')||(n=n.parents('[data-toggle="collapse"]'));var s=void 0,a=n.attr("data-target")||(s=n.attr("href"))&&s.replace(/.*(?=#[^\s]+$)/,"");"site-navbar-collapse"===(0,i.default)(a).attr("id")&&e.scrollable.update()}))}}]),t}(s.default);e.default=l})),function(e,t){if("function"==typeof define&&define.amd)define("/Plugin/menu",["Plugin"],t);else if("undefined"!=typeof exports)t(require("Plugin"));else{t(e.Plugin),e.PluginMenu={}}}(this,(function(e){"use strict";var t=babelHelpers.interopRequireDefault(e),n="menu",i=function(){function e(t,n){babelHelpers.classCallCheck(this,e),this.$el=t,this.light=n,this.built=!1,this.init()}return babelHelpers.createClass(e,[{key:"init",value:function(){this.$el.asScrollable({namespace:"scrollable",skin:"",direction:"vertical",contentSelector:">",containerSelector:">"}),this.built=!0}},{key:"update",value:function(e){void 0!==e?$(e).data("asScrollable").update():this.$el.each((function(){$(this).data("asScrollable").update()}))}},{key:"enable",value:function(){this.$el.each((function(){$(this).data("asScrollable").enable()}))}},{key:"disable",value:function(){this.$el.each((function(){$(this).data("asScrollable").disable()}))}},{key:"refresh",value:function(){this.$el.each((function(){$(this).data("asScrollable").update()}))}},{key:"destroy",value:function(){this.$el.each((function(){$(this).data("asScrollable").disable()})),this.built=!1}}]),e}(),s=function(e){function t(){var e;babelHelpers.classCallCheck(this,t);for(var n=arguments.length,i=Array(n),s=0;s<n;s++)i[s]=arguments[s];var a=babelHelpers.possibleConstructorReturn(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(i)));return a.$scrollItems=a.$el.find(".site-menu-scroll-wrap"),a}return babelHelpers.inherits(t,e),babelHelpers.createClass(t,[{key:"getName",value:function(){return n}},{key:"render",value:function(){this.bindEvents(),this.bindResize(),"xs"!==Breakpoints.current().name&&(this.scrollable=new i(this.$scrollItems,this.options.light)),this.$el.data("menuApi",this)}},{key:"globalClick",value:function(e){switch(e){case"on":$(document).on("click.site.menu",(function(e){$('.dropdown > [data-dropdown-toggle="true"]').length>0&&0===$(e.target).closest(".dropdown-menu").length&&$('.dropdown > [data-dropdown-toggle="true"]').attr("data-dropdown-toggle","false").closest(".dropdown").removeClass("open")}));break;case"off":$(document).off("click.site.menu")}}},{key:"open",value:function(e){e.is(".dropdown")&&($('[data-dropdown-toggle="true"]').attr("data-dropdown-toggle","false").closest(".dropdown").removeClass("open"),e.find('>[data-toggle="dropdown"]').attr("data-dropdown-toggle","true")),e.addClass("open")}},{key:"close",value:function(e){e.removeClass("open"),e.is(".dropdown")&&e.find('>[data-toggle="dropdown"]').attr("data-dropdown-toggle","false")}},{key:"reset",value:function(){$('.dropdown > [data-dropdown-toggle="true"]').attr("data-dropdown-toggle","false").closest(".dropdown").removeClass("open")}},{key:"bindEvents",value:function(){var e=this;"xs"!==Breakpoints.current().name&&this.globalClick("on"),this.$el.on("open.site.menu",".site-menu-item",(function(t){var n=$(this);"xs"===Breakpoints.current().name?e.expand(n,(function(){e.open(n)})):e.open(n),e.options.accordion&&n.siblings(".open").trigger("close.site.menu"),t.stopPropagation()})).on("close.site.menu",".site-menu-item.open",(function(t){var n=$(this);"xs"===Breakpoints.current().name?e.collapse(n,(function(){e.close(n)})):e.close(n),t.stopPropagation()})).on("click.site.menu ",".site-menu-item",(function(t){var n=$(this);n.is(".has-sub")&&$(t.target).closest(".site-menu-item").is(this)&&(n.is(".open")?n.trigger("close.site.menu"):n.trigger("open.site.menu")),"xs"===Breakpoints.current().name?t.stopPropagation():(n.is(".dropdown")&&t.stopPropagation(),1===$(t.target).closest(".site-menu-scroll-wrap").length&&(e.scrollable.update($(t.target).closest(".site-menu-scroll-wrap")),t.stopPropagation()))}))}},{key:"bindResize",value:function(){var e=this,t=Breakpoints.current().name;Breakpoints.on("change",(function(){var n=Breakpoints.current().name;e.reset(),"xs"===n?(e.globalClick("off"),e.scrollable.destroy(),e.$el.off("click.site.menu.scroll")):"xs"===t&&(e.scrollable||(e.scrollable=new i(e.$scrollItems,e.options.light)),e.scrollable.built||e.scrollable.init(),e.scrollable.enable(),e.globalClick("off"),e.globalClick("on"),$(".site-menu .scrollable-container",e.$el).css({height:"",width:""}),e.$el.one("click.site.menu.scroll",".site-menu-item",(function(){e.scrollable.refresh()}))),t=n}))}},{key:"collapse",value:function(e,t){var n=this;$($("> .site-menu-sub",e)[0]||$("> .dropdown-menu",e)[0]||$("> .site-menu-scroll-wrap",e)[0]).show().slideUp(this.options.speed,(function(){$(this).css("display",""),$(this).find("> .site-menu-item").removeClass("is-shown"),t&&t(),n.$el.trigger("collapsed.site.menu")}))}},{key:"expand",value:function(e,t){var n=this,i=$($("> .site-menu-sub",e)[0]||$("> .dropdown-menu",e)[0]||$("> .site-menu-scroll-wrap",e)[0]),s=i.is(".site-menu-sub")?i.children(".site-menu-item").addClass("is-hidden"):$(i.find(".site-menu-sub")[0]).addClass("is-hidden");i.hide().slideDown(this.options.speed,(function(){$(this).css("display",""),t&&t(),n.$el.trigger("expanded.site.menu")})),setTimeout((function(){s.addClass("is-shown"),s.removeClass("is-hidden")}),0)}},{key:"refresh",value:function(){this.$el.find(".open").filter(":not(.active)").removeClass("open")}}],[{key:"getDefaults",value:function(){return{speed:250,accordion:!0}}}]),t}(t.default);t.default.register(n,s)}));
