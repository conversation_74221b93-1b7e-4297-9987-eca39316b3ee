!function(t,e){"object"==typeof exports?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t)}(this,(function(t){function e(t){this._targetElement=t,this._introItems=[],this._options={nextLabel:"Next &rarr;",prevLabel:"&larr; Back",skipLabel:"Skip",doneLabel:"Done",hidePrev:!1,hideNext:!1,tooltipPosition:"bottom",tooltipClass:"",highlightClass:"",exitOnEsc:!0,exitOnOverlayClick:!0,showStepNumbers:!0,keyboardNavigation:!0,showButtons:!0,showBullets:!0,showProgress:!1,scrollToElement:!0,scrollTo:"element",scrollPadding:30,overlayOpacity:.8,positionPrecedence:["bottom","top","right","left"],disableInteraction:!1,hintPosition:"top-middle",hintButtonLabel:"Got it",hintAnimation:!0}}function i(t){var e=[],i=this;if(this._options.steps)for(var s=0,o=this._options.steps.length;s<o;s++){var l=n(this._options.steps[s]);if(l.step=e.length+1,"string"==typeof l.element&&(l.element=document.querySelector(l.element)),void 0===l.element||null==l.element){var h=document.querySelector(".introjsFloatingElement");null==h&&((h=document.createElement("div")).className="introjsFloatingElement",document.body.appendChild(h)),l.element=h,l.position="floating"}l.scrollTo=l.scrollTo||this._options.scrollTo,void 0===l.disableInteraction&&(l.disableInteraction=this._options.disableInteraction),null!=l.element&&e.push(l)}else{var u=t.querySelectorAll("*[data-intro]");if(u.length<1)return!1;s=0;for(var d=u.length;s<d;s++){if("none"!=(m=u[s]).style.display){var p=parseInt(m.getAttribute("data-step"),10),f=this._options.disableInteraction;void 0!==m.getAttribute("data-disable-interaction")&&(f=!!m.getAttribute("data-disable-interaction")),p>0&&(e[p-1]={element:m,intro:m.getAttribute("data-intro"),step:parseInt(m.getAttribute("data-step"),10),tooltipClass:m.getAttribute("data-tooltipClass"),highlightClass:m.getAttribute("data-highlightClass"),position:m.getAttribute("data-position")||this._options.tooltipPosition,scrollTo:m.getAttribute("data-scrollTo")||this._options.scrollTo,disableInteraction:f})}}var g=0;for(s=0,d=u.length;s<d;s++){var m;if(null==(m=u[s]).getAttribute("data-step")){for(;void 0!==e[g];)g++;f=this._options.disableInteraction;void 0!==m.getAttribute("data-disable-interaction")&&(f=!!m.getAttribute("data-disable-interaction")),e[g]={element:m,intro:m.getAttribute("data-intro"),step:g+1,tooltipClass:m.getAttribute("data-tooltipClass"),highlightClass:m.getAttribute("data-highlightClass"),position:m.getAttribute("data-position")||this._options.tooltipPosition,scrollTo:m.getAttribute("data-scrollTo")||this._options.scrollTo,disableInteraction:f}}}}for(var v=[],y=0;y<e.length;y++)e[y]&&v.push(e[y]);if((e=v).sort((function(t,e){return t.step-e.step})),i._introItems=e,D.call(i,t)){r.call(i);t.querySelector(".introjs-skipbutton"),t.querySelector(".introjs-nextbutton");i._onKeyDown=function(e){if(27===e.keyCode&&1==i._options.exitOnEsc)c.call(i,t);else if(37===e.keyCode)a.call(i);else if(39===e.keyCode)r.call(i);else if(13===e.keyCode){var n=e.target||e.srcElement;n&&n.className.indexOf("introjs-prevbutton")>0?a.call(i):n&&n.className.indexOf("introjs-skipbutton")>0?(i._introItems.length-1==i._currentStep&&"function"==typeof i._introCompleteCallback&&i._introCompleteCallback.call(i),c.call(i,t)):r.call(i),e.preventDefault?e.preventDefault():e.returnValue=!1}},i._onResize=function(t){i.refresh.call(i)},window.addEventListener?(this._options.keyboardNavigation&&window.addEventListener("keydown",i._onKeyDown,!0),window.addEventListener("resize",i._onResize,!0)):document.attachEvent&&(this._options.keyboardNavigation&&document.attachEvent("onkeydown",i._onKeyDown),document.attachEvent("onresize",i._onResize))}return!1}function n(t){if(null==t||"object"!=typeof t||void 0!==t.nodeType)return t;var e={};for(var i in t)"undefined"!=typeof jQuery&&t[i]instanceof jQuery?e[i]=t[i]:e[i]=n(t[i]);return e}function s(t){this._currentStep=t-2,void 0!==this._introItems&&r.call(this)}function o(t){this._currentStepNumber=t,void 0!==this._introItems&&r.call(this)}function r(){if(this._direction="forward",void 0!==this._currentStepNumber)for(var t=0,e=this._introItems.length;t<e;t++){this._introItems[t].step===this._currentStepNumber&&(this._currentStep=t-1,this._currentStepNumber=void 0)}if(void 0===this._currentStep?this._currentStep=0:++this._currentStep,void 0!==this._introBeforeChangeCallback)var i=this._introBeforeChangeCallback.call(this);if(!1===i)return--this._currentStep,!1;if(this._introItems.length<=this._currentStep)return"function"==typeof this._introCompleteCallback&&this._introCompleteCallback.call(this),void c.call(this,this._targetElement);var n=this._introItems[this._currentStep];y.call(this,n)}function a(){if(this._direction="backward",0===this._currentStep)return!1;if(--this._currentStep,void 0!==this._introBeforeChangeCallback)var t=this._introBeforeChangeCallback.call(this);if(!1===t)return++this._currentStep,!1;var e=this._introItems[this._currentStep];y.call(this,e)}function l(){if(g.call(this,document.querySelector(".introjs-helperLayer")),g.call(this,document.querySelector(".introjs-tooltipReferenceLayer")),g.call(this,document.querySelector(".introjs-disableInteraction")),void 0!==this._currentStep&&null!==this._currentStep){var t=document.querySelector(".introjs-helperNumberLayer"),e=document.querySelector(".introjs-arrow"),i=document.querySelector(".introjs-tooltip");h.call(this,this._introItems[this._currentStep].element,i,e,t)}return T.call(this),this}function c(t,e){var i=!0;if(null!=this._introBeforeExitCallback&&(i=this._introBeforeExitCallback.call(self)),e||!1!==i){var n=t.querySelectorAll(".introjs-overlay");if(n&&n.length>0)for(var s=n.length-1;s>=0;s--){var o=n[s];o.style.opacity=0,setTimeout(function(){this.parentNode&&this.parentNode.removeChild(this)}.bind(o),500)}var r=t.querySelector(".introjs-helperLayer");r&&r.parentNode.removeChild(r);var a=t.querySelector(".introjs-tooltipReferenceLayer");a&&a.parentNode.removeChild(a);var l=t.querySelector(".introjs-disableInteraction");l&&l.parentNode.removeChild(l);var c=document.querySelector(".introjsFloatingElement");c&&c.parentNode.removeChild(c),w();var h=document.querySelectorAll(".introjs-fixParent");if(h&&h.length>0)for(s=h.length-1;s>=0;s--)h[s].className=h[s].className.replace(/introjs-fixParent/g,"").replace(/^\s+|\s+$/g,"");window.removeEventListener?window.removeEventListener("keydown",this._onKeyDown,!0):document.detachEvent&&document.detachEvent("onkeydown",this._onKeyDown),null!=this._introExitCallback&&this._introExitCallback.call(self),this._currentStep=void 0}}function h(t,e,i,n,s){var o,r,a,l,c,h="";if(s=s||!1,e.style.top=null,e.style.right=null,e.style.bottom=null,e.style.left=null,e.style.marginLeft=null,e.style.marginTop=null,i.style.display="inherit",void 0!==n&&null!=n&&(n.style.top=null,n.style.left=null),this._introItems[this._currentStep])switch(h="string"==typeof(o=this._introItems[this._currentStep]).tooltipClass?o.tooltipClass:this._options.tooltipClass,e.className=("introjs-tooltip "+h).replace(/^\s+|\s+$/g,""),"floating"!=(c=this._introItems[this._currentStep].position)&&(c="auto"===c?p.call(this,t,e):p.call(this,t,e,c)),a=z(t),r=z(e),l=k(),c){case"top":if(i.className="introjs-arrow bottom",s)var f=0;else f=15;u(a,f,r,l,e),e.style.bottom=a.height+20+"px";break;case"right":e.style.left=a.width+20+"px",a.top+r.height>l.height?(i.className="introjs-arrow left-bottom",e.style.top="-"+(r.height-a.height-20)+"px"):i.className="introjs-arrow left";break;case"left":s||1!=this._options.showStepNumbers||(e.style.top="15px"),a.top+r.height>l.height?(e.style.top="-"+(r.height-a.height-20)+"px",i.className="introjs-arrow right-bottom"):i.className="introjs-arrow right",e.style.right=a.width+20+"px";break;case"floating":i.style.display="none",e.style.left="50%",e.style.top="50%",e.style.marginLeft="-"+r.width/2+"px",e.style.marginTop="-"+r.height/2+"px",void 0!==n&&null!=n&&(n.style.left="-"+(r.width/2+18)+"px",n.style.top="-"+(r.height/2+18)+"px");break;case"bottom-right-aligned":i.className="introjs-arrow top-right";d(a,0,r,e),e.style.top=a.height+20+"px";break;case"bottom-middle-aligned":i.className="introjs-arrow top-middle";var g=a.width/2-r.width/2;s&&(g+=5),d(a,g,r,e)&&(e.style.right=null,u(a,g,r,l,e)),e.style.top=a.height+20+"px";break;default:i.className="introjs-arrow top",u(a,f=0,r,l,e),e.style.top=a.height+20+"px"}}function u(t,e,i,n,s){return t.left+e+i.width>n.width?(s.style.left=n.width-i.width-t.left+"px",!1):(s.style.left=e+"px",!0)}function d(t,e,i,n){return t.left+t.width-e-i.width<0?(n.style.left=-t.left+"px",!1):(n.style.right=e+"px",!0)}function p(t,e,i){var n=this._options.positionPrecedence.slice(),s=k(),o=z(e).height+10,r=z(e).width+20,a=z(t),l="floating";return a.left+r>s.width||a.left+a.width/2-r<0?(f(n,"bottom"),f(n,"top")):(a.height+a.top+o>s.height&&f(n,"bottom"),a.top-o<0&&f(n,"top")),a.width+a.left+r>s.width&&f(n,"right"),a.left-r<0&&f(n,"left"),n.length>0&&(l=n[0]),i&&"auto"!=i&&n.indexOf(i)>-1&&(l=i),l}function f(t,e){t.indexOf(e)>-1&&t.splice(t.indexOf(e),1)}function g(t){if(t){if(!this._introItems[this._currentStep])return;var e=this._introItems[this._currentStep],i=z(e.element),n=10;$(e.element)?t.className+=" introjs-fixedTooltip":t.className=t.className.replace(" introjs-fixedTooltip",""),"floating"==e.position&&(n=0),t.setAttribute("style","width: "+(i.width+n)+"px; height:"+(i.height+n)+"px; top:"+(i.top-5)+"px;left: "+(i.left-5)+"px;")}}function m(){var t=document.querySelector(".introjs-disableInteraction");null===t&&((t=document.createElement("div")).className="introjs-disableInteraction",this._targetElement.appendChild(t)),g.call(this,t)}function v(t){t.setAttribute("role","button"),t.tabIndex=0}function y(t){void 0!==this._introChangeCallback&&this._introChangeCallback.call(this,t.element);var e=this,i=document.querySelector(".introjs-helperLayer"),n=document.querySelector(".introjs-tooltipReferenceLayer"),s="introjs-helperLayer";z(t.element);if("string"==typeof t.highlightClass&&(s+=" "+t.highlightClass),"string"==typeof this._options.highlightClass&&(s+=" "+this._options.highlightClass),null!=i){var o=n.querySelector(".introjs-helperNumberLayer"),l=n.querySelector(".introjs-tooltiptext"),u=n.querySelector(".introjs-arrow"),d=n.querySelector(".introjs-tooltip"),p=n.querySelector(".introjs-skipbutton"),f=n.querySelector(".introjs-prevbutton"),y=n.querySelector(".introjs-nextbutton");if(i.className=s,d.style.opacity=0,d.style.display="none",null!=o){var x=this._introItems[t.step-2>=0?t.step-2:0];(null!=x&&"forward"==this._direction&&"floating"==x.position||"backward"==this._direction&&"floating"==t.position)&&(o.style.opacity=0)}g.call(e,i),g.call(e,n);var $=document.querySelectorAll(".introjs-fixParent");if($&&$.length>0)for(var k=$.length-1;k>=0;k--)$[k].className=$[k].className.replace(/introjs-fixParent/g,"").replace(/^\s+|\s+$/g,"");w(),e._lastShowElementTimer&&clearTimeout(e._lastShowElementTimer),e._lastShowElementTimer=setTimeout((function(){null!=o&&(o.innerHTML=t.step),l.innerHTML=t.intro,d.style.display="block",h.call(e,t.element,d,u,o),e._options.showBullets&&(n.querySelector(".introjs-bullets li > a.active").className="",n.querySelector('.introjs-bullets li > a[data-stepnumber="'+t.step+'"]').className="active"),n.querySelector(".introjs-progress .introjs-progressbar").setAttribute("style","width:"+U.call(e)+"%;"),d.style.opacity=1,o&&(o.style.opacity=1),void 0!==p&&null!=p&&/introjs-donebutton/gi.test(p.className)?p.focus():void 0!==y&&null!=y&&y.focus(),b.call(e,t.scrollTo,t,l)}),350)}else{var D=document.createElement("div"),S=document.createElement("div"),A=document.createElement("div"),T=document.createElement("div"),E=document.createElement("div"),j=document.createElement("div"),I=document.createElement("div"),O=document.createElement("div");D.className=s,S.className="introjs-tooltipReferenceLayer",g.call(e,D),g.call(e,S),this._targetElement.appendChild(D),this._targetElement.appendChild(S),A.className="introjs-arrow",E.className="introjs-tooltiptext",E.innerHTML=t.intro,j.className="introjs-bullets",!1===this._options.showBullets&&(j.style.display="none");for(var N=document.createElement("ul"),q=(k=0,this._introItems.length);k<q;k++){var M=document.createElement("li"),L=document.createElement("a");L.onclick=function(){e.goToStep(this.getAttribute("data-stepnumber"))},k===t.step-1&&(L.className="active"),v(L),L.innerHTML="&nbsp;",L.setAttribute("data-stepnumber",this._introItems[k].step),M.appendChild(L),N.appendChild(M)}j.appendChild(N),I.className="introjs-progress",!1===this._options.showProgress&&(I.style.display="none");var P=document.createElement("div");if(P.className="introjs-progressbar",P.setAttribute("style","width:"+U.call(this)+"%;"),I.appendChild(P),O.className="introjs-tooltipbuttons",!1===this._options.showButtons&&(O.style.display="none"),T.className="introjs-tooltip",T.appendChild(E),T.appendChild(j),T.appendChild(I),1==this._options.showStepNumbers){var H=document.createElement("span");H.className="introjs-helperNumberLayer",H.innerHTML=t.step,S.appendChild(H)}T.appendChild(A),S.appendChild(T),(y=document.createElement("a")).onclick=function(){e._introItems.length-1!=e._currentStep&&r.call(e)},v(y),y.innerHTML=this._options.nextLabel,(f=document.createElement("a")).onclick=function(){0!=e._currentStep&&a.call(e)},v(f),f.innerHTML=this._options.prevLabel,(p=document.createElement("a")).className="introjs-button introjs-skipbutton",v(p),p.innerHTML=this._options.skipLabel,p.onclick=function(){e._introItems.length-1==e._currentStep&&"function"==typeof e._introCompleteCallback&&e._introCompleteCallback.call(e),c.call(e,e._targetElement)},O.appendChild(p),this._introItems.length>1&&(O.appendChild(f),O.appendChild(y)),T.appendChild(O),h.call(e,t.element,T,A,H),b.call(this,t.scrollTo,t,T)}var R=e._targetElement.querySelector(".introjs-disableInteraction");R&&R.parentNode.removeChild(R),t.disableInteraction&&m.call(e),void 0!==y&&null!=y&&y.removeAttribute("tabIndex"),void 0!==f&&null!=f&&f.removeAttribute("tabIndex"),0==this._currentStep&&this._introItems.length>1?(void 0!==p&&null!=p&&(p.className="introjs-button introjs-skipbutton"),void 0!==y&&null!=y&&(y.className="introjs-button introjs-nextbutton"),1==this._options.hidePrev?(void 0!==f&&null!=f&&(f.className="introjs-button introjs-prevbutton introjs-hidden"),void 0!==y&&null!=y&&(y.className+=" introjs-fullbutton")):void 0!==f&&null!=f&&(f.className="introjs-button introjs-prevbutton introjs-disabled"),void 0!==f&&null!=f&&(f.tabIndex="-1"),void 0!==p&&null!=p&&(p.innerHTML=this._options.skipLabel)):this._introItems.length-1==this._currentStep||1==this._introItems.length?(void 0!==p&&null!=p&&(p.innerHTML=this._options.doneLabel,p.className+=" introjs-donebutton"),void 0!==f&&null!=f&&(f.className="introjs-button introjs-prevbutton"),1==this._options.hideNext?(void 0!==y&&null!=y&&(y.className="introjs-button introjs-nextbutton introjs-hidden"),void 0!==f&&null!=f&&(f.className+=" introjs-fullbutton")):void 0!==y&&null!=y&&(y.className="introjs-button introjs-nextbutton introjs-disabled"),void 0!==y&&null!=y&&(y.tabIndex="-1")):(void 0!==p&&null!=p&&(p.className="introjs-button introjs-skipbutton"),void 0!==f&&null!=f&&(f.className="introjs-button introjs-prevbutton"),void 0!==y&&null!=y&&(y.className="introjs-button introjs-nextbutton"),void 0!==p&&null!=p&&(p.innerHTML=this._options.skipLabel)),void 0!==y&&null!=y&&y.focus(),function(t){if(t.element instanceof SVGElement)for(var e=t.element.parentNode;null!=t.element.parentNode&&e.tagName&&"body"!==e.tagName.toLowerCase();)"svg"===e.tagName.toLowerCase()&&_(e,"introjs-showElement introjs-relativePosition"),e=e.parentNode;_(t.element,"introjs-showElement");var i=C(t.element,"position");"absolute"!==i&&"relative"!==i&&"fixed"!==i&&_(t.element,"introjs-relativePosition");e=t.element.parentNode;for(;null!=e&&e.tagName&&"body"!==e.tagName.toLowerCase();){var n=C(e,"z-index"),s=parseFloat(C(e,"opacity")),o=C(e,"transform")||C(e,"-webkit-transform")||C(e,"-moz-transform")||C(e,"-ms-transform")||C(e,"-o-transform");(/[0-9]+/.test(n)||s<1||"none"!==o&&void 0!==o)&&(e.className+=" introjs-fixParent"),e=e.parentNode}}(t),void 0!==this._introAfterChangeCallback&&this._introAfterChangeCallback.call(this,t.element)}function b(t,e,i){if(this._options.scrollToElement){if("tooltip"===t)var n=i.getBoundingClientRect();else n=e.element.getBoundingClientRect();if(!function(t){var e=t.getBoundingClientRect();return e.top>=0&&e.left>=0&&e.bottom+80<=window.innerHeight&&e.right<=window.innerWidth}(e.element)){var s=k().height,o=n.bottom-(n.bottom-n.top);n.bottom;o<0||e.element.clientHeight>s?window.scrollBy(0,n.top-(s/2-n.height/2)-this._options.scrollPadding):window.scrollBy(0,n.top-(s/2-n.height/2)+this._options.scrollPadding)}}}function w(){for(var t=document.querySelectorAll(".introjs-showElement"),e=0,i=t.length;e<i;e++){x(t[e],/introjs-[a-zA-Z]+/g)}}function _(t,e){if(t instanceof SVGElement){var i=t.getAttribute("class")||"";t.setAttribute("class",i+" "+e)}else t.className+=" "+e}function x(t,e){if(t instanceof SVGElement){var i=t.getAttribute("class")||"";t.setAttribute("class",i.replace(e,"").replace(/^\s+|\s+$/g,""))}else t.className=t.className.replace(e,"").replace(/^\s+|\s+$/g,"")}function C(t,e){var i="";return t.currentStyle?i=t.currentStyle[e]:document.defaultView&&document.defaultView.getComputedStyle&&(i=document.defaultView.getComputedStyle(t,null).getPropertyValue(e)),i&&i.toLowerCase?i.toLowerCase():i}function $(t){var e=t.parentNode;return!(!e||"HTML"===e.nodeName)&&("fixed"==C(t,"position")||$(e))}function k(){if(null!=window.innerWidth)return{width:window.innerWidth,height:window.innerHeight};var t=document.documentElement;return{width:t.clientWidth,height:t.clientHeight}}function D(t){var e=document.createElement("div"),i="",n=this;if(e.className="introjs-overlay",t.tagName&&"body"!==t.tagName.toLowerCase()){var s=z(t);s&&(i+="width: "+s.width+"px; height:"+s.height+"px; top:"+s.top+"px;left: "+s.left+"px;",e.setAttribute("style",i))}else i+="top: 0;bottom: 0; left: 0;right: 0;position: fixed;",e.setAttribute("style",i);return t.appendChild(e),e.onclick=function(){1==n._options.exitOnOverlayClick&&c.call(n,t)},setTimeout((function(){i+="opacity: "+n._options.overlayOpacity.toString()+";",e.setAttribute("style",i)}),10),!0}function S(){var t=this._targetElement.querySelector(".introjs-hintReference");if(t){var e=t.getAttribute("data-step");return t.parentNode.removeChild(t),e}}function A(t){if(this._introItems=[],this._options.hints)for(var e=0,i=this._options.hints.length;e<i;e++){var s=n(this._options.hints[e]);"string"==typeof s.element&&(s.element=document.querySelector(s.element)),s.hintPosition=s.hintPosition||this._options.hintPosition,s.hintAnimation=s.hintAnimation||this._options.hintAnimation,null!=s.element&&this._introItems.push(s)}else{var o=t.querySelectorAll("*[data-hint]");if(o.length<1)return!1;for(e=0,i=o.length;e<i;e++){var r=o[e],a=r.getAttribute("data-hintAnimation");a=a?"true"==a:this._options.hintAnimation,this._introItems.push({element:r,hint:r.getAttribute("data-hint"),hintPosition:r.getAttribute("data-hintPosition")||this._options.hintPosition,hintAnimation:a,tooltipClass:r.getAttribute("data-tooltipClass"),position:r.getAttribute("data-position")||this._options.tooltipPosition})}}M.call(this),document.addEventListener?(document.addEventListener("click",S.bind(this),!1),window.addEventListener("resize",T.bind(this),!0)):document.attachEvent&&(document.attachEvent("onclick",S.bind(this)),document.attachEvent("onresize",T.bind(this)))}function T(){for(var t=0,e=this._introItems.length;t<e;t++){var i=this._introItems[t];void 0!==i.targetElement&&L.call(this,i.hintPosition,i.element,i.targetElement)}}function E(t){S.call(this);var e=this._targetElement.querySelector('.introjs-hint[data-step="'+t+'"]');e&&(e.className+=" introjs-hidehint"),void 0!==this._hintCloseCallback&&this._hintCloseCallback.call(this,t)}function j(){var t=this._targetElement.querySelectorAll(".introjs-hint");if(t&&t.length>0)for(var e=0;e<t.length;e++)E.call(this,t[e].getAttribute("data-step"))}function I(){var t=this._targetElement.querySelectorAll(".introjs-hint");if(t&&t.length>0)for(var e=0;e<t.length;e++)O.call(this,t[e].getAttribute("data-step"));else A.call(this,this._targetElement)}function O(t){var e=this._targetElement.querySelector('.introjs-hint[data-step="'+t+'"]');e&&(e.className=e.className.replace(/introjs\-hidehint/g,""))}function N(){var t=this._targetElement.querySelectorAll(".introjs-hint");if(t&&t.length>0)for(var e=0;e<t.length;e++)q.call(this,t[e].getAttribute("data-step"))}function q(t){var e=this._targetElement.querySelector('.introjs-hint[data-step="'+t+'"]');e&&e.parentNode.removeChild(e)}function M(){var t=this,e=document.querySelector(".introjs-hints");if(null!=e)i=e;else{var i=document.createElement("div");i.className="introjs-hints"}for(var n=0,s=this._introItems.length;n<s;n++){var o=this._introItems[n];if(!document.querySelector('.introjs-hint[data-step="'+n+'"]')){var r=document.createElement("a");v(r),function(e,i,n){e.onclick=function(e){var i=e||window.event;i.stopPropagation&&i.stopPropagation(),null!=i.cancelBubble&&(i.cancelBubble=!0),P.call(t,n)}}(r,0,n),r.className="introjs-hint",o.hintAnimation||(r.className+=" introjs-hint-no-anim"),$(o.element)&&(r.className+=" introjs-fixedhint");var a=document.createElement("div");a.className="introjs-hint-dot";var l=document.createElement("div");l.className="introjs-hint-pulse",r.appendChild(a),r.appendChild(l),r.setAttribute("data-step",n),o.targetElement=o.element,o.element=r,L.call(this,o.hintPosition,r,o.targetElement),i.appendChild(r)}}document.body.appendChild(i),void 0!==this._hintsAddedCallback&&this._hintsAddedCallback.call(this)}function L(t,e,i){var n=z.call(this,i),s=20,o=20;switch(t){default:case"top-left":e.style.left=n.left+"px",e.style.top=n.top+"px";break;case"top-right":e.style.left=n.left+n.width-s+"px",e.style.top=n.top+"px";break;case"bottom-left":e.style.left=n.left+"px",e.style.top=n.top+n.height-o+"px";break;case"bottom-right":e.style.left=n.left+n.width-s+"px",e.style.top=n.top+n.height-o+"px";break;case"middle-left":e.style.left=n.left+"px",e.style.top=n.top+(n.height-o)/2+"px";break;case"middle-right":e.style.left=n.left+n.width-s+"px",e.style.top=n.top+(n.height-o)/2+"px";break;case"middle-middle":e.style.left=n.left+(n.width-s)/2+"px",e.style.top=n.top+(n.height-o)/2+"px";break;case"bottom-middle":e.style.left=n.left+(n.width-s)/2+"px",e.style.top=n.top+n.height-o+"px";break;case"top-middle":e.style.left=n.left+(n.width-s)/2+"px",e.style.top=n.top+"px"}}function P(t){var e=document.querySelector('.introjs-hint[data-step="'+t+'"]'),i=this._introItems[t];void 0!==this._hintClickCallback&&this._hintClickCallback.call(this,e,i,t);var n=S.call(this);if(parseInt(n,10)!=t){var s=document.createElement("div"),o=document.createElement("div"),r=document.createElement("div"),a=document.createElement("div");s.className="introjs-tooltip",s.onclick=function(t){t.stopPropagation?t.stopPropagation():t.cancelBubble=!0},o.className="introjs-tooltiptext";var l=document.createElement("p");l.innerHTML=i.hint;var c=document.createElement("a");c.className="introjs-button",c.innerHTML=this._options.hintButtonLabel,c.onclick=E.bind(this,t),o.appendChild(l),o.appendChild(c),r.className="introjs-arrow",s.appendChild(r),s.appendChild(o),this._currentStep=e.getAttribute("data-step"),a.className="introjs-tooltipReferenceLayer introjs-hintReference",a.setAttribute("data-step",e.getAttribute("data-step")),g.call(this,a),a.appendChild(s),document.body.appendChild(a),h.call(this,e,s,r,null,!0)}}function z(t){var e={},i=document.body,n=document.documentElement,s=window.pageYOffset||n.scrollTop||i.scrollTop,o=window.pageXOffset||n.scrollLeft||i.scrollLeft;if(t instanceof SVGElement){var r=t.getBoundingClientRect();e.top=r.top+s,e.width=r.width,e.height=r.height,e.left=r.left+o}else{e.width=t.offsetWidth,e.height=t.offsetHeight;for(var a=0,l=0;t&&!isNaN(t.offsetLeft)&&!isNaN(t.offsetTop);)a+=t.offsetLeft,l+=t.offsetTop,t=t.offsetParent;e.top=l,e.left=a}return e}function U(){return parseInt(this._currentStep+1,10)/this._introItems.length*100}var H=function(t){if("object"==typeof t)return new e(t);if("string"==typeof t){var i=document.querySelector(t);if(i)return new e(i);throw new Error("There is no element with given selector.")}return new e(document.body)};return H.version="2.8.0-alpha.1",H.fn=e.prototype={clone:function(){return new e(this)},setOption:function(t,e){return this._options[t]=e,this},setOptions:function(t){return this._options=function(t,e){var i={};for(var n in t)i[n]=t[n];for(var n in e)i[n]=e[n];return i}(this._options,t),this},start:function(){return i.call(this,this._targetElement),this},goToStep:function(t){return s.call(this,t),this},addStep:function(t){return this._options.steps||(this._options.steps=[]),this._options.steps.push(t),this},addSteps:function(t){if(t.length){for(var e=0;e<t.length;e++)this.addStep(t[e]);return this}},goToStepNumber:function(t){return o.call(this,t),this},nextStep:function(){return r.call(this),this},previousStep:function(){return a.call(this),this},exit:function(t){return c.call(this,this._targetElement,t),this},refresh:function(){return l.call(this),this},onbeforechange:function(t){if("function"!=typeof t)throw new Error("Provided callback for onbeforechange was not a function");return this._introBeforeChangeCallback=t,this},onchange:function(t){if("function"!=typeof t)throw new Error("Provided callback for onchange was not a function.");return this._introChangeCallback=t,this},onafterchange:function(t){if("function"!=typeof t)throw new Error("Provided callback for onafterchange was not a function");return this._introAfterChangeCallback=t,this},oncomplete:function(t){if("function"!=typeof t)throw new Error("Provided callback for oncomplete was not a function.");return this._introCompleteCallback=t,this},onhintsadded:function(t){if("function"!=typeof t)throw new Error("Provided callback for onhintsadded was not a function.");return this._hintsAddedCallback=t,this},onhintclick:function(t){if("function"!=typeof t)throw new Error("Provided callback for onhintclick was not a function.");return this._hintClickCallback=t,this},onhintclose:function(t){if("function"!=typeof t)throw new Error("Provided callback for onhintclose was not a function.");return this._hintCloseCallback=t,this},onexit:function(t){if("function"!=typeof t)throw new Error("Provided callback for onexit was not a function.");return this._introExitCallback=t,this},onbeforeexit:function(t){if("function"!=typeof t)throw new Error("Provided callback for onbeforeexit was not a function.");return this._introBeforeExitCallback=t,this},addHints:function(){return A.call(this,this._targetElement),this},hideHint:function(t){return E.call(this,t),this},hideHints:function(){return j.call(this),this},showHint:function(t){return O.call(this,t),this},showHints:function(){return I.call(this),this},removeHints:function(){return N.call(this),this},removeHint:function(t){return q.call(this,t),this},showHintDialog:function(t){return P.call(this,t),this}},t.introJs=H,H})),function(t,e){if("function"==typeof define&&define.amd)define(["jquery"],e);else if("undefined"!=typeof exports)e(require("jquery"));else{e(t.jQuery),t.jqueryAsPieProgressEs={}}}(this,(function(t){"use strict";var e,i=(e=t)&&e.__esModule?e:{default:e};var n=function(){function t(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,i,n){return i&&t(e.prototype,i),n&&t(e,n),e}}(),s=function(t,e){var i=document.createElementNS("http://www.w3.org/2000/svg",t);if(!e)return i;for(var n in e)Object.hasOwnProperty.call(e,n)&&i.setAttribute(n,e[n]);return i};Date.now||(Date.now=function(){return(new Date).getTime()});for(var o=["webkit","moz"],r=0;r<o.length&&!window.requestAnimationFrame;++r){var a=o[r];window.requestAnimationFrame=window[a+"RequestAnimationFrame"],window.cancelAnimationFrame=window[a+"CancelAnimationFrame"]||window[a+"CancelRequestAnimationFrame"]}if(/iP(ad|hone|od).*OS (6|7|8)/.test(window.navigator.userAgent)||!window.requestAnimationFrame||!window.cancelAnimationFrame){var l=0;window.requestAnimationFrame=function(t){var e=c(),i=Math.max(l+16,e);return setTimeout((function(){t(l=i)}),i-e)},window.cancelAnimationFrame=clearTimeout}var c=function(){return void 0!==window.performance&&window.performance.now?window.performance.now():Date.now()},h="createElementNS"in document&&new s("svg",{}).createSVGRect,u=function(t,e,i,n){var s=function(t,e){return 1-3*e+3*t},o=function(t,e){return 3*e-6*t},r=function(t){return 3*t},a=function(t,e,i){return((s(e,i)*t+o(e,i))*t+r(e))*t};return t===e&&i===n?{css:"linear",fn:function(t){return t}}:{css:"cubic-bezier("+t+","+e+","+i+","+n+")",fn:function(l){return a(function(e){for(var n,l,c,h=e,u=0;u<4;++u){var d=(n=h,3*s(l=t,c=i)*n*n+2*o(l,c)*n+r(l));if(0===d)return h;h-=(a(h,t,i)-e)/d}return h}(l),e,n)}}},d={ease:u(.25,.1,.25,1),linear:u(0,0,1,1),"ease-in":u(.42,0,1,1),"ease-out":u(0,0,.58,1),"ease-in-out":u(.42,0,.58,1)},p={namespace:"asPieProgress",classes:{svg:"pie_progress__svg",element:"pie_progress",number:"pie_progress__number",content:"pie_progress__content"},min:0,max:100,goal:100,size:160,speed:15,barcolor:"#ef1e25",barsize:"4",trackcolor:"#f2f2f2",fillcolor:"none",easing:"ease",numberCallback:function(t){return Math.round(this.getPercentage(t))+"%"},contentCallback:null},f="asPieProgress",g=function(){function t(e,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.element=e,this.$element=(0,i.default)(e),this.options=i.default.extend(!0,{},p,n,this.$element.data()),this.namespace=this.options.namespace,this.classes=this.options.classes,this.easing=d[this.options.easing]||d.ease,this.$element.addClass(this.classes.element),this.min=this.$element.attr("aria-valuemin"),this.max=this.$element.attr("aria-valuemax"),this.min=this.min?parseInt(this.min,10):this.options.min,this.max=this.max?parseInt(this.max,10):this.options.max,this.first=this.$element.attr("aria-valuenow"),this.first=this.first?parseInt(this.first,10):this.options.first?this.options.first:this.min,this.now=this.first,this.goal=this.options.goal,this._frameId=null,this.initialized=!1,this._trigger("init"),this.init()}return n(t,[{key:"init",value:function(){this.$number=this.$element.find("."+this.classes.number),this.$content=this.$element.find("."+this.classes.content),this.size=this.options.size,this.width=this.size,this.height=this.size,this.prepare(),this.initialized=!0,this._trigger("ready")}},{key:"prepare",value:function(){h&&(this.svg=new s("svg",{version:"1.1",preserveAspectRatio:"xMinYMin meet",viewBox:"0 0 "+this.width+" "+this.height}),this.buildTrack(),this.buildBar(),(0,i.default)('<div class="'+this.classes.svg+'"></div>').append(this.svg).appendTo(this.$element))}},{key:"buildTrack",value:function(){var t=this.size,e=this.size/2,i=t/2,n=this.options.barsize,o=new s("ellipse",{rx:e-n/2,ry:i-n/2,cx:e,cy:i,stroke:this.options.trackcolor,fill:this.options.fillcolor,"stroke-width":n});this.svg.appendChild(o)}},{key:"buildBar",value:function(){if(h){var t=new s("path",{fill:"none","stroke-width":this.options.barsize,stroke:this.options.barcolor});this.bar=t,this.svg.appendChild(t),this._drawBar(this.first),this._updateBar()}}},{key:"_drawBar",value:function(t){if(h){this.barGoal=t;var e=this.size,i=this.size/2,n=e/2,s=this.options.barsize,o=Math.min(i,n)-s/2;this.r=o;var r=this.getPercentage(t);100===r&&(r-=1e-4);var a=0+r*Math.PI*2/100,l=i+o*Math.sin(0),c=i+o*Math.sin(a),u=n-o*Math.cos(0),d=n-o*Math.cos(a),p=0;a-0>Math.PI&&(p=1);var f="M"+l+","+u+" A"+o+","+o+" 0 "+p+" 1 "+c+","+d;this.bar.setAttribute("d",f)}}},{key:"_updateBar",value:function(){if(h){var t=this.getPercentage(this.now),e=this.bar.getTotalLength(),i=e*(1-t/this.getPercentage(this.barGoal));this.bar.style.strokeDasharray=e+" "+e,this.bar.style.strokeDashoffset=i}}},{key:"_trigger",value:function(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];var s=[this].concat(i);this.$element.trigger(f+"::"+t,s);var o="on"+(t=t.replace(/\b\w+\b/g,(function(t){return t.substring(0,1).toUpperCase()+t.substring(1)})));"function"==typeof this.options[o]&&this.options[o].apply(this,i)}},{key:"getPercentage",value:function(t){return 100*(t-this.min)/(this.max-this.min)}},{key:"go",value:function(t){var e,i=this;this._clear(),"string"==typeof(e=t)&&-1!==e.indexOf("%")&&(t=parseInt(t.replace("%",""),10),t=Math.round(this.min+t/100*(this.max-this.min))),void 0===t&&(t=this.goal),t>this.max?t=this.max:t<this.min&&(t=this.min),this.barGoal<t&&this._drawBar(t);var n=i.now,s=c(),o=s+100*Math.abs(n-t)*i.options.speed/(i.max-i.min);i._frameId=window.requestAnimationFrame((function e(r){var a=void 0;if(r>o)a=t;else{var l=(r-s)/i.options.speed;a=Math.round(i.easing.fn(l/100)*(i.max-i.min)),t>n?(a=n+a)>t&&(a=t):(a=n-a)<t&&(a=t)}i._update(a),a===t?(window.cancelAnimationFrame(i._frameId),i._frameId=null,i.now===i.goal&&i._trigger("finish")):i._frameId=window.requestAnimationFrame(e)}))}},{key:"_update",value:function(t){this.now=t,this._updateBar(),this.$element.attr("aria-valuenow",this.now),this.$number.length>0&&"function"==typeof this.options.numberCallback&&this.$number.html(this.options.numberCallback.call(this,[this.now])),this.$content.length>0&&"function"==typeof this.options.contentCallback&&this.$content.html(this.options.contentCallback.call(this,[this.now])),this._trigger("update",t)}},{key:"_clear",value:function(){this._frameId&&(window.cancelAnimationFrame(this._frameId),this._frameId=null)}},{key:"get",value:function(){return this.now}},{key:"start",value:function(){this._clear(),this._trigger("start"),this.go(this.goal)}},{key:"reset",value:function(){this._clear(),this._drawBar(this.first),this._update(this.first),this._trigger("reset")}},{key:"stop",value:function(){this._clear(),this._trigger("stop")}},{key:"finish",value:function(){this._clear(),this._update(this.goal),this._trigger("finish")}},{key:"destroy",value:function(){this.$element.data(f,null),this._trigger("destroy")}}],[{key:"registerEasing",value:function(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];d[t]=u.apply(void 0,i)}},{key:"getEasing",value:function(t){return d[t]}},{key:"setDefaults",value:function(t){i.default.extend(!0,p,i.default.isPlainObject(t)&&t)}}]),t}(),m="asPieProgress",v=i.default.fn.asPieProgress,y=function(t){for(var e=arguments.length,n=Array(e>1?e-1:0),s=1;s<e;s++)n[s-1]=arguments[s];if("string"==typeof t){var o=t;if(/^_/.test(o))return!1;if(!/^(get)/.test(o))return this.each((function(){var t=i.default.data(this,m);t&&"function"==typeof t[o]&&t[o].apply(t,n)}));var r=this.first().data(m);if(r&&"function"==typeof r[o])return r[o].apply(r,n)}return this.each((function(){(0,i.default)(this).data(m)||(0,i.default)(this).data(m,new g(this,t))}))};i.default.fn.asPieProgress=y,i.default.asPieProgress=i.default.extend({setDefaults:g.setDefaults,registerEasing:g.registerEasing,getEasing:g.getEasing,noConflict:function(){return i.default.fn.asPieProgress=v,y}},{version:"0.4.7"})})),function(t){"use strict";"function"==typeof define&&define.amd?define(["jquery"],t):"undefined"!=typeof module&&module.exports?module.exports=t(require("jquery")):t(jQuery)}((function(t){var e=-1,i=-1,n=function(t){return parseFloat(t)||0},s=function(e){var i=t(e),s=null,o=[];return i.each((function(){var e=t(this),i=e.offset().top-n(e.css("margin-top")),r=o.length>0?o[o.length-1]:null;null===r?o.push(e):Math.floor(Math.abs(s-i))<=1?o[o.length-1]=r.add(e):o.push(e),s=i})),o},o=function(e){var i={byRow:!0,property:"height",target:null,remove:!1};return"object"==typeof e?t.extend(i,e):("boolean"==typeof e?i.byRow=e:"remove"===e&&(i.remove=!0),i)},r=t.fn.matchHeight=function(e){var i=o(e);if(i.remove){var n=this;return this.css(i.property,""),t.each(r._groups,(function(t,e){e.elements=e.elements.not(n)})),this}return this.length<=1&&!i.target||(r._groups.push({elements:this,options:i}),r._apply(this,i)),this};r.version="0.7.2",r._groups=[],r._throttle=80,r._maintainScroll=!1,r._beforeUpdate=null,r._afterUpdate=null,r._rows=s,r._parse=n,r._parseOptions=o,r._apply=function(e,i){var a=o(i),l=t(e),c=[l],h=t(window).scrollTop(),u=t("html").outerHeight(!0),d=l.parents().filter(":hidden");return d.each((function(){var e=t(this);e.data("style-cache",e.attr("style"))})),d.css("display","block"),a.byRow&&!a.target&&(l.each((function(){var e=t(this),i=e.css("display");"inline-block"!==i&&"flex"!==i&&"inline-flex"!==i&&(i="block"),e.data("style-cache",e.attr("style")),e.css({display:i,"padding-top":"0","padding-bottom":"0","margin-top":"0","margin-bottom":"0","border-top-width":"0","border-bottom-width":"0",height:"100px",overflow:"hidden"})})),c=s(l),l.each((function(){var e=t(this);e.attr("style",e.data("style-cache")||"")}))),t.each(c,(function(e,i){var s=t(i),o=0;if(a.target)o=a.target.outerHeight(!1);else{if(a.byRow&&s.length<=1)return void s.css(a.property,"");s.each((function(){var e=t(this),i=e.attr("style"),n=e.css("display");"inline-block"!==n&&"flex"!==n&&"inline-flex"!==n&&(n="block");var s={display:n};s[a.property]="",e.css(s),e.outerHeight(!1)>o&&(o=e.outerHeight(!1)),i?e.attr("style",i):e.css("display","")}))}s.each((function(){var e=t(this),i=0;a.target&&e.is(a.target)||("border-box"!==e.css("box-sizing")&&(i+=n(e.css("border-top-width"))+n(e.css("border-bottom-width")),i+=n(e.css("padding-top"))+n(e.css("padding-bottom"))),e.css(a.property,o-i+"px"))}))})),d.each((function(){var e=t(this);e.attr("style",e.data("style-cache")||null)})),r._maintainScroll&&t(window).scrollTop(h/u*t("html").outerHeight(!0)),this},r._applyDataApi=function(){var e={};t("[data-match-height], [data-mh]").each((function(){var i=t(this),n=i.attr("data-mh")||i.attr("data-match-height");e[n]=n in e?e[n].add(i):i})),t.each(e,(function(){this.matchHeight(!0)}))};var a=function(e){r._beforeUpdate&&r._beforeUpdate(e,r._groups),t.each(r._groups,(function(){r._apply(this.elements,this.options)})),r._afterUpdate&&r._afterUpdate(e,r._groups)};r._update=function(n,s){if(s&&"resize"===s.type){var o=t(window).width();if(o===e)return;e=o}n?-1===i&&(i=setTimeout((function(){a(s),i=-1}),r._throttle)):a(s)},t(r._applyDataApi);var l=t.fn.on?"on":"bind";t(window)[l]("load",(function(t){r._update(!1,t)})),t(window)[l]("resize orientationchange",(function(t){r._update(!0,t)}))})),
/*!
 * Datepicker for Bootstrap v1.7.1 (https://github.com/uxsolutions/bootstrap-datepicker)
 *
 * Licensed under the Apache License v2.0 (http://www.apache.org/licenses/LICENSE-2.0)
 */
function(t){"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports?t(require("jquery")):t(jQuery)}((function(t,e){function i(){return new Date(Date.UTC.apply(Date,arguments))}function n(){var t=new Date;return i(t.getFullYear(),t.getMonth(),t.getDate())}function s(t,e){return t.getUTCFullYear()===e.getUTCFullYear()&&t.getUTCMonth()===e.getUTCMonth()&&t.getUTCDate()===e.getUTCDate()}function o(i,n){return function(){return n!==e&&t.fn.datepicker.deprecated(n),this[i].apply(this,arguments)}}var r,a=(r={get:function(t){return this.slice(t)[0]},contains:function(t){for(var e=t&&t.valueOf(),i=0,n=this.length;i<n;i++)if(0<=this[i].valueOf()-e&&this[i].valueOf()-e<864e5)return i;return-1},remove:function(t){this.splice(t,1)},replace:function(e){e&&(t.isArray(e)||(e=[e]),this.clear(),this.push.apply(this,e))},clear:function(){this.length=0},copy:function(){var t=new a;return t.replace(this),t}},function(){var e=[];return e.push.apply(e,arguments),t.extend(e,r),e}),l=function(e,i){t.data(e,"datepicker",this),this._process_options(i),this.dates=new a,this.viewDate=this.o.defaultViewDate,this.focusDate=null,this.element=t(e),this.isInput=this.element.is("input"),this.inputField=this.isInput?this.element:this.element.find("input"),this.component=!!this.element.hasClass("date")&&this.element.find(".add-on, .input-group-addon, .btn"),this.component&&0===this.component.length&&(this.component=!1),this.isInline=!this.component&&this.element.is("div"),this.picker=t(g.template),this._check_template(this.o.templates.leftArrow)&&this.picker.find(".prev").html(this.o.templates.leftArrow),this._check_template(this.o.templates.rightArrow)&&this.picker.find(".next").html(this.o.templates.rightArrow),this._buildEvents(),this._attachEvents(),this.isInline?this.picker.addClass("datepicker-inline").appendTo(this.element):this.picker.addClass("datepicker-dropdown dropdown-menu"),this.o.rtl&&this.picker.addClass("datepicker-rtl"),this.o.calendarWeeks&&this.picker.find(".datepicker-days .datepicker-switch, thead .datepicker-title, tfoot .today, tfoot .clear").attr("colspan",(function(t,e){return Number(e)+1})),this._process_options({startDate:this._o.startDate,endDate:this._o.endDate,daysOfWeekDisabled:this.o.daysOfWeekDisabled,daysOfWeekHighlighted:this.o.daysOfWeekHighlighted,datesDisabled:this.o.datesDisabled}),this._allow_update=!1,this.setViewMode(this.o.startView),this._allow_update=!0,this.fillDow(),this.fillMonths(),this.update(),this.isInline&&this.show()};l.prototype={constructor:l,_resolveViewName:function(e){return t.each(g.viewModes,(function(i,n){if(e===i||-1!==t.inArray(e,n.names))return e=i,!1})),e},_resolveDaysOfWeek:function(e){return t.isArray(e)||(e=e.split(/[,\s]*/)),t.map(e,Number)},_check_template:function(i){try{return i!==e&&""!==i&&((i.match(/[<>]/g)||[]).length<=0||t(i).length>0)}catch(t){return!1}},_process_options:function(e){this._o=t.extend({},this._o,e);var s=this.o=t.extend({},this._o),o=s.language;f[o]||(o=o.split("-")[0],f[o]||(o=d.language)),s.language=o,s.startView=this._resolveViewName(s.startView),s.minViewMode=this._resolveViewName(s.minViewMode),s.maxViewMode=this._resolveViewName(s.maxViewMode),s.startView=Math.max(this.o.minViewMode,Math.min(this.o.maxViewMode,s.startView)),!0!==s.multidate&&(s.multidate=Number(s.multidate)||!1,!1!==s.multidate&&(s.multidate=Math.max(0,s.multidate))),s.multidateSeparator=String(s.multidateSeparator),s.weekStart%=7,s.weekEnd=(s.weekStart+6)%7;var r=g.parseFormat(s.format);s.startDate!==-1/0&&(s.startDate?s.startDate instanceof Date?s.startDate=this._local_to_utc(this._zero_time(s.startDate)):s.startDate=g.parseDate(s.startDate,r,s.language,s.assumeNearbyYear):s.startDate=-1/0),s.endDate!==1/0&&(s.endDate?s.endDate instanceof Date?s.endDate=this._local_to_utc(this._zero_time(s.endDate)):s.endDate=g.parseDate(s.endDate,r,s.language,s.assumeNearbyYear):s.endDate=1/0),s.daysOfWeekDisabled=this._resolveDaysOfWeek(s.daysOfWeekDisabled||[]),s.daysOfWeekHighlighted=this._resolveDaysOfWeek(s.daysOfWeekHighlighted||[]),s.datesDisabled=s.datesDisabled||[],t.isArray(s.datesDisabled)||(s.datesDisabled=s.datesDisabled.split(",")),s.datesDisabled=t.map(s.datesDisabled,(function(t){return g.parseDate(t,r,s.language,s.assumeNearbyYear)}));var a=String(s.orientation).toLowerCase().split(/\s+/g),l=s.orientation.toLowerCase();if(a=t.grep(a,(function(t){return/^auto|left|right|top|bottom$/.test(t)})),s.orientation={x:"auto",y:"auto"},l&&"auto"!==l)if(1===a.length)switch(a[0]){case"top":case"bottom":s.orientation.y=a[0];break;case"left":case"right":s.orientation.x=a[0]}else l=t.grep(a,(function(t){return/^left|right$/.test(t)})),s.orientation.x=l[0]||"auto",l=t.grep(a,(function(t){return/^top|bottom$/.test(t)})),s.orientation.y=l[0]||"auto";else;if(s.defaultViewDate instanceof Date||"string"==typeof s.defaultViewDate)s.defaultViewDate=g.parseDate(s.defaultViewDate,r,s.language,s.assumeNearbyYear);else if(s.defaultViewDate){var c=s.defaultViewDate.year||(new Date).getFullYear(),h=s.defaultViewDate.month||0,u=s.defaultViewDate.day||1;s.defaultViewDate=i(c,h,u)}else s.defaultViewDate=n()},_events:[],_secondaryEvents:[],_applyEvents:function(t){for(var i,n,s,o=0;o<t.length;o++)i=t[o][0],2===t[o].length?(n=e,s=t[o][1]):3===t[o].length&&(n=t[o][1],s=t[o][2]),i.on(s,n)},_unapplyEvents:function(t){for(var i,n,s,o=0;o<t.length;o++)i=t[o][0],2===t[o].length?(s=e,n=t[o][1]):3===t[o].length&&(s=t[o][1],n=t[o][2]),i.off(n,s)},_buildEvents:function(){var e={keyup:t.proxy((function(e){-1===t.inArray(e.keyCode,[27,37,39,38,40,32,13,9])&&this.update()}),this),keydown:t.proxy(this.keydown,this),paste:t.proxy(this.paste,this)};!0===this.o.showOnFocus&&(e.focus=t.proxy(this.show,this)),this.isInput?this._events=[[this.element,e]]:this.component&&this.inputField.length?this._events=[[this.inputField,e],[this.component,{click:t.proxy(this.show,this)}]]:this._events=[[this.element,{click:t.proxy(this.show,this),keydown:t.proxy(this.keydown,this)}]],this._events.push([this.element,"*",{blur:t.proxy((function(t){this._focused_from=t.target}),this)}],[this.element,{blur:t.proxy((function(t){this._focused_from=t.target}),this)}]),this.o.immediateUpdates&&this._events.push([this.element,{"changeYear changeMonth":t.proxy((function(t){this.update(t.date)}),this)}]),this._secondaryEvents=[[this.picker,{click:t.proxy(this.click,this)}],[this.picker,".prev, .next",{click:t.proxy(this.navArrowsClick,this)}],[this.picker,".day:not(.disabled)",{click:t.proxy(this.dayCellClick,this)}],[t(window),{resize:t.proxy(this.place,this)}],[t(document),{"mousedown touchstart":t.proxy((function(t){this.element.is(t.target)||this.element.find(t.target).length||this.picker.is(t.target)||this.picker.find(t.target).length||this.isInline||this.hide()}),this)}]]},_attachEvents:function(){this._detachEvents(),this._applyEvents(this._events)},_detachEvents:function(){this._unapplyEvents(this._events)},_attachSecondaryEvents:function(){this._detachSecondaryEvents(),this._applyEvents(this._secondaryEvents)},_detachSecondaryEvents:function(){this._unapplyEvents(this._secondaryEvents)},_trigger:function(e,i){var n=i||this.dates.get(-1),s=this._utc_to_local(n);this.element.trigger({type:e,date:s,viewMode:this.viewMode,dates:t.map(this.dates,this._utc_to_local),format:t.proxy((function(t,e){0===arguments.length?(t=this.dates.length-1,e=this.o.format):"string"==typeof t&&(e=t,t=this.dates.length-1),e=e||this.o.format;var i=this.dates.get(t);return g.formatDate(i,e,this.o.language)}),this)})},show:function(){if(!(this.inputField.prop("disabled")||this.inputField.prop("readonly")&&!1===this.o.enableOnReadonly))return this.isInline||this.picker.appendTo(this.o.container),this.place(),this.picker.show(),this._attachSecondaryEvents(),this._trigger("show"),(window.navigator.msMaxTouchPoints||"ontouchstart"in document)&&this.o.disableTouchKeyboard&&t(this.element).blur(),this},hide:function(){return this.isInline||!this.picker.is(":visible")||(this.focusDate=null,this.picker.hide().detach(),this._detachSecondaryEvents(),this.setViewMode(this.o.startView),this.o.forceParse&&this.inputField.val()&&this.setValue(),this._trigger("hide")),this},destroy:function(){return this.hide(),this._detachEvents(),this._detachSecondaryEvents(),this.picker.remove(),delete this.element.data().datepicker,this.isInput||delete this.element.data().date,this},paste:function(e){var i;if(e.originalEvent.clipboardData&&e.originalEvent.clipboardData.types&&-1!==t.inArray("text/plain",e.originalEvent.clipboardData.types))i=e.originalEvent.clipboardData.getData("text/plain");else{if(!window.clipboardData)return;i=window.clipboardData.getData("Text")}this.setDate(i),this.update(),e.preventDefault()},_utc_to_local:function(t){if(!t)return t;var e=new Date(t.getTime()+6e4*t.getTimezoneOffset());return e.getTimezoneOffset()!==t.getTimezoneOffset()&&(e=new Date(t.getTime()+6e4*e.getTimezoneOffset())),e},_local_to_utc:function(t){return t&&new Date(t.getTime()-6e4*t.getTimezoneOffset())},_zero_time:function(t){return t&&new Date(t.getFullYear(),t.getMonth(),t.getDate())},_zero_utc_time:function(t){return t&&i(t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate())},getDates:function(){return t.map(this.dates,this._utc_to_local)},getUTCDates:function(){return t.map(this.dates,(function(t){return new Date(t)}))},getDate:function(){return this._utc_to_local(this.getUTCDate())},getUTCDate:function(){var t=this.dates.get(-1);return t!==e?new Date(t):null},clearDates:function(){this.inputField.val(""),this.update(),this._trigger("changeDate"),this.o.autoclose&&this.hide()},setDates:function(){var e=t.isArray(arguments[0])?arguments[0]:arguments;return this.update.apply(this,e),this._trigger("changeDate"),this.setValue(),this},setUTCDates:function(){var e=t.isArray(arguments[0])?arguments[0]:arguments;return this.setDates.apply(this,t.map(e,this._utc_to_local)),this},setDate:o("setDates"),setUTCDate:o("setUTCDates"),remove:o("destroy","Method `remove` is deprecated and will be removed in version 2.0. Use `destroy` instead"),setValue:function(){var t=this.getFormattedDate();return this.inputField.val(t),this},getFormattedDate:function(i){i===e&&(i=this.o.format);var n=this.o.language;return t.map(this.dates,(function(t){return g.formatDate(t,i,n)})).join(this.o.multidateSeparator)},getStartDate:function(){return this.o.startDate},setStartDate:function(t){return this._process_options({startDate:t}),this.update(),this.updateNavArrows(),this},getEndDate:function(){return this.o.endDate},setEndDate:function(t){return this._process_options({endDate:t}),this.update(),this.updateNavArrows(),this},setDaysOfWeekDisabled:function(t){return this._process_options({daysOfWeekDisabled:t}),this.update(),this},setDaysOfWeekHighlighted:function(t){return this._process_options({daysOfWeekHighlighted:t}),this.update(),this},setDatesDisabled:function(t){return this._process_options({datesDisabled:t}),this.update(),this},place:function(){if(this.isInline)return this;var e=this.picker.outerWidth(),i=this.picker.outerHeight(),n=t(this.o.container),s=n.width(),o="body"===this.o.container?t(document).scrollTop():n.scrollTop(),r=n.offset(),a=[0];this.element.parents().each((function(){var e=t(this).css("z-index");"auto"!==e&&0!==Number(e)&&a.push(Number(e))}));var l=Math.max.apply(Math,a)+this.o.zIndexOffset,c=this.component?this.component.parent().offset():this.element.offset(),h=this.component?this.component.outerHeight(!0):this.element.outerHeight(!1),u=this.component?this.component.outerWidth(!0):this.element.outerWidth(!1),d=c.left-r.left,p=c.top-r.top;"body"!==this.o.container&&(p+=o),this.picker.removeClass("datepicker-orient-top datepicker-orient-bottom datepicker-orient-right datepicker-orient-left"),"auto"!==this.o.orientation.x?(this.picker.addClass("datepicker-orient-"+this.o.orientation.x),"right"===this.o.orientation.x&&(d-=e-u)):c.left<0?(this.picker.addClass("datepicker-orient-left"),d-=c.left-10):d+e>s?(this.picker.addClass("datepicker-orient-right"),d+=u-e):this.o.rtl?this.picker.addClass("datepicker-orient-right"):this.picker.addClass("datepicker-orient-left");var f=this.o.orientation.y;if("auto"===f&&(f=-o+p-i<0?"bottom":"top"),this.picker.addClass("datepicker-orient-"+f),"top"===f?p-=i+parseInt(this.picker.css("padding-top")):p+=h,this.o.rtl){var g=s-(d+u);this.picker.css({top:p,right:g,zIndex:l})}else this.picker.css({top:p,left:d,zIndex:l});return this},_allow_update:!0,update:function(){if(!this._allow_update)return this;var e=this.dates.copy(),i=[],n=!1;return arguments.length?(t.each(arguments,t.proxy((function(t,e){e instanceof Date&&(e=this._local_to_utc(e)),i.push(e)}),this)),n=!0):(i=(i=this.isInput?this.element.val():this.element.data("date")||this.inputField.val())&&this.o.multidate?i.split(this.o.multidateSeparator):[i],delete this.element.data().date),i=t.map(i,t.proxy((function(t){return g.parseDate(t,this.o.format,this.o.language,this.o.assumeNearbyYear)}),this)),i=t.grep(i,t.proxy((function(t){return!this.dateWithinRange(t)||!t}),this),!0),this.dates.replace(i),this.o.updateViewDate&&(this.dates.length?this.viewDate=new Date(this.dates.get(-1)):this.viewDate<this.o.startDate?this.viewDate=new Date(this.o.startDate):this.viewDate>this.o.endDate?this.viewDate=new Date(this.o.endDate):this.viewDate=this.o.defaultViewDate),n?(this.setValue(),this.element.change()):this.dates.length&&String(e)!==String(this.dates)&&n&&(this._trigger("changeDate"),this.element.change()),!this.dates.length&&e.length&&(this._trigger("clearDate"),this.element.change()),this.fill(),this},fillDow:function(){if(this.o.showWeekDays){var e=this.o.weekStart,i="<tr>";for(this.o.calendarWeeks&&(i+='<th class="cw">&#160;</th>');e<this.o.weekStart+7;)i+='<th class="dow',-1!==t.inArray(e,this.o.daysOfWeekDisabled)&&(i+=" disabled"),i+='">'+f[this.o.language].daysMin[e++%7]+"</th>";i+="</tr>",this.picker.find(".datepicker-days thead").append(i)}},fillMonths:function(){for(var t=this._utc_to_local(this.viewDate),e="",i=0;i<12;i++)e+='<span class="month'+(t&&t.getMonth()===i?" focused":"")+'">'+f[this.o.language].monthsShort[i]+"</span>";this.picker.find(".datepicker-months td").html(e)},setRange:function(e){e&&e.length?this.range=t.map(e,(function(t){return t.valueOf()})):delete this.range,this.fill()},getClassNames:function(e){var i=[],o=this.viewDate.getUTCFullYear(),r=this.viewDate.getUTCMonth(),a=n();return e.getUTCFullYear()<o||e.getUTCFullYear()===o&&e.getUTCMonth()<r?i.push("old"):(e.getUTCFullYear()>o||e.getUTCFullYear()===o&&e.getUTCMonth()>r)&&i.push("new"),this.focusDate&&e.valueOf()===this.focusDate.valueOf()&&i.push("focused"),this.o.todayHighlight&&s(e,a)&&i.push("today"),-1!==this.dates.contains(e)&&i.push("active"),this.dateWithinRange(e)||i.push("disabled"),this.dateIsDisabled(e)&&i.push("disabled","disabled-date"),-1!==t.inArray(e.getUTCDay(),this.o.daysOfWeekHighlighted)&&i.push("highlighted"),this.range&&(e>this.range[0]&&e<this.range[this.range.length-1]&&i.push("range"),-1!==t.inArray(e.valueOf(),this.range)&&i.push("selected"),e.valueOf()===this.range[0]&&i.push("range-start"),e.valueOf()===this.range[this.range.length-1]&&i.push("range-end")),i},_fill_yearsView:function(i,n,s,o,r,a,l){for(var c,h,u,d="",p=s/10,f=this.picker.find(i),g=Math.floor(o/s)*s,m=g+9*p,v=Math.floor(this.viewDate.getFullYear()/p)*p,y=t.map(this.dates,(function(t){return Math.floor(t.getUTCFullYear()/p)*p})),b=g-p;b<=m+p;b+=p)c=[n],h=null,b===g-p?c.push("old"):b===m+p&&c.push("new"),-1!==t.inArray(b,y)&&c.push("active"),(b<r||b>a)&&c.push("disabled"),b===v&&c.push("focused"),l!==t.noop&&((u=l(new Date(b,0,1)))===e?u={}:"boolean"==typeof u?u={enabled:u}:"string"==typeof u&&(u={classes:u}),!1===u.enabled&&c.push("disabled"),u.classes&&(c=c.concat(u.classes.split(/\s+/))),u.tooltip&&(h=u.tooltip)),d+='<span class="'+c.join(" ")+'"'+(h?' title="'+h+'"':"")+">"+b+"</span>";f.find(".datepicker-switch").text(g+"-"+m),f.find("td").html(d)},fill:function(){var n,s,o=new Date(this.viewDate),r=o.getUTCFullYear(),a=o.getUTCMonth(),l=this.o.startDate!==-1/0?this.o.startDate.getUTCFullYear():-1/0,c=this.o.startDate!==-1/0?this.o.startDate.getUTCMonth():-1/0,h=this.o.endDate!==1/0?this.o.endDate.getUTCFullYear():1/0,u=this.o.endDate!==1/0?this.o.endDate.getUTCMonth():1/0,d=f[this.o.language].today||f.en.today||"",p=f[this.o.language].clear||f.en.clear||"",m=f[this.o.language].titleFormat||f.en.titleFormat;if(!isNaN(r)&&!isNaN(a)){this.picker.find(".datepicker-days .datepicker-switch").text(g.formatDate(o,m,this.o.language)),this.picker.find("tfoot .today").text(d).css("display",!0===this.o.todayBtn||"linked"===this.o.todayBtn?"table-cell":"none"),this.picker.find("tfoot .clear").text(p).css("display",!0===this.o.clearBtn?"table-cell":"none"),this.picker.find("thead .datepicker-title").text(this.o.title).css("display","string"==typeof this.o.title&&""!==this.o.title?"table-cell":"none"),this.updateNavArrows(),this.fillMonths();var v=i(r,a,0),y=v.getUTCDate();v.setUTCDate(y-(v.getUTCDay()-this.o.weekStart+7)%7);var b=new Date(v);v.getUTCFullYear()<100&&b.setUTCFullYear(v.getUTCFullYear()),b.setUTCDate(b.getUTCDate()+42),b=b.valueOf();for(var w,_,x=[];v.valueOf()<b;){if((w=v.getUTCDay())===this.o.weekStart&&(x.push("<tr>"),this.o.calendarWeeks)){var C=new Date(+v+(this.o.weekStart-w-7)%7*864e5),$=new Date(Number(C)+(11-C.getUTCDay())%7*864e5),k=new Date(Number(k=i($.getUTCFullYear(),0,1))+(11-k.getUTCDay())%7*864e5),D=($-k)/864e5/7+1;x.push('<td class="cw">'+D+"</td>")}(_=this.getClassNames(v)).push("day");var S=v.getUTCDate();this.o.beforeShowDay!==t.noop&&((s=this.o.beforeShowDay(this._utc_to_local(v)))===e?s={}:"boolean"==typeof s?s={enabled:s}:"string"==typeof s&&(s={classes:s}),!1===s.enabled&&_.push("disabled"),s.classes&&(_=_.concat(s.classes.split(/\s+/))),s.tooltip&&(n=s.tooltip),s.content&&(S=s.content)),_=t.isFunction(t.uniqueSort)?t.uniqueSort(_):t.unique(_),x.push('<td class="'+_.join(" ")+'"'+(n?' title="'+n+'"':"")+' data-date="'+v.getTime().toString()+'">'+S+"</td>"),n=null,w===this.o.weekEnd&&x.push("</tr>"),v.setUTCDate(v.getUTCDate()+1)}this.picker.find(".datepicker-days tbody").html(x.join(""));var A=f[this.o.language].monthsTitle||f.en.monthsTitle||"Months",T=this.picker.find(".datepicker-months").find(".datepicker-switch").text(this.o.maxViewMode<2?A:r).end().find("tbody span").removeClass("active");if(t.each(this.dates,(function(t,e){e.getUTCFullYear()===r&&T.eq(e.getUTCMonth()).addClass("active")})),(r<l||r>h)&&T.addClass("disabled"),r===l&&T.slice(0,c).addClass("disabled"),r===h&&T.slice(u+1).addClass("disabled"),this.o.beforeShowMonth!==t.noop){var E=this;t.each(T,(function(i,n){var s=new Date(r,i,1),o=E.o.beforeShowMonth(s);o===e?o={}:"boolean"==typeof o?o={enabled:o}:"string"==typeof o&&(o={classes:o}),!1!==o.enabled||t(n).hasClass("disabled")||t(n).addClass("disabled"),o.classes&&t(n).addClass(o.classes),o.tooltip&&t(n).prop("title",o.tooltip)}))}this._fill_yearsView(".datepicker-years","year",10,r,l,h,this.o.beforeShowYear),this._fill_yearsView(".datepicker-decades","decade",100,r,l,h,this.o.beforeShowDecade),this._fill_yearsView(".datepicker-centuries","century",1e3,r,l,h,this.o.beforeShowCentury)}},updateNavArrows:function(){if(this._allow_update){var t,e,i=new Date(this.viewDate),n=i.getUTCFullYear(),s=i.getUTCMonth(),o=this.o.startDate!==-1/0?this.o.startDate.getUTCFullYear():-1/0,r=this.o.startDate!==-1/0?this.o.startDate.getUTCMonth():-1/0,a=this.o.endDate!==1/0?this.o.endDate.getUTCFullYear():1/0,l=this.o.endDate!==1/0?this.o.endDate.getUTCMonth():1/0,c=1;switch(this.viewMode){case 0:t=n<=o&&s<=r,e=n>=a&&s>=l;break;case 4:c*=10;case 3:c*=10;case 2:c*=10;case 1:t=Math.floor(n/c)*c<=o,e=Math.floor(n/c)*c+c>=a}this.picker.find(".prev").toggleClass("disabled",t),this.picker.find(".next").toggleClass("disabled",e)}},click:function(e){var s,o,r;e.preventDefault(),e.stopPropagation(),(s=t(e.target)).hasClass("datepicker-switch")&&this.viewMode!==this.o.maxViewMode&&this.setViewMode(this.viewMode+1),s.hasClass("today")&&!s.hasClass("day")&&(this.setViewMode(0),this._setDate(n(),"linked"===this.o.todayBtn?null:"view")),s.hasClass("clear")&&this.clearDates(),s.hasClass("disabled")||(s.hasClass("month")||s.hasClass("year")||s.hasClass("decade")||s.hasClass("century"))&&(this.viewDate.setUTCDate(1),1,1===this.viewMode?(r=s.parent().find("span").index(s),o=this.viewDate.getUTCFullYear(),this.viewDate.setUTCMonth(r)):(r=0,o=Number(s.text()),this.viewDate.setUTCFullYear(o)),this._trigger(g.viewModes[this.viewMode-1].e,this.viewDate),this.viewMode===this.o.minViewMode?this._setDate(i(o,r,1)):(this.setViewMode(this.viewMode-1),this.fill())),this.picker.is(":visible")&&this._focused_from&&this._focused_from.focus(),delete this._focused_from},dayCellClick:function(e){var i=t(e.currentTarget).data("date"),n=new Date(i);this.o.updateViewDate&&(n.getUTCFullYear()!==this.viewDate.getUTCFullYear()&&this._trigger("changeYear",this.viewDate),n.getUTCMonth()!==this.viewDate.getUTCMonth()&&this._trigger("changeMonth",this.viewDate)),this._setDate(n)},navArrowsClick:function(e){var i=t(e.currentTarget).hasClass("prev")?-1:1;0!==this.viewMode&&(i*=12*g.viewModes[this.viewMode].navStep),this.viewDate=this.moveMonth(this.viewDate,i),this._trigger(g.viewModes[this.viewMode].e,this.viewDate),this.fill()},_toggle_multidate:function(t){var e=this.dates.contains(t);if(t||this.dates.clear(),-1!==e?(!0===this.o.multidate||this.o.multidate>1||this.o.toggleActive)&&this.dates.remove(e):!1===this.o.multidate?(this.dates.clear(),this.dates.push(t)):this.dates.push(t),"number"==typeof this.o.multidate)for(;this.dates.length>this.o.multidate;)this.dates.remove(0)},_setDate:function(t,e){e&&"date"!==e||this._toggle_multidate(t&&new Date(t)),(!e&&this.o.updateViewDate||"view"===e)&&(this.viewDate=t&&new Date(t)),this.fill(),this.setValue(),e&&"view"===e||this._trigger("changeDate"),this.inputField.trigger("change"),!this.o.autoclose||e&&"date"!==e||this.hide()},moveDay:function(t,e){var i=new Date(t);return i.setUTCDate(t.getUTCDate()+e),i},moveWeek:function(t,e){return this.moveDay(t,7*e)},moveMonth:function(t,e){if(!(i=t)||isNaN(i.getTime()))return this.o.defaultViewDate;var i;if(!e)return t;var n,s,o=new Date(t.valueOf()),r=o.getUTCDate(),a=o.getUTCMonth(),l=Math.abs(e);if(e=e>0?1:-1,1===l)s=-1===e?function(){return o.getUTCMonth()===a}:function(){return o.getUTCMonth()!==n},n=a+e,o.setUTCMonth(n),n=(n+12)%12;else{for(var c=0;c<l;c++)o=this.moveMonth(o,e);n=o.getUTCMonth(),o.setUTCDate(r),s=function(){return n!==o.getUTCMonth()}}for(;s();)o.setUTCDate(--r),o.setUTCMonth(n);return o},moveYear:function(t,e){return this.moveMonth(t,12*e)},moveAvailableDate:function(t,e,i){do{if(t=this[i](t,e),!this.dateWithinRange(t))return!1;i="moveDay"}while(this.dateIsDisabled(t));return t},weekOfDateIsDisabled:function(e){return-1!==t.inArray(e.getUTCDay(),this.o.daysOfWeekDisabled)},dateIsDisabled:function(e){return this.weekOfDateIsDisabled(e)||t.grep(this.o.datesDisabled,(function(t){return s(e,t)})).length>0},dateWithinRange:function(t){return t>=this.o.startDate&&t<=this.o.endDate},keydown:function(t){if(this.picker.is(":visible")){var e,i,n=!1,s=this.focusDate||this.viewDate;switch(t.keyCode){case 27:this.focusDate?(this.focusDate=null,this.viewDate=this.dates.get(-1)||this.viewDate,this.fill()):this.hide(),t.preventDefault(),t.stopPropagation();break;case 37:case 38:case 39:case 40:if(!this.o.keyboardNavigation||7===this.o.daysOfWeekDisabled.length)break;e=37===t.keyCode||38===t.keyCode?-1:1,0===this.viewMode?t.ctrlKey?(i=this.moveAvailableDate(s,e,"moveYear"))&&this._trigger("changeYear",this.viewDate):t.shiftKey?(i=this.moveAvailableDate(s,e,"moveMonth"))&&this._trigger("changeMonth",this.viewDate):37===t.keyCode||39===t.keyCode?i=this.moveAvailableDate(s,e,"moveDay"):this.weekOfDateIsDisabled(s)||(i=this.moveAvailableDate(s,e,"moveWeek")):1===this.viewMode?(38!==t.keyCode&&40!==t.keyCode||(e*=4),i=this.moveAvailableDate(s,e,"moveMonth")):2===this.viewMode&&(38!==t.keyCode&&40!==t.keyCode||(e*=4),i=this.moveAvailableDate(s,e,"moveYear")),i&&(this.focusDate=this.viewDate=i,this.setValue(),this.fill(),t.preventDefault());break;case 13:if(!this.o.forceParse)break;s=this.focusDate||this.dates.get(-1)||this.viewDate,this.o.keyboardNavigation&&(this._toggle_multidate(s),n=!0),this.focusDate=null,this.viewDate=this.dates.get(-1)||this.viewDate,this.setValue(),this.fill(),this.picker.is(":visible")&&(t.preventDefault(),t.stopPropagation(),this.o.autoclose&&this.hide());break;case 9:this.focusDate=null,this.viewDate=this.dates.get(-1)||this.viewDate,this.fill(),this.hide()}n&&(this.dates.length?this._trigger("changeDate"):this._trigger("clearDate"),this.inputField.trigger("change"))}else 40!==t.keyCode&&27!==t.keyCode||(this.show(),t.stopPropagation())},setViewMode:function(t){this.viewMode=t,this.picker.children("div").hide().filter(".datepicker-"+g.viewModes[this.viewMode].clsName).show(),this.updateNavArrows(),this._trigger("changeViewMode",new Date(this.viewDate))}};var c=function(e,i){t.data(e,"datepicker",this),this.element=t(e),this.inputs=t.map(i.inputs,(function(t){return t.jquery?t[0]:t})),delete i.inputs,this.keepEmptyValues=i.keepEmptyValues,delete i.keepEmptyValues,u.call(t(this.inputs),i).on("changeDate",t.proxy(this.dateUpdated,this)),this.pickers=t.map(this.inputs,(function(e){return t.data(e,"datepicker")})),this.updateDates()};c.prototype={updateDates:function(){this.dates=t.map(this.pickers,(function(t){return t.getUTCDate()})),this.updateRanges()},updateRanges:function(){var e=t.map(this.dates,(function(t){return t.valueOf()}));t.each(this.pickers,(function(t,i){i.setRange(e)}))},dateUpdated:function(i){if(!this.updating){this.updating=!0;var n=t.data(i.target,"datepicker");if(n!==e){var s=n.getUTCDate(),o=this.keepEmptyValues,r=t.inArray(i.target,this.inputs),a=r-1,l=r+1,c=this.inputs.length;if(-1!==r){if(t.each(this.pickers,(function(t,e){e.getUTCDate()||e!==n&&o||e.setUTCDate(s)})),s<this.dates[a])for(;a>=0&&s<this.dates[a];)this.pickers[a--].setUTCDate(s);else if(s>this.dates[l])for(;l<c&&s>this.dates[l];)this.pickers[l++].setUTCDate(s);this.updateDates(),delete this.updating}}}},destroy:function(){t.map(this.pickers,(function(t){t.destroy()})),t(this.inputs).off("changeDate",this.dateUpdated),delete this.element.data().datepicker},remove:o("destroy","Method `remove` is deprecated and will be removed in version 2.0. Use `destroy` instead")};var h=t.fn.datepicker,u=function(i){var n,s=Array.apply(null,arguments);if(s.shift(),this.each((function(){var e=t(this),o=e.data("datepicker"),r="object"==typeof i&&i;if(!o){var a=function(e,i){var n=t(e).data(),s={},o=new RegExp("^"+i.toLowerCase()+"([A-Z])");function r(t,e){return e.toLowerCase()}for(var a in i=new RegExp("^"+i.toLowerCase()),n)i.test(a)&&(s[a.replace(o,r)]=n[a]);return s}(this,"date"),h=function(e){var i={};if(f[e]||(e=e.split("-")[0],f[e])){var n=f[e];return t.each(p,(function(t,e){e in n&&(i[e]=n[e])})),i}}(t.extend({},d,a,r).language),u=t.extend({},d,h,a,r);e.hasClass("input-daterange")||u.inputs?(t.extend(u,{inputs:u.inputs||e.find("input").toArray()}),o=new c(this,u)):o=new l(this,u),e.data("datepicker",o)}"string"==typeof i&&"function"==typeof o[i]&&(n=o[i].apply(o,s))})),n===e||n instanceof l||n instanceof c)return this;if(this.length>1)throw new Error("Using only allowed for the collection of a single element ("+i+" function)");return n};t.fn.datepicker=u;var d=t.fn.datepicker.defaults={assumeNearbyYear:!1,autoclose:!1,beforeShowDay:t.noop,beforeShowMonth:t.noop,beforeShowYear:t.noop,beforeShowDecade:t.noop,beforeShowCentury:t.noop,calendarWeeks:!1,clearBtn:!1,toggleActive:!1,daysOfWeekDisabled:[],daysOfWeekHighlighted:[],datesDisabled:[],endDate:1/0,forceParse:!0,format:"mm/dd/yyyy",keepEmptyValues:!1,keyboardNavigation:!0,language:"en",minViewMode:0,maxViewMode:4,multidate:!1,multidateSeparator:",",orientation:"auto",rtl:!1,startDate:-1/0,startView:0,todayBtn:!1,todayHighlight:!1,updateViewDate:!0,weekStart:0,disableTouchKeyboard:!1,enableOnReadonly:!0,showOnFocus:!0,zIndexOffset:10,container:"body",immediateUpdates:!1,title:"",templates:{leftArrow:"&#x00AB;",rightArrow:"&#x00BB;"},showWeekDays:!0},p=t.fn.datepicker.locale_opts=["format","rtl","weekStart"];t.fn.datepicker.Constructor=l;var f=t.fn.datepicker.dates={en:{days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],daysMin:["Su","Mo","Tu","We","Th","Fr","Sa"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],today:"Today",clear:"Clear",titleFormat:"MM yyyy"}},g={viewModes:[{names:["days","month"],clsName:"days",e:"changeMonth"},{names:["months","year"],clsName:"months",e:"changeYear",navStep:1},{names:["years","decade"],clsName:"years",e:"changeDecade",navStep:10},{names:["decades","century"],clsName:"decades",e:"changeCentury",navStep:100},{names:["centuries","millennium"],clsName:"centuries",e:"changeMillennium",navStep:1e3}],validParts:/dd?|DD?|mm?|MM?|yy(?:yy)?/g,nonpunctuation:/[^ -\/:-@\u5e74\u6708\u65e5\[-`{-~\t\n\r]+/g,parseFormat:function(t){if("function"==typeof t.toValue&&"function"==typeof t.toDisplay)return t;var e=t.replace(this.validParts,"\0").split("\0"),i=t.match(this.validParts);if(!e||!e.length||!i||0===i.length)throw new Error("Invalid date format.");return{separators:e,parts:i}},parseDate:function(i,s,o,r){if(!i)return e;if(i instanceof Date)return i;if("string"==typeof s&&(s=g.parseFormat(s)),s.toValue)return s.toValue(i,s,o);var a,c,h,u,d,p={d:"moveDay",m:"moveMonth",w:"moveWeek",y:"moveYear"},m={yesterday:"-1d",today:"+0d",tomorrow:"+1d"};if(i in m&&(i=m[i]),/^[\-+]\d+[dmwy]([\s,]+[\-+]\d+[dmwy])*$/i.test(i)){for(a=i.match(/([\-+]\d+)([dmwy])/gi),i=new Date,u=0;u<a.length;u++)c=a[u].match(/([\-+]\d+)([dmwy])/i),h=Number(c[1]),d=p[c[2].toLowerCase()],i=l.prototype[d](i,h);return l.prototype._zero_utc_time(i)}a=i&&i.match(this.nonpunctuation)||[];var v,y,b={},w=["yyyy","yy","M","MM","m","mm","d","dd"],_={yyyy:function(t,e){return t.setUTCFullYear(r?(!0===(n=r)&&(n=10),(i=e)<100&&(i+=2e3)>(new Date).getFullYear()+n&&(i-=100),i):e);var i,n},m:function(t,e){if(isNaN(t))return t;for(e-=1;e<0;)e+=12;for(e%=12,t.setUTCMonth(e);t.getUTCMonth()!==e;)t.setUTCDate(t.getUTCDate()-1);return t},d:function(t,e){return t.setUTCDate(e)}};_.yy=_.yyyy,_.M=_.MM=_.mm=_.m,_.dd=_.d,i=n();var x=s.parts.slice();function C(){var t=this.slice(0,a[u].length),e=a[u].slice(0,t.length);return t.toLowerCase()===e.toLowerCase()}if(a.length!==x.length&&(x=t(x).filter((function(e,i){return-1!==t.inArray(i,w)})).toArray()),a.length===x.length){var $,k,D;for(u=0,$=x.length;u<$;u++){if(v=parseInt(a[u],10),c=x[u],isNaN(v))switch(c){case"MM":y=t(f[o].months).filter(C),v=t.inArray(y[0],f[o].months)+1;break;case"M":y=t(f[o].monthsShort).filter(C),v=t.inArray(y[0],f[o].monthsShort)+1}b[c]=v}for(u=0;u<w.length;u++)(D=w[u])in b&&!isNaN(b[D])&&(k=new Date(i),_[D](k,b[D]),isNaN(k)||(i=k))}return i},formatDate:function(e,i,n){if(!e)return"";if("string"==typeof i&&(i=g.parseFormat(i)),i.toDisplay)return i.toDisplay(e,i,n);var s={d:e.getUTCDate(),D:f[n].daysShort[e.getUTCDay()],DD:f[n].days[e.getUTCDay()],m:e.getUTCMonth()+1,M:f[n].monthsShort[e.getUTCMonth()],MM:f[n].months[e.getUTCMonth()],yy:e.getUTCFullYear().toString().substring(2),yyyy:e.getUTCFullYear()};s.dd=(s.d<10?"0":"")+s.d,s.mm=(s.m<10?"0":"")+s.m,e=[];for(var o=t.extend([],i.separators),r=0,a=i.parts.length;r<=a;r++)o.length&&e.push(o.shift()),e.push(s[i.parts[r]]);return e.join("")},headTemplate:'<thead><tr><th colspan="7" class="datepicker-title"></th></tr><tr><th class="prev">'+d.templates.leftArrow+'</th><th colspan="5" class="datepicker-switch"></th><th class="next">'+d.templates.rightArrow+"</th></tr></thead>",contTemplate:'<tbody><tr><td colspan="7"></td></tr></tbody>',footTemplate:'<tfoot><tr><th colspan="7" class="today"></th></tr><tr><th colspan="7" class="clear"></th></tr></tfoot>'};g.template='<div class="datepicker"><div class="datepicker-days"><table class="table-condensed">'+g.headTemplate+"<tbody></tbody>"+g.footTemplate+'</table></div><div class="datepicker-months"><table class="table-condensed">'+g.headTemplate+g.contTemplate+g.footTemplate+'</table></div><div class="datepicker-years"><table class="table-condensed">'+g.headTemplate+g.contTemplate+g.footTemplate+'</table></div><div class="datepicker-decades"><table class="table-condensed">'+g.headTemplate+g.contTemplate+g.footTemplate+'</table></div><div class="datepicker-centuries"><table class="table-condensed">'+g.headTemplate+g.contTemplate+g.footTemplate+"</table></div></div>",t.fn.datepicker.DPGlobal=g,t.fn.datepicker.noConflict=function(){return t.fn.datepicker=h,this},t.fn.datepicker.version="1.7.1",t.fn.datepicker.deprecated=function(t){var e=window.console;e&&e.warn&&e.warn("DEPRECATED: "+t)},t(document).on("focus.datepicker.data-api click.datepicker.data-api",'[data-provide="datepicker"]',(function(e){var i=t(this);i.data("datepicker")||(e.preventDefault(),u.call(i,"show"))})),t((function(){u.call(t('[data-provide="datepicker-inline"]'))}))})),
/*!
 * Bootstrap-select v1.12.4 (http://silviomoreto.github.io/bootstrap-select)
 *
 * Copyright 2013-2017 bootstrap-select
 * Licensed under MIT (https://github.com/silviomoreto/bootstrap-select/blob/master/LICENSE)
 */
function(t,e){"function"==typeof define&&define.amd?define(["jquery"],(function(t){return e(t)})):"object"==typeof module&&module.exports?module.exports=e(require("jquery")):e(t.jQuery)}(this,(function(t){!function(t){"use strict";var e,i,n,s;String.prototype.includes||(e={}.toString,i=function(){try{var t={},e=Object.defineProperty,i=e(t,t,t)&&e}catch(t){}return i}(),n="".indexOf,s=function(t){if(null==this)throw new TypeError;var i=String(this);if(t&&"[object RegExp]"==e.call(t))throw new TypeError;var s=i.length,o=String(t),r=o.length,a=arguments.length>1?arguments[1]:void 0,l=a?Number(a):0;return l!=l&&(l=0),!(r+Math.min(Math.max(l,0),s)>s)&&-1!=n.call(i,o,l)},i?i(String.prototype,"includes",{value:s,configurable:!0,writable:!0}):String.prototype.includes=s),String.prototype.startsWith||function(){var t=function(){try{var t={},e=Object.defineProperty,i=e(t,t,t)&&e}catch(t){}return i}(),e={}.toString,i=function(t){if(null==this)throw new TypeError;var i=String(this);if(t&&"[object RegExp]"==e.call(t))throw new TypeError;var n=i.length,s=String(t),o=s.length,r=arguments.length>1?arguments[1]:void 0,a=r?Number(r):0;a!=a&&(a=0);var l=Math.min(Math.max(a,0),n);if(o+l>n)return!1;for(var c=-1;++c<o;)if(i.charCodeAt(l+c)!=s.charCodeAt(c))return!1;return!0};t?t(String.prototype,"startsWith",{value:i,configurable:!0,writable:!0}):String.prototype.startsWith=i}(),Object.keys||(Object.keys=function(t,e,i){for(e in i=[],t)i.hasOwnProperty.call(t,e)&&i.push(e);return i});var o={useDefault:!1,_set:t.valHooks.select.set};t.valHooks.select.set=function(e,i){return i&&!o.useDefault&&t(e).data("selected",!0),o._set.apply(this,arguments)};var r=null,a=function(){try{return new Event("change"),!0}catch(t){return!1}}();function l(e){return t.each([{re:/[\xC0-\xC6]/g,ch:"A"},{re:/[\xE0-\xE6]/g,ch:"a"},{re:/[\xC8-\xCB]/g,ch:"E"},{re:/[\xE8-\xEB]/g,ch:"e"},{re:/[\xCC-\xCF]/g,ch:"I"},{re:/[\xEC-\xEF]/g,ch:"i"},{re:/[\xD2-\xD6]/g,ch:"O"},{re:/[\xF2-\xF6]/g,ch:"o"},{re:/[\xD9-\xDC]/g,ch:"U"},{re:/[\xF9-\xFC]/g,ch:"u"},{re:/[\xC7-\xE7]/g,ch:"c"},{re:/[\xD1]/g,ch:"N"},{re:/[\xF1]/g,ch:"n"}],(function(){e=e?e.replace(this.re,this.ch):""})),e}t.fn.triggerNative=function(t){var e,i=this[0];i.dispatchEvent?(a?e=new Event(t,{bubbles:!0}):(e=document.createEvent("Event")).initEvent(t,!0,!1),i.dispatchEvent(e)):i.fireEvent?((e=document.createEventObject()).eventType=t,i.fireEvent("on"+t,e)):this.trigger(t)},t.expr.pseudos.icontains=function(e,i,n){var s=t(e).find("a");return(s.data("tokens")||s.text()).toString().toUpperCase().includes(n[3].toUpperCase())},t.expr.pseudos.ibegins=function(e,i,n){var s=t(e).find("a");return(s.data("tokens")||s.text()).toString().toUpperCase().startsWith(n[3].toUpperCase())},t.expr.pseudos.aicontains=function(e,i,n){var s=t(e).find("a");return(s.data("tokens")||s.data("normalizedText")||s.text()).toString().toUpperCase().includes(n[3].toUpperCase())},t.expr.pseudos.aibegins=function(e,i,n){var s=t(e).find("a");return(s.data("tokens")||s.data("normalizedText")||s.text()).toString().toUpperCase().startsWith(n[3].toUpperCase())};var c=function(t){var e=function(e){return t[e]},i="(?:"+Object.keys(t).join("|")+")",n=RegExp(i),s=RegExp(i,"g");return function(t){return t=null==t?"":""+t,n.test(t)?t.replace(s,e):t}},h=c({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"}),u=c({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#x27;":"'","&#x60;":"`"}),d=function(e,i){o.useDefault||(t.valHooks.select.set=o._set,o.useDefault=!0),this.$element=t(e),this.$newElement=null,this.$button=null,this.$menu=null,this.$lis=null,this.options=i,null===this.options.title&&(this.options.title=this.$element.attr("title"));var n=this.options.windowPadding;"number"==typeof n&&(this.options.windowPadding=[n,n,n,n]),this.val=d.prototype.val,this.render=d.prototype.render,this.refresh=d.prototype.refresh,this.setStyle=d.prototype.setStyle,this.selectAll=d.prototype.selectAll,this.deselectAll=d.prototype.deselectAll,this.destroy=d.prototype.destroy,this.remove=d.prototype.remove,this.show=d.prototype.show,this.hide=d.prototype.hide,this.init()};function p(e){var i,n=arguments,s=e;[].shift.apply(n);var o=this.each((function(){var e=t(this);if(e.is("select")){var o=e.data("selectpicker"),r="object"==typeof s&&s;if(o){if(r)for(var a in r)r.hasOwnProperty(a)&&(o.options[a]=r[a])}else{var l=t.extend({},d.DEFAULTS,t.fn.selectpicker.defaults||{},e.data(),r);l.template=t.extend({},d.DEFAULTS.template,t.fn.selectpicker.defaults?t.fn.selectpicker.defaults.template:{},e.data().template,r.template),e.data("selectpicker",o=new d(this,l))}"string"==typeof s&&(i=o[s]instanceof Function?o[s].apply(o,n):o.options[s])}}));return void 0!==i?i:o}d.VERSION="1.12.4",d.DEFAULTS={noneSelectedText:"Nothing selected",noneResultsText:"No results matched {0}",countSelectedText:function(t,e){return 1==t?"{0} item selected":"{0} items selected"},maxOptionsText:function(t,e){return[1==t?"Limit reached ({n} item max)":"Limit reached ({n} items max)",1==e?"Group limit reached ({n} item max)":"Group limit reached ({n} items max)"]},selectAllText:"Select All",deselectAllText:"Deselect All",doneButton:!1,doneButtonText:"Close",multipleSeparator:", ",styleBase:"btn",style:"btn-default",size:"auto",title:null,selectedTextFormat:"values",width:!1,container:!1,hideDisabled:!1,showSubtext:!1,showIcon:!0,showContent:!0,dropupAuto:!0,header:!1,liveSearch:!1,liveSearchPlaceholder:null,liveSearchNormalize:!1,liveSearchStyle:"contains",actionsBox:!1,iconBase:"glyphicon",tickIcon:"glyphicon-ok",showTick:!1,template:{caret:'<span class="caret"></span>'},maxOptions:!1,mobile:!1,selectOnTab:!1,dropdownAlignRight:!1,windowPadding:0},d.prototype={constructor:d,init:function(){var e=this,i=this.$element.attr("id");this.$element.addClass("bs-select-hidden"),this.liObj={},this.multiple=this.$element.prop("multiple"),this.autofocus=this.$element.prop("autofocus"),this.$newElement=this.createView(),this.$element.after(this.$newElement).appendTo(this.$newElement),this.$button=this.$newElement.children("button"),this.$menu=this.$newElement.children(".dropdown-menu"),this.$menuInner=this.$menu.children(".inner"),this.$searchbox=this.$menu.find("input"),this.$element.removeClass("bs-select-hidden"),!0===this.options.dropdownAlignRight&&this.$menu.addClass("dropdown-menu-right"),void 0!==i&&(this.$button.attr("data-id",i),t('label[for="'+i+'"]').click((function(t){t.preventDefault(),e.$button.focus()}))),this.checkDisabled(),this.clickListener(),this.options.liveSearch&&this.liveSearchListener(),this.render(),this.setStyle(),this.setWidth(),this.options.container&&this.selectPosition(),this.$menu.data("this",this),this.$newElement.data("this",this),this.options.mobile&&this.mobile(),this.$newElement.on({"hide.bs.dropdown":function(t){e.$menuInner.attr("aria-expanded",!1),e.$element.trigger("hide.bs.select",t)},"hidden.bs.dropdown":function(t){e.$element.trigger("hidden.bs.select",t)},"show.bs.dropdown":function(t){e.$menuInner.attr("aria-expanded",!0),e.$element.trigger("show.bs.select",t)},"shown.bs.dropdown":function(t){e.$element.trigger("shown.bs.select",t)}}),e.$element[0].hasAttribute("required")&&this.$element.on("invalid",(function(){e.$button.addClass("bs-invalid"),e.$element.on({"focus.bs.select":function(){e.$button.focus(),e.$element.off("focus.bs.select")},"shown.bs.select":function(){e.$element.val(e.$element.val()).off("shown.bs.select")},"rendered.bs.select":function(){this.validity.valid&&e.$button.removeClass("bs-invalid"),e.$element.off("rendered.bs.select")}}),e.$button.on("blur.bs.select",(function(){e.$element.focus().blur(),e.$button.off("blur.bs.select")}))})),setTimeout((function(){e.$element.trigger("loaded.bs.select")}))},createDropdown:function(){var e=this.multiple||this.options.showTick?" show-tick":"",i=this.$element.parent().hasClass("input-group")?" input-group-btn":"",n=this.autofocus?" autofocus":"",s=this.options.header?'<div class="popover-title"><button type="button" class="close" aria-hidden="true">&times;</button>'+this.options.header+"</div>":"",o=this.options.liveSearch?'<div class="bs-searchbox"><input type="text" class="form-control" autocomplete="off"'+(null===this.options.liveSearchPlaceholder?"":' placeholder="'+h(this.options.liveSearchPlaceholder)+'"')+' role="textbox" aria-label="Search"></div>':"",r=this.multiple&&this.options.actionsBox?'<div class="bs-actionsbox"><div class="btn-group btn-group-sm btn-block"><button type="button" class="actions-btn bs-select-all btn btn-default">'+this.options.selectAllText+'</button><button type="button" class="actions-btn bs-deselect-all btn btn-default">'+this.options.deselectAllText+"</button></div></div>":"",a=this.multiple&&this.options.doneButton?'<div class="bs-donebutton"><div class="btn-group btn-block"><button type="button" class="btn btn-sm btn-default">'+this.options.doneButtonText+"</button></div></div>":"",l='<div class="btn-group bootstrap-select'+e+i+'"><button type="button" class="'+this.options.styleBase+' dropdown-toggle" data-toggle="dropdown"'+n+' role="button"><span class="filter-option pull-left"></span>&nbsp;<span class="bs-caret">'+this.options.template.caret+'</span></button><div class="dropdown-menu open" role="combobox">'+s+o+r+'<ul class="dropdown-menu inner" role="listbox" aria-expanded="false"></ul>'+a+"</div></div>";return t(l)},createView:function(){var t=this.createDropdown(),e=this.createLi();return t.find("ul")[0].innerHTML=e,t},reloadLi:function(){var t=this.createLi();this.$menuInner[0].innerHTML=t},createLi:function(){var e=this,i=[],n=0,s=document.createElement("option"),o=-1,r=function(t,e,i,n){return"<li"+(void 0!==i&&""!==i?' class="'+i+'"':"")+(null!=e?' data-original-index="'+e+'"':"")+(null!=n?'data-optgroup="'+n+'"':"")+">"+t+"</li>"},a=function(i,n,s,o){return'<a tabindex="0"'+(void 0!==n?' class="'+n+'"':"")+(s?' style="'+s+'"':"")+(e.options.liveSearchNormalize?' data-normalized-text="'+l(h(t(i).html()))+'"':"")+(void 0!==o||null!==o?' data-tokens="'+o+'"':"")+' role="option">'+i+'<span class="'+e.options.iconBase+" "+e.options.tickIcon+' check-mark"></span></a>'};if(this.options.title&&!this.multiple&&(o--,!this.$element.find(".bs-title-option").length)){var c=this.$element[0];s.className="bs-title-option",s.innerHTML=this.options.title,s.value="",c.insertBefore(s,c.firstChild),void 0===t(c.options[c.selectedIndex]).attr("selected")&&void 0===this.$element.data("selected")&&(s.selected=!0)}var u=this.$element.find("option");return u.each((function(s){var l=t(this);if(o++,!l.hasClass("bs-title-option")){var c,d=this.className||"",p=h(this.style.cssText),f=l.data("content")?l.data("content"):l.html(),g=l.data("tokens")?l.data("tokens"):null,m=void 0!==l.data("subtext")?'<small class="text-muted">'+l.data("subtext")+"</small>":"",v=void 0!==l.data("icon")?'<span class="'+e.options.iconBase+" "+l.data("icon")+'"></span> ':"",y=l.parent(),b="OPTGROUP"===y[0].tagName,w=b&&y[0].disabled,_=this.disabled||w;if(""!==v&&_&&(v="<span>"+v+"</span>"),e.options.hideDisabled&&(_&&!b||w))return c=l.data("prevHiddenIndex"),l.next().data("prevHiddenIndex",void 0!==c?c:s),void o--;if(l.data("content")||(f=v+'<span class="text">'+f+m+"</span>"),b&&!0!==l.data("divider")){if(e.options.hideDisabled&&_){if(void 0===y.data("allOptionsDisabled")){var x=y.children();y.data("allOptionsDisabled",x.filter(":disabled").length===x.length)}if(y.data("allOptionsDisabled"))return void o--}var C=" "+y[0].className||"";if(0===l.index()){n+=1;var $=y[0].label,k=void 0!==y.data("subtext")?'<small class="text-muted">'+y.data("subtext")+"</small>":"";$=(y.data("icon")?'<span class="'+e.options.iconBase+" "+y.data("icon")+'"></span> ':"")+'<span class="text">'+h($)+k+"</span>",0!==s&&i.length>0&&(o++,i.push(r("",null,"divider",n+"div"))),o++,i.push(r($,null,"dropdown-header"+C,n))}if(e.options.hideDisabled&&_)return void o--;i.push(r(a(f,"opt "+d+C,p,g),s,"",n))}else if(!0===l.data("divider"))i.push(r("",s,"divider"));else if(!0===l.data("hidden"))c=l.data("prevHiddenIndex"),l.next().data("prevHiddenIndex",void 0!==c?c:s),i.push(r(a(f,d,p,g),s,"hidden is-hidden"));else{var D=this.previousElementSibling&&"OPTGROUP"===this.previousElementSibling.tagName;if(!D&&e.options.hideDisabled&&void 0!==(c=l.data("prevHiddenIndex"))){var S=u.eq(c)[0].previousElementSibling;S&&"OPTGROUP"===S.tagName&&!S.disabled&&(D=!0)}D&&(o++,i.push(r("",null,"divider",n+"div"))),i.push(r(a(f,d,p,g),s))}e.liObj[s]=o}})),this.multiple||0!==this.$element.find("option:selected").length||this.options.title||this.$element.find("option").eq(0).prop("selected",!0).attr("selected","selected"),i.join("")},findLis:function(){return null==this.$lis&&(this.$lis=this.$menu.find("li")),this.$lis},render:function(e){var i,n=this,s=this.$element.find("option");!1!==e&&s.each((function(t){var e=n.findLis().eq(n.liObj[t]);n.setDisabled(t,this.disabled||"OPTGROUP"===this.parentNode.tagName&&this.parentNode.disabled,e),n.setSelected(t,this.selected,e)})),this.togglePlaceholder(),this.tabIndex();var o=s.map((function(){if(this.selected){if(n.options.hideDisabled&&(this.disabled||"OPTGROUP"===this.parentNode.tagName&&this.parentNode.disabled))return;var e,i=t(this),s=i.data("icon")&&n.options.showIcon?'<i class="'+n.options.iconBase+" "+i.data("icon")+'"></i> ':"";return e=n.options.showSubtext&&i.data("subtext")&&!n.multiple?' <small class="text-muted">'+i.data("subtext")+"</small>":"",void 0!==i.attr("title")?i.attr("title"):i.data("content")&&n.options.showContent?i.data("content").toString():s+i.html()+e}})).toArray(),r=this.multiple?o.join(this.options.multipleSeparator):o[0];if(this.multiple&&this.options.selectedTextFormat.indexOf("count")>-1){var a=this.options.selectedTextFormat.split(">");if(a.length>1&&o.length>a[1]||1==a.length&&o.length>=2){i=this.options.hideDisabled?", [disabled]":"";var l=s.not('[data-divider="true"], [data-hidden="true"]'+i).length;r=("function"==typeof this.options.countSelectedText?this.options.countSelectedText(o.length,l):this.options.countSelectedText).replace("{0}",o.length.toString()).replace("{1}",l.toString())}}null==this.options.title&&(this.options.title=this.$element.attr("title")),"static"==this.options.selectedTextFormat&&(r=this.options.title),r||(r=void 0!==this.options.title?this.options.title:this.options.noneSelectedText),this.$button.attr("title",u(t.trim(r.replace(/<[^>]*>?/g,"")))),this.$button.children(".filter-option").html(r),this.$element.trigger("rendered.bs.select")},setStyle:function(t,e){this.$element.attr("class")&&this.$newElement.addClass(this.$element.attr("class").replace(/selectpicker|mobile-device|bs-select-hidden|validate\[.*\]/gi,""));var i=t||this.options.style;"add"==e?this.$button.addClass(i):"remove"==e?this.$button.removeClass(i):(this.$button.removeClass(this.options.style),this.$button.addClass(i))},liHeight:function(e){if(e||!1!==this.options.size&&!this.sizeInfo){var i=document.createElement("div"),n=document.createElement("div"),s=document.createElement("ul"),o=document.createElement("li"),r=document.createElement("li"),a=document.createElement("a"),l=document.createElement("span"),c=this.options.header&&this.$menu.find(".popover-title").length>0?this.$menu.find(".popover-title")[0].cloneNode(!0):null,h=this.options.liveSearch?document.createElement("div"):null,u=this.options.actionsBox&&this.multiple&&this.$menu.find(".bs-actionsbox").length>0?this.$menu.find(".bs-actionsbox")[0].cloneNode(!0):null,d=this.options.doneButton&&this.multiple&&this.$menu.find(".bs-donebutton").length>0?this.$menu.find(".bs-donebutton")[0].cloneNode(!0):null;if(l.className="text",i.className=this.$menu[0].parentNode.className+" open",n.className="dropdown-menu open",s.className="dropdown-menu inner",o.className="divider",l.appendChild(document.createTextNode("Inner text")),a.appendChild(l),r.appendChild(a),s.appendChild(r),s.appendChild(o),c&&n.appendChild(c),h){var p=document.createElement("input");h.className="bs-searchbox",p.className="form-control",h.appendChild(p),n.appendChild(h)}u&&n.appendChild(u),n.appendChild(s),d&&n.appendChild(d),i.appendChild(n),document.body.appendChild(i);var f=a.offsetHeight,g=c?c.offsetHeight:0,m=h?h.offsetHeight:0,v=u?u.offsetHeight:0,y=d?d.offsetHeight:0,b=t(o).outerHeight(!0),w="function"==typeof getComputedStyle&&getComputedStyle(n),_=w?null:t(n),x={vert:parseInt(w?w.paddingTop:_.css("paddingTop"))+parseInt(w?w.paddingBottom:_.css("paddingBottom"))+parseInt(w?w.borderTopWidth:_.css("borderTopWidth"))+parseInt(w?w.borderBottomWidth:_.css("borderBottomWidth")),horiz:parseInt(w?w.paddingLeft:_.css("paddingLeft"))+parseInt(w?w.paddingRight:_.css("paddingRight"))+parseInt(w?w.borderLeftWidth:_.css("borderLeftWidth"))+parseInt(w?w.borderRightWidth:_.css("borderRightWidth"))},C={vert:x.vert+parseInt(w?w.marginTop:_.css("marginTop"))+parseInt(w?w.marginBottom:_.css("marginBottom"))+2,horiz:x.horiz+parseInt(w?w.marginLeft:_.css("marginLeft"))+parseInt(w?w.marginRight:_.css("marginRight"))+2};document.body.removeChild(i),this.sizeInfo={liHeight:f,headerHeight:g,searchHeight:m,actionsHeight:v,doneButtonHeight:y,dividerHeight:b,menuPadding:x,menuExtras:C}}},setSize:function(){if(this.findLis(),this.liHeight(),this.options.header&&this.$menu.css("padding-top",0),!1!==this.options.size){var e,i,n,s,o,r,a,l,c=this,h=this.$menu,u=this.$menuInner,d=t(window),p=this.$newElement[0].offsetHeight,f=this.$newElement[0].offsetWidth,g=this.sizeInfo.liHeight,m=this.sizeInfo.headerHeight,v=this.sizeInfo.searchHeight,y=this.sizeInfo.actionsHeight,b=this.sizeInfo.doneButtonHeight,w=this.sizeInfo.dividerHeight,_=this.sizeInfo.menuPadding,x=this.sizeInfo.menuExtras,C=this.options.hideDisabled?".disabled":"",$=function(){var e,i=c.$newElement.offset(),n=t(c.options.container);c.options.container&&!n.is("body")?((e=n.offset()).top+=parseInt(n.css("borderTopWidth")),e.left+=parseInt(n.css("borderLeftWidth"))):e={top:0,left:0};var s=c.options.windowPadding;o=i.top-e.top-d.scrollTop(),r=d.height()-o-p-e.top-s[2],a=i.left-e.left-d.scrollLeft(),l=d.width()-a-f-e.left-s[1],o-=s[0],a-=s[3]};if($(),"auto"===this.options.size){var k=function(){var d,p=function(e,i){return function(n){return i?n.classList?n.classList.contains(e):t(n).hasClass(e):!(n.classList?n.classList.contains(e):t(n).hasClass(e))}},w=c.$menuInner[0].getElementsByTagName("li"),C=Array.prototype.filter?Array.prototype.filter.call(w,p("hidden",!1)):c.$lis.not(".hidden"),k=Array.prototype.filter?Array.prototype.filter.call(C,p("dropdown-header",!0)):C.filter(".dropdown-header");$(),e=r-x.vert,i=l-x.horiz,c.options.container?(h.data("height")||h.data("height",h.height()),n=h.data("height"),h.data("width")||h.data("width",h.width()),s=h.data("width")):(n=h.height(),s=h.width()),c.options.dropupAuto&&c.$newElement.toggleClass("dropup",o>r&&e-x.vert<n),c.$newElement.hasClass("dropup")&&(e=o-x.vert),"auto"===c.options.dropdownAlignRight&&h.toggleClass("dropdown-menu-right",a>l&&i-x.horiz<s-f),d=C.length+k.length>3?3*g+x.vert-2:0,h.css({"max-height":e+"px",overflow:"hidden","min-height":d+m+v+y+b+"px"}),u.css({"max-height":e-m-v-y-b-_.vert+"px","overflow-y":"auto","min-height":Math.max(d-_.vert,0)+"px"})};k(),this.$searchbox.off("input.getSize propertychange.getSize").on("input.getSize propertychange.getSize",k),d.off("resize.getSize scroll.getSize").on("resize.getSize scroll.getSize",k)}else if(this.options.size&&"auto"!=this.options.size&&this.$lis.not(C).length>this.options.size){var D=this.$lis.not(".divider").not(C).children().slice(0,this.options.size).last().parent().index(),S=this.$lis.slice(0,D+1).filter(".divider").length;e=g*this.options.size+S*w+_.vert,c.options.container?(h.data("height")||h.data("height",h.height()),n=h.data("height")):n=h.height(),c.options.dropupAuto&&this.$newElement.toggleClass("dropup",o>r&&e-x.vert<n),h.css({"max-height":e+m+v+y+b+"px",overflow:"hidden","min-height":""}),u.css({"max-height":e-_.vert+"px","overflow-y":"auto","min-height":""})}}},setWidth:function(){if("auto"===this.options.width){this.$menu.css("min-width","0");var t=this.$menu.parent().clone().appendTo("body"),e=this.options.container?this.$newElement.clone().appendTo("body"):t,i=t.children(".dropdown-menu").outerWidth(),n=e.css("width","auto").children("button").outerWidth();t.remove(),e.remove(),this.$newElement.css("width",Math.max(i,n)+"px")}else"fit"===this.options.width?(this.$menu.css("min-width",""),this.$newElement.css("width","").addClass("fit-width")):this.options.width?(this.$menu.css("min-width",""),this.$newElement.css("width",this.options.width)):(this.$menu.css("min-width",""),this.$newElement.css("width",""));this.$newElement.hasClass("fit-width")&&"fit"!==this.options.width&&this.$newElement.removeClass("fit-width")},selectPosition:function(){this.$bsContainer=t('<div class="bs-container" />');var e,i,n,s=this,o=t(this.options.container),r=function(t){s.$bsContainer.addClass(t.attr("class").replace(/form-control|fit-width/gi,"")).toggleClass("dropup",t.hasClass("dropup")),e=t.offset(),o.is("body")?i={top:0,left:0}:((i=o.offset()).top+=parseInt(o.css("borderTopWidth"))-o.scrollTop(),i.left+=parseInt(o.css("borderLeftWidth"))-o.scrollLeft()),n=t.hasClass("dropup")?0:t[0].offsetHeight,s.$bsContainer.css({top:e.top-i.top+n,left:e.left-i.left,width:t[0].offsetWidth})};this.$button.on("click",(function(){var e=t(this);s.isDisabled()||(r(s.$newElement),s.$bsContainer.appendTo(s.options.container).toggleClass("open",!e.hasClass("open")).append(s.$menu))})),t(window).on("resize scroll",(function(){r(s.$newElement)})),this.$element.on("hide.bs.select",(function(){s.$menu.data("height",s.$menu.height()),s.$bsContainer.detach()}))},setSelected:function(t,e,i){i||(this.togglePlaceholder(),i=this.findLis().eq(this.liObj[t])),i.toggleClass("selected",e).find("a").attr("aria-selected",e)},setDisabled:function(t,e,i){i||(i=this.findLis().eq(this.liObj[t])),e?i.addClass("disabled").children("a").attr("href","#").attr("tabindex",-1).attr("aria-disabled",!0):i.removeClass("disabled").children("a").removeAttr("href").attr("tabindex",0).attr("aria-disabled",!1)},isDisabled:function(){return this.$element[0].disabled},checkDisabled:function(){var t=this;this.isDisabled()?(this.$newElement.addClass("disabled"),this.$button.addClass("disabled").attr("tabindex",-1).attr("aria-disabled",!0)):(this.$button.hasClass("disabled")&&(this.$newElement.removeClass("disabled"),this.$button.removeClass("disabled").attr("aria-disabled",!1)),-1!=this.$button.attr("tabindex")||this.$element.data("tabindex")||this.$button.removeAttr("tabindex")),this.$button.click((function(){return!t.isDisabled()}))},togglePlaceholder:function(){var t=this.$element.val();this.$button.toggleClass("bs-placeholder",null===t||""===t||t.constructor===Array&&0===t.length)},tabIndex:function(){this.$element.data("tabindex")!==this.$element.attr("tabindex")&&-98!==this.$element.attr("tabindex")&&"-98"!==this.$element.attr("tabindex")&&(this.$element.data("tabindex",this.$element.attr("tabindex")),this.$button.attr("tabindex",this.$element.data("tabindex"))),this.$element.attr("tabindex",-98)},clickListener:function(){var e=this,i=t(document);i.data("spaceSelect",!1),this.$button.on("keyup",(function(t){/(32)/.test(t.keyCode.toString(10))&&i.data("spaceSelect")&&(t.preventDefault(),i.data("spaceSelect",!1))})),this.$button.on("click",(function(){e.setSize()})),this.$element.on("shown.bs.select",(function(){if(e.options.liveSearch||e.multiple){if(!e.multiple){var t=e.liObj[e.$element[0].selectedIndex];if("number"!=typeof t||!1===e.options.size)return;var i=e.$lis.eq(t)[0].offsetTop-e.$menuInner[0].offsetTop;i=i-e.$menuInner[0].offsetHeight/2+e.sizeInfo.liHeight/2,e.$menuInner[0].scrollTop=i}}else e.$menuInner.find(".selected a").focus()})),this.$menuInner.on("click","li a",(function(i){var n=t(this),s=n.parent().data("originalIndex"),o=e.$element.val(),a=e.$element.prop("selectedIndex"),l=!0;if(e.multiple&&1!==e.options.maxOptions&&i.stopPropagation(),i.preventDefault(),!e.isDisabled()&&!n.parent().hasClass("disabled")){var c=e.$element.find("option"),h=c.eq(s),u=h.prop("selected"),d=h.parent("optgroup"),p=e.options.maxOptions,f=d.data("maxOptions")||!1;if(e.multiple){if(h.prop("selected",!u),e.setSelected(s,!u),n.blur(),!1!==p||!1!==f){var g=p<c.filter(":selected").length,m=f<d.find("option:selected").length;if(p&&g||f&&m)if(p&&1==p)c.prop("selected",!1),h.prop("selected",!0),e.$menuInner.find(".selected").removeClass("selected"),e.setSelected(s,!0);else if(f&&1==f){d.find("option:selected").prop("selected",!1),h.prop("selected",!0);var v=n.parent().data("optgroup");e.$menuInner.find('[data-optgroup="'+v+'"]').removeClass("selected"),e.setSelected(s,!0)}else{var y="string"==typeof e.options.maxOptionsText?[e.options.maxOptionsText,e.options.maxOptionsText]:e.options.maxOptionsText,b="function"==typeof y?y(p,f):y,w=b[0].replace("{n}",p),_=b[1].replace("{n}",f),x=t('<div class="notify"></div>');b[2]&&(w=w.replace("{var}",b[2][p>1?0:1]),_=_.replace("{var}",b[2][f>1?0:1])),h.prop("selected",!1),e.$menu.append(x),p&&g&&(x.append(t("<div>"+w+"</div>")),l=!1,e.$element.trigger("maxReached.bs.select")),f&&m&&(x.append(t("<div>"+_+"</div>")),l=!1,e.$element.trigger("maxReachedGrp.bs.select")),setTimeout((function(){e.setSelected(s,!1)}),10),x.delay(750).fadeOut(300,(function(){t(this).remove()}))}}}else c.prop("selected",!1),h.prop("selected",!0),e.$menuInner.find(".selected").removeClass("selected").find("a").attr("aria-selected",!1),e.setSelected(s,!0);!e.multiple||e.multiple&&1===e.options.maxOptions?e.$button.focus():e.options.liveSearch&&e.$searchbox.focus(),l&&(o!=e.$element.val()&&e.multiple||a!=e.$element.prop("selectedIndex")&&!e.multiple)&&(r=[s,h.prop("selected"),u],e.$element.triggerNative("change"))}})),this.$menu.on("click","li.disabled a, .popover-title, .popover-title :not(.close)",(function(i){i.currentTarget==this&&(i.preventDefault(),i.stopPropagation(),e.options.liveSearch&&!t(i.target).hasClass("close")?e.$searchbox.focus():e.$button.focus())})),this.$menuInner.on("click",".divider, .dropdown-header",(function(t){t.preventDefault(),t.stopPropagation(),e.options.liveSearch?e.$searchbox.focus():e.$button.focus()})),this.$menu.on("click",".popover-title .close",(function(){e.$button.click()})),this.$searchbox.on("click",(function(t){t.stopPropagation()})),this.$menu.on("click",".actions-btn",(function(i){e.options.liveSearch?e.$searchbox.focus():e.$button.focus(),i.preventDefault(),i.stopPropagation(),t(this).hasClass("bs-select-all")?e.selectAll():e.deselectAll()})),this.$element.change((function(){e.render(!1),e.$element.trigger("changed.bs.select",r),r=null}))},liveSearchListener:function(){var e=this,i=t('<li class="no-results"></li>');this.$button.on("click.dropdown.data-api",(function(){e.$menuInner.find(".active").removeClass("active"),e.$searchbox.val()&&(e.$searchbox.val(""),e.$lis.not(".is-hidden").removeClass("hidden"),i.parent().length&&i.remove()),e.multiple||e.$menuInner.find(".selected").addClass("active"),setTimeout((function(){e.$searchbox.focus()}),10)})),this.$searchbox.on("click.dropdown.data-api focus.dropdown.data-api touchend.dropdown.data-api",(function(t){t.stopPropagation()})),this.$searchbox.on("input propertychange",(function(){if(e.$lis.not(".is-hidden").removeClass("hidden"),e.$lis.filter(".active").removeClass("active"),i.remove(),e.$searchbox.val()){var n,s=e.$lis.not(".is-hidden, .divider, .dropdown-header");if((n=e.options.liveSearchNormalize?s.not(":a"+e._searchStyle()+'("'+l(e.$searchbox.val())+'")'):s.not(":"+e._searchStyle()+'("'+e.$searchbox.val()+'")')).length===s.length)i.html(e.options.noneResultsText.replace("{0}",'"'+h(e.$searchbox.val())+'"')),e.$menuInner.append(i),e.$lis.addClass("hidden");else{n.addClass("hidden");var o,r=e.$lis.not(".hidden");r.each((function(e){var i=t(this);i.hasClass("divider")?void 0===o?i.addClass("hidden"):(o&&o.addClass("hidden"),o=i):i.hasClass("dropdown-header")&&r.eq(e+1).data("optgroup")!==i.data("optgroup")?i.addClass("hidden"):o=null})),o&&o.addClass("hidden"),s.not(".hidden").first().addClass("active"),e.$menuInner.scrollTop(0)}}}))},_searchStyle:function(){return{begins:"ibegins",startsWith:"ibegins"}[this.options.liveSearchStyle]||"icontains"},val:function(t){return void 0!==t?(this.$element.val(t),this.render(),this.$element):this.$element.val()},changeAll:function(e){if(this.multiple){void 0===e&&(e=!0),this.findLis();var i=this.$element.find("option"),n=this.$lis.not(".divider, .dropdown-header, .disabled, .hidden"),s=n.length,o=[];if(e){if(n.filter(".selected").length===n.length)return}else if(0===n.filter(".selected").length)return;n.toggleClass("selected",e);for(var r=0;r<s;r++){var a=n[r].getAttribute("data-original-index");o[o.length]=i.eq(a)[0]}t(o).prop("selected",e),this.render(!1),this.togglePlaceholder(),this.$element.triggerNative("change")}},selectAll:function(){return this.changeAll(!0)},deselectAll:function(){return this.changeAll(!1)},toggle:function(t){(t=t||window.event)&&t.stopPropagation(),this.$button.trigger("click")},keydown:function(e){var i,n,s,o,r=t(this),a=(r.is("input")?r.parent().parent():r.parent()).data("this"),l=":not(.disabled, .hidden, .dropdown-header, .divider)",c={32:" ",48:"0",49:"1",50:"2",51:"3",52:"4",53:"5",54:"6",55:"7",56:"8",57:"9",59:";",65:"a",66:"b",67:"c",68:"d",69:"e",70:"f",71:"g",72:"h",73:"i",74:"j",75:"k",76:"l",77:"m",78:"n",79:"o",80:"p",81:"q",82:"r",83:"s",84:"t",85:"u",86:"v",87:"w",88:"x",89:"y",90:"z",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9"};if(!(o=a.$newElement.hasClass("open"))&&(e.keyCode>=48&&e.keyCode<=57||e.keyCode>=96&&e.keyCode<=105||e.keyCode>=65&&e.keyCode<=90))return a.options.container?a.$button.trigger("click"):(a.setSize(),a.$menu.parent().addClass("open"),o=!0),void a.$searchbox.focus();if(a.options.liveSearch&&/(^9$|27)/.test(e.keyCode.toString(10))&&o&&(e.preventDefault(),e.stopPropagation(),a.$menuInner.click(),a.$button.focus()),/(38|40)/.test(e.keyCode.toString(10))){if(!(i=a.$lis.filter(l)).length)return;n=a.options.liveSearch?i.index(i.filter(".active")):i.index(i.find("a").filter(":focus").parent()),s=a.$menuInner.data("prevIndex"),38==e.keyCode?(!a.options.liveSearch&&n!=s||-1==n||n--,n<0&&(n+=i.length)):40==e.keyCode&&((a.options.liveSearch||n==s)&&n++,n%=i.length),a.$menuInner.data("prevIndex",n),a.options.liveSearch?(e.preventDefault(),r.hasClass("dropdown-toggle")||(i.removeClass("active").eq(n).addClass("active").children("a").focus(),r.focus())):i.eq(n).children("a").focus()}else if(!r.is("input")){var h,u=[];(i=a.$lis.filter(l)).each((function(i){t.trim(t(this).children("a").text().toLowerCase()).substring(0,1)==c[e.keyCode]&&u.push(i)})),h=t(document).data("keycount"),h++,t(document).data("keycount",h),t.trim(t(":focus").text().toLowerCase()).substring(0,1)!=c[e.keyCode]?(h=1,t(document).data("keycount",h)):h>=u.length&&(t(document).data("keycount",0),h>u.length&&(h=1)),i.eq(u[h-1]).children("a").focus()}if((/(13|32)/.test(e.keyCode.toString(10))||/(^9$)/.test(e.keyCode.toString(10))&&a.options.selectOnTab)&&o){if(/(32)/.test(e.keyCode.toString(10))||e.preventDefault(),a.options.liveSearch)/(32)/.test(e.keyCode.toString(10))||(a.$menuInner.find(".active a").click(),r.focus());else{var d=t(":focus");d.click(),d.focus(),e.preventDefault(),t(document).data("spaceSelect",!0)}t(document).data("keycount",0)}(/(^9$|27)/.test(e.keyCode.toString(10))&&o&&(a.multiple||a.options.liveSearch)||/(27)/.test(e.keyCode.toString(10))&&!o)&&(a.$menu.parent().removeClass("open"),a.options.container&&a.$newElement.removeClass("open"),a.$button.focus())},mobile:function(){this.$element.addClass("mobile-device")},refresh:function(){this.$lis=null,this.liObj={},this.reloadLi(),this.render(),this.checkDisabled(),this.liHeight(!0),this.setStyle(),this.setWidth(),this.$lis&&this.$searchbox.trigger("propertychange"),this.$element.trigger("refreshed.bs.select")},hide:function(){this.$newElement.hide()},show:function(){this.$newElement.show()},remove:function(){this.$newElement.remove(),this.$element.remove()},destroy:function(){this.$newElement.before(this.$element).remove(),this.$bsContainer?this.$bsContainer.remove():this.$menu.remove(),this.$element.off(".bs.select").removeData("selectpicker").removeClass("bs-select-hidden selectpicker")}};var f=t.fn.selectpicker;t.fn.selectpicker=p,t.fn.selectpicker.Constructor=d,t.fn.selectpicker.noConflict=function(){return t.fn.selectpicker=f,this},t(document).data("keycount",0).on("keydown.bs.select",'.bootstrap-select [data-toggle=dropdown], .bootstrap-select [role="listbox"], .bs-searchbox input',d.prototype.keydown).on("focusin.modal",'.bootstrap-select [data-toggle=dropdown], .bootstrap-select [role="listbox"], .bs-searchbox input',(function(t){t.stopPropagation()})),t(window).on("load.bs.select.data-api",(function(){t(".selectpicker").each((function(){var e=t(this);p.call(e,e.data())}))}))}(t)})),function(t){"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof module&&module.exports?module.exports=function(e,i){return void 0===i&&(i="undefined"!=typeof window?require("jquery"):require("jquery")(e)),t(i),i}:t(jQuery)}((function(t){var e=function(){if(t&&t.fn&&t.fn.select2&&t.fn.select2.amd)var e=t.fn.select2.amd;return function(){var t,i,n;e&&e.requirejs||(e?i=e:e={},function(e){function s(t,e){return w.call(t,e)}function o(t,e){var i,n,s,o,r,a,l,c,h,u,d,p=e&&e.split("/"),f=y.map,g=f&&f["*"]||{};if(t){for(r=(t=t.split("/")).length-1,y.nodeIdCompat&&x.test(t[r])&&(t[r]=t[r].replace(x,"")),"."===t[0].charAt(0)&&p&&(t=p.slice(0,p.length-1).concat(t)),h=0;h<t.length;h++)if("."===(d=t[h]))t.splice(h,1),h-=1;else if(".."===d){if(0===h||1===h&&".."===t[2]||".."===t[h-1])continue;h>0&&(t.splice(h-1,2),h-=2)}t=t.join("/")}if((p||g)&&f){for(h=(i=t.split("/")).length;h>0;h-=1){if(n=i.slice(0,h).join("/"),p)for(u=p.length;u>0;u-=1)if((s=f[p.slice(0,u).join("/")])&&(s=s[n])){o=s,a=h;break}if(o)break;!l&&g&&g[n]&&(l=g[n],c=h)}!o&&l&&(o=l,a=c),o&&(i.splice(0,a,o),t=i.join("/"))}return t}function r(t,i){return function(){var n=_.call(arguments,0);return"string"!=typeof n[0]&&1===n.length&&n.push(null),p.apply(e,n.concat([t,i]))}}function a(t){return function(e){m[t]=e}}function l(t){if(s(v,t)){var i=v[t];delete v[t],b[t]=!0,d.apply(e,i)}if(!s(m,t)&&!s(b,t))throw new Error("No "+t);return m[t]}function c(t){var e,i=t?t.indexOf("!"):-1;return i>-1&&(e=t.substring(0,i),t=t.substring(i+1,t.length)),[e,t]}function h(t){return t?c(t):[]}function u(t){return function(){return y&&y.config&&y.config[t]||{}}}var d,p,f,g,m={},v={},y={},b={},w=Object.prototype.hasOwnProperty,_=[].slice,x=/\.js$/;f=function(t,e){var i,n=c(t),s=n[0],r=e[1];return t=n[1],s&&(i=l(s=o(s,r))),s?t=i&&i.normalize?i.normalize(t,function(t){return function(e){return o(e,t)}}(r)):o(t,r):(s=(n=c(t=o(t,r)))[0],t=n[1],s&&(i=l(s))),{f:s?s+"!"+t:t,n:t,pr:s,p:i}},g={require:function(t){return r(t)},exports:function(t){var e=m[t];return void 0!==e?e:m[t]={}},module:function(t){return{id:t,uri:"",exports:m[t],config:u(t)}}},d=function(t,i,n,o){var c,u,d,p,y,w,_,x=[],C=typeof n;if(w=h(o=o||t),"undefined"===C||"function"===C){for(i=!i.length&&n.length?["require","exports","module"]:i,y=0;y<i.length;y+=1)if("require"===(u=(p=f(i[y],w)).f))x[y]=g.require(t);else if("exports"===u)x[y]=g.exports(t),_=!0;else if("module"===u)c=x[y]=g.module(t);else if(s(m,u)||s(v,u)||s(b,u))x[y]=l(u);else{if(!p.p)throw new Error(t+" missing "+u);p.p.load(p.n,r(o,!0),a(u),{}),x[y]=m[u]}d=n?n.apply(m[t],x):void 0,t&&(c&&c.exports!==e&&c.exports!==m[t]?m[t]=c.exports:d===e&&_||(m[t]=d))}else t&&(m[t]=n)},t=i=p=function(t,i,n,s,o){if("string"==typeof t)return g[t]?g[t](i):l(f(t,h(i)).f);if(!t.splice){if((y=t).deps&&p(y.deps,y.callback),!i)return;i.splice?(t=i,i=n,n=null):t=e}return i=i||function(){},"function"==typeof n&&(n=s,s=o),s?d(e,t,i,n):setTimeout((function(){d(e,t,i,n)}),4),p},p.config=function(t){return p(t)},t._defined=m,n=function(t,e,i){if("string"!=typeof t)throw new Error("See almond README: incorrect module build, no module name");e.splice||(i=e,e=[]),s(m,t)||s(v,t)||(v[t]=[t,e,i])},n.amd={jQuery:!0}}(),e.requirejs=t,e.require=i,e.define=n)}(),e.define("almond",(function(){})),e.define("jquery",[],(function(){var e=t||$;return null==e&&console&&console.error&&console.error("Select2: An instance of jQuery or a jQuery-compatible library was not found. Make sure that you are including jQuery before Select2 on your web page."),e})),e.define("select2/utils",["jquery"],(function(t){function e(t){var e=t.prototype,i=[];for(var n in e)"function"==typeof e[n]&&"constructor"!==n&&i.push(n);return i}var i={Extend:function(t,e){function i(){this.constructor=t}var n={}.hasOwnProperty;for(var s in e)n.call(e,s)&&(t[s]=e[s]);return i.prototype=e.prototype,t.prototype=new i,t.__super__=e.prototype,t},Decorate:function(t,i){function n(){var e=Array.prototype.unshift,n=i.prototype.constructor.length,s=t.prototype.constructor;n>0&&(e.call(arguments,t.prototype.constructor),s=i.prototype.constructor),s.apply(this,arguments)}var s=e(i),o=e(t);i.displayName=t.displayName,n.prototype=new function(){this.constructor=n};for(var r=0;r<o.length;r++){var a=o[r];n.prototype[a]=t.prototype[a]}for(var l=function(t){var e=function(){};t in n.prototype&&(e=n.prototype[t]);var s=i.prototype[t];return function(){return Array.prototype.unshift.call(arguments,e),s.apply(this,arguments)}},c=0;c<s.length;c++){var h=s[c];n.prototype[h]=l(h)}return n}},n=function(){this.listeners={}};n.prototype.on=function(t,e){this.listeners=this.listeners||{},t in this.listeners?this.listeners[t].push(e):this.listeners[t]=[e]},n.prototype.trigger=function(t){var e=Array.prototype.slice,i=e.call(arguments,1);this.listeners=this.listeners||{},null==i&&(i=[]),0===i.length&&i.push({}),i[0]._type=t,t in this.listeners&&this.invoke(this.listeners[t],e.call(arguments,1)),"*"in this.listeners&&this.invoke(this.listeners["*"],arguments)},n.prototype.invoke=function(t,e){for(var i=0,n=t.length;i<n;i++)t[i].apply(this,e)},i.Observable=n,i.generateChars=function(t){for(var e="",i=0;i<t;i++)e+=Math.floor(36*Math.random()).toString(36);return e},i.bind=function(t,e){return function(){t.apply(e,arguments)}},i._convertData=function(t){for(var e in t){var i=e.split("-"),n=t;if(1!==i.length){for(var s=0;s<i.length;s++){var o=i[s];(o=o.substring(0,1).toLowerCase()+o.substring(1))in n||(n[o]={}),s==i.length-1&&(n[o]=t[e]),n=n[o]}delete t[e]}}return t},i.hasScroll=function(e,i){var n=t(i),s=i.style.overflowX,o=i.style.overflowY;return(s!==o||"hidden"!==o&&"visible"!==o)&&("scroll"===s||"scroll"===o||n.innerHeight()<i.scrollHeight||n.innerWidth()<i.scrollWidth)},i.escapeMarkup=function(t){var e={"\\":"&#92;","&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#47;"};return"string"!=typeof t?t:String(t).replace(/[&<>"'\/\\]/g,(function(t){return e[t]}))},i.appendMany=function(e,i){if("1.7"===t.fn.jquery.substr(0,3)){var n=t();t.map(i,(function(t){n=n.add(t)})),i=n}e.append(i)},i.__cache={};var s=0;return i.GetUniqueElementId=function(t){var e=t.getAttribute("data-select2-id");return null==e&&(t.id?(e=t.id,t.setAttribute("data-select2-id",e)):(t.setAttribute("data-select2-id",++s),e=s.toString())),e},i.StoreData=function(t,e,n){var s=i.GetUniqueElementId(t);i.__cache[s]||(i.__cache[s]={}),i.__cache[s][e]=n},i.GetData=function(e,n){var s=i.GetUniqueElementId(e);return n?i.__cache[s]&&null!=i.__cache[s][n]?i.__cache[s][n]:t(e).data(n):i.__cache[s]},i.RemoveData=function(t){var e=i.GetUniqueElementId(t);null!=i.__cache[e]&&delete i.__cache[e]},i})),e.define("select2/results",["jquery","./utils"],(function(t,e){function i(t,e,n){this.$element=t,this.data=n,this.options=e,i.__super__.constructor.call(this)}return e.Extend(i,e.Observable),i.prototype.render=function(){var e=t('<ul class="select2-results__options" role="tree"></ul>');return this.options.get("multiple")&&e.attr("aria-multiselectable","true"),this.$results=e,e},i.prototype.clear=function(){this.$results.empty()},i.prototype.displayMessage=function(e){var i=this.options.get("escapeMarkup");this.clear(),this.hideLoading();var n=t('<li role="treeitem" aria-live="assertive" class="select2-results__option"></li>'),s=this.options.get("translations").get(e.message);n.append(i(s(e.args))),n[0].className+=" select2-results__message",this.$results.append(n)},i.prototype.hideMessages=function(){this.$results.find(".select2-results__message").remove()},i.prototype.append=function(t){this.hideLoading();var e=[];if(null!=t.results&&0!==t.results.length){t.results=this.sort(t.results);for(var i=0;i<t.results.length;i++){var n=t.results[i],s=this.option(n);e.push(s)}this.$results.append(e)}else 0===this.$results.children().length&&this.trigger("results:message",{message:"noResults"})},i.prototype.position=function(t,e){e.find(".select2-results").append(t)},i.prototype.sort=function(t){return this.options.get("sorter")(t)},i.prototype.highlightFirstItem=function(){var t=this.$results.find(".select2-results__option[aria-selected]"),e=t.filter("[aria-selected=true]");e.length>0?e.first().trigger("mouseenter"):t.first().trigger("mouseenter"),this.ensureHighlightVisible()},i.prototype.setClasses=function(){var i=this;this.data.current((function(n){var s=t.map(n,(function(t){return t.id.toString()}));i.$results.find(".select2-results__option[aria-selected]").each((function(){var i=t(this),n=e.GetData(this,"data"),o=""+n.id;null!=n.element&&n.element.selected||null==n.element&&t.inArray(o,s)>-1?i.attr("aria-selected","true"):i.attr("aria-selected","false")}))}))},i.prototype.showLoading=function(t){this.hideLoading();var e={disabled:!0,loading:!0,text:this.options.get("translations").get("searching")(t)},i=this.option(e);i.className+=" loading-results",this.$results.prepend(i)},i.prototype.hideLoading=function(){this.$results.find(".loading-results").remove()},i.prototype.option=function(i){var n=document.createElement("li");n.className="select2-results__option";var s={role:"treeitem","aria-selected":"false"};for(var o in i.disabled&&(delete s["aria-selected"],s["aria-disabled"]="true"),null==i.id&&delete s["aria-selected"],null!=i._resultId&&(n.id=i._resultId),i.title&&(n.title=i.title),i.children&&(s.role="group",s["aria-label"]=i.text,delete s["aria-selected"]),s){var r=s[o];n.setAttribute(o,r)}if(i.children){var a=t(n),l=document.createElement("strong");l.className="select2-results__group",t(l),this.template(i,l);for(var c=[],h=0;h<i.children.length;h++){var u=i.children[h],d=this.option(u);c.push(d)}var p=t("<ul></ul>",{class:"select2-results__options select2-results__options--nested"});p.append(c),a.append(l),a.append(p)}else this.template(i,n);return e.StoreData(n,"data",i),n},i.prototype.bind=function(i,n){var s=this,o=i.id+"-results";this.$results.attr("id",o),i.on("results:all",(function(t){s.clear(),s.append(t.data),i.isOpen()&&(s.setClasses(),s.highlightFirstItem())})),i.on("results:append",(function(t){s.append(t.data),i.isOpen()&&s.setClasses()})),i.on("query",(function(t){s.hideMessages(),s.showLoading(t)})),i.on("select",(function(){i.isOpen()&&(s.setClasses(),s.highlightFirstItem())})),i.on("unselect",(function(){i.isOpen()&&(s.setClasses(),s.highlightFirstItem())})),i.on("open",(function(){s.$results.attr("aria-expanded","true"),s.$results.attr("aria-hidden","false"),s.setClasses(),s.ensureHighlightVisible()})),i.on("close",(function(){s.$results.attr("aria-expanded","false"),s.$results.attr("aria-hidden","true"),s.$results.removeAttr("aria-activedescendant")})),i.on("results:toggle",(function(){var t=s.getHighlightedResults();0!==t.length&&t.trigger("mouseup")})),i.on("results:select",(function(){var t=s.getHighlightedResults();if(0!==t.length){var i=e.GetData(t[0],"data");"true"==t.attr("aria-selected")?s.trigger("close",{}):s.trigger("select",{data:i})}})),i.on("results:previous",(function(){var t=s.getHighlightedResults(),e=s.$results.find("[aria-selected]"),i=e.index(t);if(0!==i){var n=i-1;0===t.length&&(n=0);var o=e.eq(n);o.trigger("mouseenter");var r=s.$results.offset().top,a=o.offset().top,l=s.$results.scrollTop()+(a-r);0===n?s.$results.scrollTop(0):a-r<0&&s.$results.scrollTop(l)}})),i.on("results:next",(function(){var t=s.getHighlightedResults(),e=s.$results.find("[aria-selected]"),i=e.index(t)+1;if(!(i>=e.length)){var n=e.eq(i);n.trigger("mouseenter");var o=s.$results.offset().top+s.$results.outerHeight(!1),r=n.offset().top+n.outerHeight(!1),a=s.$results.scrollTop()+r-o;0===i?s.$results.scrollTop(0):r>o&&s.$results.scrollTop(a)}})),i.on("results:focus",(function(t){t.element.addClass("select2-results__option--highlighted")})),i.on("results:message",(function(t){s.displayMessage(t)})),t.fn.mousewheel&&this.$results.on("mousewheel",(function(t){var e=s.$results.scrollTop(),i=s.$results.get(0).scrollHeight-e+t.deltaY,n=t.deltaY>0&&e-t.deltaY<=0,o=t.deltaY<0&&i<=s.$results.height();n?(s.$results.scrollTop(0),t.preventDefault(),t.stopPropagation()):o&&(s.$results.scrollTop(s.$results.get(0).scrollHeight-s.$results.height()),t.preventDefault(),t.stopPropagation())})),this.$results.on("mouseup",".select2-results__option[aria-selected]",(function(i){var n=t(this),o=e.GetData(this,"data");"true"!==n.attr("aria-selected")?s.trigger("select",{originalEvent:i,data:o}):s.options.get("multiple")?s.trigger("unselect",{originalEvent:i,data:o}):s.trigger("close",{})})),this.$results.on("mouseenter",".select2-results__option[aria-selected]",(function(i){var n=e.GetData(this,"data");s.getHighlightedResults().removeClass("select2-results__option--highlighted"),s.trigger("results:focus",{data:n,element:t(this)})}))},i.prototype.getHighlightedResults=function(){return this.$results.find(".select2-results__option--highlighted")},i.prototype.destroy=function(){this.$results.remove()},i.prototype.ensureHighlightVisible=function(){var t=this.getHighlightedResults();if(0!==t.length){var e=this.$results.find("[aria-selected]").index(t),i=this.$results.offset().top,n=t.offset().top,s=this.$results.scrollTop()+(n-i),o=n-i;s-=2*t.outerHeight(!1),e<=2?this.$results.scrollTop(0):(o>this.$results.outerHeight()||o<0)&&this.$results.scrollTop(s)}},i.prototype.template=function(e,i){var n=this.options.get("templateResult"),s=this.options.get("escapeMarkup"),o=n(e,i);null==o?i.style.display="none":"string"==typeof o?i.innerHTML=s(o):t(i).append(o)},i})),e.define("select2/keys",[],(function(){return{BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46}})),e.define("select2/selection/base",["jquery","../utils","../keys"],(function(t,e,i){function n(t,e){this.$element=t,this.options=e,n.__super__.constructor.call(this)}return e.Extend(n,e.Observable),n.prototype.render=function(){var i=t('<span class="select2-selection" role="combobox"  aria-haspopup="true" aria-expanded="false"></span>');return this._tabindex=0,null!=e.GetData(this.$element[0],"old-tabindex")?this._tabindex=e.GetData(this.$element[0],"old-tabindex"):null!=this.$element.attr("tabindex")&&(this._tabindex=this.$element.attr("tabindex")),i.attr("title",this.$element.attr("title")),i.attr("tabindex",this._tabindex),this.$selection=i,i},n.prototype.bind=function(t,e){var n=this,s=(t.id,t.id+"-results");this.container=t,this.$selection.on("focus",(function(t){n.trigger("focus",t)})),this.$selection.on("blur",(function(t){n._handleBlur(t)})),this.$selection.on("keydown",(function(t){n.trigger("keypress",t),t.which===i.SPACE&&t.preventDefault()})),t.on("results:focus",(function(t){n.$selection.attr("aria-activedescendant",t.data._resultId)})),t.on("selection:update",(function(t){n.update(t.data)})),t.on("open",(function(){n.$selection.attr("aria-expanded","true"),n.$selection.attr("aria-owns",s),n._attachCloseHandler(t)})),t.on("close",(function(){n.$selection.attr("aria-expanded","false"),n.$selection.removeAttr("aria-activedescendant"),n.$selection.removeAttr("aria-owns"),n.$selection.focus(),n._detachCloseHandler(t)})),t.on("enable",(function(){n.$selection.attr("tabindex",n._tabindex)})),t.on("disable",(function(){n.$selection.attr("tabindex","-1")}))},n.prototype._handleBlur=function(e){var i=this;window.setTimeout((function(){document.activeElement==i.$selection[0]||t.contains(i.$selection[0],document.activeElement)||i.trigger("blur",e)}),1)},n.prototype._attachCloseHandler=function(i){t(document.body).on("mousedown.select2."+i.id,(function(i){var n=t(i.target).closest(".select2");t(".select2.select2-container--open").each((function(){t(this),this!=n[0]&&e.GetData(this,"element").select2("close")}))}))},n.prototype._detachCloseHandler=function(e){t(document.body).off("mousedown.select2."+e.id)},n.prototype.position=function(t,e){e.find(".selection").append(t)},n.prototype.destroy=function(){this._detachCloseHandler(this.container)},n.prototype.update=function(t){throw new Error("The `update` method must be defined in child classes.")},n})),e.define("select2/selection/single",["jquery","./base","../utils","../keys"],(function(t,e,i,n){function s(){s.__super__.constructor.apply(this,arguments)}return i.Extend(s,e),s.prototype.render=function(){var t=s.__super__.render.call(this);return t.addClass("select2-selection--single"),t.html('<span class="select2-selection__rendered"></span><span class="select2-selection__arrow" role="presentation"><b role="presentation"></b></span>'),t},s.prototype.bind=function(t,e){var i=this;s.__super__.bind.apply(this,arguments);var n=t.id+"-container";this.$selection.find(".select2-selection__rendered").attr("id",n).attr("role","textbox").attr("aria-readonly","true"),this.$selection.attr("aria-labelledby",n),this.$selection.on("mousedown",(function(t){1===t.which&&i.trigger("toggle",{originalEvent:t})})),this.$selection.on("focus",(function(t){})),this.$selection.on("blur",(function(t){})),t.on("focus",(function(e){t.isOpen()||i.$selection.focus()}))},s.prototype.clear=function(){var t=this.$selection.find(".select2-selection__rendered");t.empty(),t.removeAttr("title")},s.prototype.display=function(t,e){var i=this.options.get("templateSelection");return this.options.get("escapeMarkup")(i(t,e))},s.prototype.selectionContainer=function(){return t("<span></span>")},s.prototype.update=function(t){if(0!==t.length){var e=t[0],i=this.$selection.find(".select2-selection__rendered"),n=this.display(e,i);i.empty().append(n),i.attr("title",e.title||e.text)}else this.clear()},s})),e.define("select2/selection/multiple",["jquery","./base","../utils"],(function(t,e,i){function n(t,e){n.__super__.constructor.apply(this,arguments)}return i.Extend(n,e),n.prototype.render=function(){var t=n.__super__.render.call(this);return t.addClass("select2-selection--multiple"),t.html('<ul class="select2-selection__rendered"></ul>'),t},n.prototype.bind=function(e,s){var o=this;n.__super__.bind.apply(this,arguments),this.$selection.on("click",(function(t){o.trigger("toggle",{originalEvent:t})})),this.$selection.on("click",".select2-selection__choice__remove",(function(e){if(!o.options.get("disabled")){var n=t(this).parent(),s=i.GetData(n[0],"data");o.trigger("unselect",{originalEvent:e,data:s})}}))},n.prototype.clear=function(){var t=this.$selection.find(".select2-selection__rendered");t.empty(),t.removeAttr("title")},n.prototype.display=function(t,e){var i=this.options.get("templateSelection");return this.options.get("escapeMarkup")(i(t,e))},n.prototype.selectionContainer=function(){return t('<li class="select2-selection__choice"><span class="select2-selection__choice__remove" role="presentation">&times;</span></li>')},n.prototype.update=function(t){if(this.clear(),0!==t.length){for(var e=[],n=0;n<t.length;n++){var s=t[n],o=this.selectionContainer(),r=this.display(s,o);o.append(r),o.attr("title",s.title||s.text),i.StoreData(o[0],"data",s),e.push(o)}var a=this.$selection.find(".select2-selection__rendered");i.appendMany(a,e)}},n})),e.define("select2/selection/placeholder",["../utils"],(function(t){function e(t,e,i){this.placeholder=this.normalizePlaceholder(i.get("placeholder")),t.call(this,e,i)}return e.prototype.normalizePlaceholder=function(t,e){return"string"==typeof e&&(e={id:"",text:e}),e},e.prototype.createPlaceholder=function(t,e){var i=this.selectionContainer();return i.html(this.display(e)),i.addClass("select2-selection__placeholder").removeClass("select2-selection__choice"),i},e.prototype.update=function(t,e){var i=1==e.length&&e[0].id!=this.placeholder.id;if(e.length>1||i)return t.call(this,e);this.clear();var n=this.createPlaceholder(this.placeholder);this.$selection.find(".select2-selection__rendered").append(n)},e})),e.define("select2/selection/allowClear",["jquery","../keys","../utils"],(function(t,e,i){function n(){}return n.prototype.bind=function(t,e,i){var n=this;t.call(this,e,i),null==this.placeholder&&this.options.get("debug")&&window.console&&console.error&&console.error("Select2: The `allowClear` option should be used in combination with the `placeholder` option."),this.$selection.on("mousedown",".select2-selection__clear",(function(t){n._handleClear(t)})),e.on("keypress",(function(t){n._handleKeyboardClear(t,e)}))},n.prototype._handleClear=function(t,e){if(!this.options.get("disabled")){var n=this.$selection.find(".select2-selection__clear");if(0!==n.length){e.stopPropagation();var s=i.GetData(n[0],"data"),o=this.$element.val();this.$element.val(this.placeholder.id);var r={data:s};if(this.trigger("clear",r),r.prevented)return void this.$element.val(o);for(var a=0;a<s.length;a++)if(r={data:s[a]},this.trigger("unselect",r),r.prevented)return void this.$element.val(o);this.$element.trigger("change"),this.trigger("toggle",{})}}},n.prototype._handleKeyboardClear=function(t,i,n){n.isOpen()||i.which!=e.DELETE&&i.which!=e.BACKSPACE||this._handleClear(i)},n.prototype.update=function(e,n){if(e.call(this,n),!(this.$selection.find(".select2-selection__placeholder").length>0||0===n.length)){var s=t('<span class="select2-selection__clear">&times;</span>');i.StoreData(s[0],"data",n),this.$selection.find(".select2-selection__rendered").prepend(s)}},n})),e.define("select2/selection/search",["jquery","../utils","../keys"],(function(t,e,i){function n(t,e,i){t.call(this,e,i)}return n.prototype.render=function(e){var i=t('<li class="select2-search select2-search--inline"><input class="select2-search__field" type="search" tabindex="-1" autocomplete="off" autocorrect="off" autocapitalize="none" spellcheck="false" role="textbox" aria-autocomplete="list" /></li>');this.$searchContainer=i,this.$search=i.find("input");var n=e.call(this);return this._transferTabIndex(),n},n.prototype.bind=function(t,n,s){var o=this;t.call(this,n,s),n.on("open",(function(){o.$search.trigger("focus")})),n.on("close",(function(){o.$search.val(""),o.$search.removeAttr("aria-activedescendant"),o.$search.trigger("focus")})),n.on("enable",(function(){o.$search.prop("disabled",!1),o._transferTabIndex()})),n.on("disable",(function(){o.$search.prop("disabled",!0)})),n.on("focus",(function(t){o.$search.trigger("focus")})),n.on("results:focus",(function(t){o.$search.attr("aria-activedescendant",t.id)})),this.$selection.on("focusin",".select2-search--inline",(function(t){o.trigger("focus",t)})),this.$selection.on("focusout",".select2-search--inline",(function(t){o._handleBlur(t)})),this.$selection.on("keydown",".select2-search--inline",(function(t){if(t.stopPropagation(),o.trigger("keypress",t),o._keyUpPrevented=t.isDefaultPrevented(),t.which===i.BACKSPACE&&""===o.$search.val()){var n=o.$searchContainer.prev(".select2-selection__choice");if(n.length>0){var s=e.GetData(n[0],"data");o.searchRemoveChoice(s),t.preventDefault()}}}));var r=document.documentMode,a=r&&r<=11;this.$selection.on("input.searchcheck",".select2-search--inline",(function(t){a?o.$selection.off("input.search input.searchcheck"):o.$selection.off("keyup.search")})),this.$selection.on("keyup.search input.search",".select2-search--inline",(function(t){if(a&&"input"===t.type)o.$selection.off("input.search input.searchcheck");else{var e=t.which;e!=i.SHIFT&&e!=i.CTRL&&e!=i.ALT&&e!=i.TAB&&o.handleSearch(t)}}))},n.prototype._transferTabIndex=function(t){this.$search.attr("tabindex",this.$selection.attr("tabindex")),this.$selection.attr("tabindex","-1")},n.prototype.createPlaceholder=function(t,e){this.$search.attr("placeholder",e.text)},n.prototype.update=function(t,e){var i=this.$search[0]==document.activeElement;this.$search.attr("placeholder",""),t.call(this,e),this.$selection.find(".select2-selection__rendered").append(this.$searchContainer),this.resizeSearch(),i&&this.$search.focus()},n.prototype.handleSearch=function(){if(this.resizeSearch(),!this._keyUpPrevented){var t=this.$search.val();this.trigger("query",{term:t})}this._keyUpPrevented=!1},n.prototype.searchRemoveChoice=function(t,e){this.trigger("unselect",{data:e}),this.$search.val(e.text),this.handleSearch()},n.prototype.resizeSearch=function(){this.$search.css("width","25px");var t="";t=""!==this.$search.attr("placeholder")?this.$selection.find(".select2-selection__rendered").innerWidth():.75*(this.$search.val().length+1)+"em",this.$search.css("width",t)},n})),e.define("select2/selection/eventRelay",["jquery"],(function(t){function e(){}return e.prototype.bind=function(e,i,n){var s=this,o=["open","opening","close","closing","select","selecting","unselect","unselecting","clear","clearing"],r=["opening","closing","selecting","unselecting","clearing"];e.call(this,i,n),i.on("*",(function(e,i){if(-1!==t.inArray(e,o)){i=i||{};var n=t.Event("select2:"+e,{params:i});s.$element.trigger(n),-1!==t.inArray(e,r)&&(i.prevented=n.isDefaultPrevented())}}))},e})),e.define("select2/translation",["jquery","require"],(function(t,e){function i(t){this.dict=t||{}}return i.prototype.all=function(){return this.dict},i.prototype.get=function(t){return this.dict[t]},i.prototype.extend=function(e){this.dict=t.extend({},e.all(),this.dict)},i._cache={},i.loadPath=function(t){if(!(t in i._cache)){var n=e(t);i._cache[t]=n}return new i(i._cache[t])},i})),e.define("select2/diacritics",[],(function(){return{"Ⓐ":"A","Ａ":"A","À":"A","Á":"A","Â":"A","Ầ":"A","Ấ":"A","Ẫ":"A","Ẩ":"A","Ã":"A","Ā":"A","Ă":"A","Ằ":"A","Ắ":"A","Ẵ":"A","Ẳ":"A","Ȧ":"A","Ǡ":"A","Ä":"A","Ǟ":"A","Ả":"A","Å":"A","Ǻ":"A","Ǎ":"A","Ȁ":"A","Ȃ":"A","Ạ":"A","Ậ":"A","Ặ":"A","Ḁ":"A","Ą":"A","Ⱥ":"A","Ɐ":"A","Ꜳ":"AA","Æ":"AE","Ǽ":"AE","Ǣ":"AE","Ꜵ":"AO","Ꜷ":"AU","Ꜹ":"AV","Ꜻ":"AV","Ꜽ":"AY","Ⓑ":"B","Ｂ":"B","Ḃ":"B","Ḅ":"B","Ḇ":"B","Ƀ":"B","Ƃ":"B","Ɓ":"B","Ⓒ":"C","Ｃ":"C","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","Ç":"C","Ḉ":"C","Ƈ":"C","Ȼ":"C","Ꜿ":"C","Ⓓ":"D","Ｄ":"D","Ḋ":"D","Ď":"D","Ḍ":"D","Ḑ":"D","Ḓ":"D","Ḏ":"D","Đ":"D","Ƌ":"D","Ɗ":"D","Ɖ":"D","Ꝺ":"D","Ǳ":"DZ","Ǆ":"DZ","ǲ":"Dz","ǅ":"Dz","Ⓔ":"E","Ｅ":"E","È":"E","É":"E","Ê":"E","Ề":"E","Ế":"E","Ễ":"E","Ể":"E","Ẽ":"E","Ē":"E","Ḕ":"E","Ḗ":"E","Ĕ":"E","Ė":"E","Ë":"E","Ẻ":"E","Ě":"E","Ȅ":"E","Ȇ":"E","Ẹ":"E","Ệ":"E","Ȩ":"E","Ḝ":"E","Ę":"E","Ḙ":"E","Ḛ":"E","Ɛ":"E","Ǝ":"E","Ⓕ":"F","Ｆ":"F","Ḟ":"F","Ƒ":"F","Ꝼ":"F","Ⓖ":"G","Ｇ":"G","Ǵ":"G","Ĝ":"G","Ḡ":"G","Ğ":"G","Ġ":"G","Ǧ":"G","Ģ":"G","Ǥ":"G","Ɠ":"G","Ꞡ":"G","Ᵹ":"G","Ꝿ":"G","Ⓗ":"H","Ｈ":"H","Ĥ":"H","Ḣ":"H","Ḧ":"H","Ȟ":"H","Ḥ":"H","Ḩ":"H","Ḫ":"H","Ħ":"H","Ⱨ":"H","Ⱶ":"H","Ɥ":"H","Ⓘ":"I","Ｉ":"I","Ì":"I","Í":"I","Î":"I","Ĩ":"I","Ī":"I","Ĭ":"I","İ":"I","Ï":"I","Ḯ":"I","Ỉ":"I","Ǐ":"I","Ȉ":"I","Ȋ":"I","Ị":"I","Į":"I","Ḭ":"I","Ɨ":"I","Ⓙ":"J","Ｊ":"J","Ĵ":"J","Ɉ":"J","Ⓚ":"K","Ｋ":"K","Ḱ":"K","Ǩ":"K","Ḳ":"K","Ķ":"K","Ḵ":"K","Ƙ":"K","Ⱪ":"K","Ꝁ":"K","Ꝃ":"K","Ꝅ":"K","Ꞣ":"K","Ⓛ":"L","Ｌ":"L","Ŀ":"L","Ĺ":"L","Ľ":"L","Ḷ":"L","Ḹ":"L","Ļ":"L","Ḽ":"L","Ḻ":"L","Ł":"L","Ƚ":"L","Ɫ":"L","Ⱡ":"L","Ꝉ":"L","Ꝇ":"L","Ꞁ":"L","Ǉ":"LJ","ǈ":"Lj","Ⓜ":"M","Ｍ":"M","Ḿ":"M","Ṁ":"M","Ṃ":"M","Ɱ":"M","Ɯ":"M","Ⓝ":"N","Ｎ":"N","Ǹ":"N","Ń":"N","Ñ":"N","Ṅ":"N","Ň":"N","Ṇ":"N","Ņ":"N","Ṋ":"N","Ṉ":"N","Ƞ":"N","Ɲ":"N","Ꞑ":"N","Ꞥ":"N","Ǌ":"NJ","ǋ":"Nj","Ⓞ":"O","Ｏ":"O","Ò":"O","Ó":"O","Ô":"O","Ồ":"O","Ố":"O","Ỗ":"O","Ổ":"O","Õ":"O","Ṍ":"O","Ȭ":"O","Ṏ":"O","Ō":"O","Ṑ":"O","Ṓ":"O","Ŏ":"O","Ȯ":"O","Ȱ":"O","Ö":"O","Ȫ":"O","Ỏ":"O","Ő":"O","Ǒ":"O","Ȍ":"O","Ȏ":"O","Ơ":"O","Ờ":"O","Ớ":"O","Ỡ":"O","Ở":"O","Ợ":"O","Ọ":"O","Ộ":"O","Ǫ":"O","Ǭ":"O","Ø":"O","Ǿ":"O","Ɔ":"O","Ɵ":"O","Ꝋ":"O","Ꝍ":"O","Ƣ":"OI","Ꝏ":"OO","Ȣ":"OU","Ⓟ":"P","Ｐ":"P","Ṕ":"P","Ṗ":"P","Ƥ":"P","Ᵽ":"P","Ꝑ":"P","Ꝓ":"P","Ꝕ":"P","Ⓠ":"Q","Ｑ":"Q","Ꝗ":"Q","Ꝙ":"Q","Ɋ":"Q","Ⓡ":"R","Ｒ":"R","Ŕ":"R","Ṙ":"R","Ř":"R","Ȑ":"R","Ȓ":"R","Ṛ":"R","Ṝ":"R","Ŗ":"R","Ṟ":"R","Ɍ":"R","Ɽ":"R","Ꝛ":"R","Ꞧ":"R","Ꞃ":"R","Ⓢ":"S","Ｓ":"S","ẞ":"S","Ś":"S","Ṥ":"S","Ŝ":"S","Ṡ":"S","Š":"S","Ṧ":"S","Ṣ":"S","Ṩ":"S","Ș":"S","Ş":"S","Ȿ":"S","Ꞩ":"S","Ꞅ":"S","Ⓣ":"T","Ｔ":"T","Ṫ":"T","Ť":"T","Ṭ":"T","Ț":"T","Ţ":"T","Ṱ":"T","Ṯ":"T","Ŧ":"T","Ƭ":"T","Ʈ":"T","Ⱦ":"T","Ꞇ":"T","Ꜩ":"TZ","Ⓤ":"U","Ｕ":"U","Ù":"U","Ú":"U","Û":"U","Ũ":"U","Ṹ":"U","Ū":"U","Ṻ":"U","Ŭ":"U","Ü":"U","Ǜ":"U","Ǘ":"U","Ǖ":"U","Ǚ":"U","Ủ":"U","Ů":"U","Ű":"U","Ǔ":"U","Ȕ":"U","Ȗ":"U","Ư":"U","Ừ":"U","Ứ":"U","Ữ":"U","Ử":"U","Ự":"U","Ụ":"U","Ṳ":"U","Ų":"U","Ṷ":"U","Ṵ":"U","Ʉ":"U","Ⓥ":"V","Ｖ":"V","Ṽ":"V","Ṿ":"V","Ʋ":"V","Ꝟ":"V","Ʌ":"V","Ꝡ":"VY","Ⓦ":"W","Ｗ":"W","Ẁ":"W","Ẃ":"W","Ŵ":"W","Ẇ":"W","Ẅ":"W","Ẉ":"W","Ⱳ":"W","Ⓧ":"X","Ｘ":"X","Ẋ":"X","Ẍ":"X","Ⓨ":"Y","Ｙ":"Y","Ỳ":"Y","Ý":"Y","Ŷ":"Y","Ỹ":"Y","Ȳ":"Y","Ẏ":"Y","Ÿ":"Y","Ỷ":"Y","Ỵ":"Y","Ƴ":"Y","Ɏ":"Y","Ỿ":"Y","Ⓩ":"Z","Ｚ":"Z","Ź":"Z","Ẑ":"Z","Ż":"Z","Ž":"Z","Ẓ":"Z","Ẕ":"Z","Ƶ":"Z","Ȥ":"Z","Ɀ":"Z","Ⱬ":"Z","Ꝣ":"Z","ⓐ":"a","ａ":"a","ẚ":"a","à":"a","á":"a","â":"a","ầ":"a","ấ":"a","ẫ":"a","ẩ":"a","ã":"a","ā":"a","ă":"a","ằ":"a","ắ":"a","ẵ":"a","ẳ":"a","ȧ":"a","ǡ":"a","ä":"a","ǟ":"a","ả":"a","å":"a","ǻ":"a","ǎ":"a","ȁ":"a","ȃ":"a","ạ":"a","ậ":"a","ặ":"a","ḁ":"a","ą":"a","ⱥ":"a","ɐ":"a","ꜳ":"aa","æ":"ae","ǽ":"ae","ǣ":"ae","ꜵ":"ao","ꜷ":"au","ꜹ":"av","ꜻ":"av","ꜽ":"ay","ⓑ":"b","ｂ":"b","ḃ":"b","ḅ":"b","ḇ":"b","ƀ":"b","ƃ":"b","ɓ":"b","ⓒ":"c","ｃ":"c","ć":"c","ĉ":"c","ċ":"c","č":"c","ç":"c","ḉ":"c","ƈ":"c","ȼ":"c","ꜿ":"c","ↄ":"c","ⓓ":"d","ｄ":"d","ḋ":"d","ď":"d","ḍ":"d","ḑ":"d","ḓ":"d","ḏ":"d","đ":"d","ƌ":"d","ɖ":"d","ɗ":"d","ꝺ":"d","ǳ":"dz","ǆ":"dz","ⓔ":"e","ｅ":"e","è":"e","é":"e","ê":"e","ề":"e","ế":"e","ễ":"e","ể":"e","ẽ":"e","ē":"e","ḕ":"e","ḗ":"e","ĕ":"e","ė":"e","ë":"e","ẻ":"e","ě":"e","ȅ":"e","ȇ":"e","ẹ":"e","ệ":"e","ȩ":"e","ḝ":"e","ę":"e","ḙ":"e","ḛ":"e","ɇ":"e","ɛ":"e","ǝ":"e","ⓕ":"f","ｆ":"f","ḟ":"f","ƒ":"f","ꝼ":"f","ⓖ":"g","ｇ":"g","ǵ":"g","ĝ":"g","ḡ":"g","ğ":"g","ġ":"g","ǧ":"g","ģ":"g","ǥ":"g","ɠ":"g","ꞡ":"g","ᵹ":"g","ꝿ":"g","ⓗ":"h","ｈ":"h","ĥ":"h","ḣ":"h","ḧ":"h","ȟ":"h","ḥ":"h","ḩ":"h","ḫ":"h","ẖ":"h","ħ":"h","ⱨ":"h","ⱶ":"h","ɥ":"h","ƕ":"hv","ⓘ":"i","ｉ":"i","ì":"i","í":"i","î":"i","ĩ":"i","ī":"i","ĭ":"i","ï":"i","ḯ":"i","ỉ":"i","ǐ":"i","ȉ":"i","ȋ":"i","ị":"i","į":"i","ḭ":"i","ɨ":"i","ı":"i","ⓙ":"j","ｊ":"j","ĵ":"j","ǰ":"j","ɉ":"j","ⓚ":"k","ｋ":"k","ḱ":"k","ǩ":"k","ḳ":"k","ķ":"k","ḵ":"k","ƙ":"k","ⱪ":"k","ꝁ":"k","ꝃ":"k","ꝅ":"k","ꞣ":"k","ⓛ":"l","ｌ":"l","ŀ":"l","ĺ":"l","ľ":"l","ḷ":"l","ḹ":"l","ļ":"l","ḽ":"l","ḻ":"l","ſ":"l","ł":"l","ƚ":"l","ɫ":"l","ⱡ":"l","ꝉ":"l","ꞁ":"l","ꝇ":"l","ǉ":"lj","ⓜ":"m","ｍ":"m","ḿ":"m","ṁ":"m","ṃ":"m","ɱ":"m","ɯ":"m","ⓝ":"n","ｎ":"n","ǹ":"n","ń":"n","ñ":"n","ṅ":"n","ň":"n","ṇ":"n","ņ":"n","ṋ":"n","ṉ":"n","ƞ":"n","ɲ":"n","ŉ":"n","ꞑ":"n","ꞥ":"n","ǌ":"nj","ⓞ":"o","ｏ":"o","ò":"o","ó":"o","ô":"o","ồ":"o","ố":"o","ỗ":"o","ổ":"o","õ":"o","ṍ":"o","ȭ":"o","ṏ":"o","ō":"o","ṑ":"o","ṓ":"o","ŏ":"o","ȯ":"o","ȱ":"o","ö":"o","ȫ":"o","ỏ":"o","ő":"o","ǒ":"o","ȍ":"o","ȏ":"o","ơ":"o","ờ":"o","ớ":"o","ỡ":"o","ở":"o","ợ":"o","ọ":"o","ộ":"o","ǫ":"o","ǭ":"o","ø":"o","ǿ":"o","ɔ":"o","ꝋ":"o","ꝍ":"o","ɵ":"o","ƣ":"oi","ȣ":"ou","ꝏ":"oo","ⓟ":"p","ｐ":"p","ṕ":"p","ṗ":"p","ƥ":"p","ᵽ":"p","ꝑ":"p","ꝓ":"p","ꝕ":"p","ⓠ":"q","ｑ":"q","ɋ":"q","ꝗ":"q","ꝙ":"q","ⓡ":"r","ｒ":"r","ŕ":"r","ṙ":"r","ř":"r","ȑ":"r","ȓ":"r","ṛ":"r","ṝ":"r","ŗ":"r","ṟ":"r","ɍ":"r","ɽ":"r","ꝛ":"r","ꞧ":"r","ꞃ":"r","ⓢ":"s","ｓ":"s","ß":"s","ś":"s","ṥ":"s","ŝ":"s","ṡ":"s","š":"s","ṧ":"s","ṣ":"s","ṩ":"s","ș":"s","ş":"s","ȿ":"s","ꞩ":"s","ꞅ":"s","ẛ":"s","ⓣ":"t","ｔ":"t","ṫ":"t","ẗ":"t","ť":"t","ṭ":"t","ț":"t","ţ":"t","ṱ":"t","ṯ":"t","ŧ":"t","ƭ":"t","ʈ":"t","ⱦ":"t","ꞇ":"t","ꜩ":"tz","ⓤ":"u","ｕ":"u","ù":"u","ú":"u","û":"u","ũ":"u","ṹ":"u","ū":"u","ṻ":"u","ŭ":"u","ü":"u","ǜ":"u","ǘ":"u","ǖ":"u","ǚ":"u","ủ":"u","ů":"u","ű":"u","ǔ":"u","ȕ":"u","ȗ":"u","ư":"u","ừ":"u","ứ":"u","ữ":"u","ử":"u","ự":"u","ụ":"u","ṳ":"u","ų":"u","ṷ":"u","ṵ":"u","ʉ":"u","ⓥ":"v","ｖ":"v","ṽ":"v","ṿ":"v","ʋ":"v","ꝟ":"v","ʌ":"v","ꝡ":"vy","ⓦ":"w","ｗ":"w","ẁ":"w","ẃ":"w","ŵ":"w","ẇ":"w","ẅ":"w","ẘ":"w","ẉ":"w","ⱳ":"w","ⓧ":"x","ｘ":"x","ẋ":"x","ẍ":"x","ⓨ":"y","ｙ":"y","ỳ":"y","ý":"y","ŷ":"y","ỹ":"y","ȳ":"y","ẏ":"y","ÿ":"y","ỷ":"y","ẙ":"y","ỵ":"y","ƴ":"y","ɏ":"y","ỿ":"y","ⓩ":"z","ｚ":"z","ź":"z","ẑ":"z","ż":"z","ž":"z","ẓ":"z","ẕ":"z","ƶ":"z","ȥ":"z","ɀ":"z","ⱬ":"z","ꝣ":"z","Ά":"Α","Έ":"Ε","Ή":"Η","Ί":"Ι","Ϊ":"Ι","Ό":"Ο","Ύ":"Υ","Ϋ":"Υ","Ώ":"Ω","ά":"α","έ":"ε","ή":"η","ί":"ι","ϊ":"ι","ΐ":"ι","ό":"ο","ύ":"υ","ϋ":"υ","ΰ":"υ","ω":"ω","ς":"σ"}})),e.define("select2/data/base",["../utils"],(function(t){function e(t,i){e.__super__.constructor.call(this)}return t.Extend(e,t.Observable),e.prototype.current=function(t){throw new Error("The `current` method must be defined in child classes.")},e.prototype.query=function(t,e){throw new Error("The `query` method must be defined in child classes.")},e.prototype.bind=function(t,e){},e.prototype.destroy=function(){},e.prototype.generateResultId=function(e,i){var n=e.id+"-result-";return n+=t.generateChars(4),null!=i.id?n+="-"+i.id.toString():n+="-"+t.generateChars(4),n},e})),e.define("select2/data/select",["./base","../utils","jquery"],(function(t,e,i){function n(t,e){this.$element=t,this.options=e,n.__super__.constructor.call(this)}return e.Extend(n,t),n.prototype.current=function(t){var e=[],n=this;this.$element.find(":selected").each((function(){var t=i(this),s=n.item(t);e.push(s)})),t(e)},n.prototype.select=function(t){var e=this;if(t.selected=!0,i(t.element).is("option"))return t.element.selected=!0,void this.$element.trigger("change");if(this.$element.prop("multiple"))this.current((function(n){var s=[];(t=[t]).push.apply(t,n);for(var o=0;o<t.length;o++){var r=t[o].id;-1===i.inArray(r,s)&&s.push(r)}e.$element.val(s),e.$element.trigger("change")}));else{var n=t.id;this.$element.val(n),this.$element.trigger("change")}},n.prototype.unselect=function(t){var e=this;if(this.$element.prop("multiple")){if(t.selected=!1,i(t.element).is("option"))return t.element.selected=!1,void this.$element.trigger("change");this.current((function(n){for(var s=[],o=0;o<n.length;o++){var r=n[o].id;r!==t.id&&-1===i.inArray(r,s)&&s.push(r)}e.$element.val(s),e.$element.trigger("change")}))}},n.prototype.bind=function(t,e){var i=this;this.container=t,t.on("select",(function(t){i.select(t.data)})),t.on("unselect",(function(t){i.unselect(t.data)}))},n.prototype.destroy=function(){this.$element.find("*").each((function(){e.RemoveData(this)}))},n.prototype.query=function(t,e){var n=[],s=this;this.$element.children().each((function(){var e=i(this);if(e.is("option")||e.is("optgroup")){var o=s.item(e),r=s.matches(t,o);null!==r&&n.push(r)}})),e({results:n})},n.prototype.addOptions=function(t){e.appendMany(this.$element,t)},n.prototype.option=function(t){var n;t.children?(n=document.createElement("optgroup")).label=t.text:void 0!==(n=document.createElement("option")).textContent?n.textContent=t.text:n.innerText=t.text,void 0!==t.id&&(n.value=t.id),t.disabled&&(n.disabled=!0),t.selected&&(n.selected=!0),t.title&&(n.title=t.title);var s=i(n),o=this._normalizeItem(t);return o.element=n,e.StoreData(n,"data",o),s},n.prototype.item=function(t){var n={};if(null!=(n=e.GetData(t[0],"data")))return n;if(t.is("option"))n={id:t.val(),text:t.text(),disabled:t.prop("disabled"),selected:t.prop("selected"),title:t.prop("title")};else if(t.is("optgroup")){n={text:t.prop("label"),children:[],title:t.prop("title")};for(var s=t.children("option"),o=[],r=0;r<s.length;r++){var a=i(s[r]),l=this.item(a);o.push(l)}n.children=o}return(n=this._normalizeItem(n)).element=t[0],e.StoreData(t[0],"data",n),n},n.prototype._normalizeItem=function(t){t!==Object(t)&&(t={id:t,text:t});return null!=(t=i.extend({},{text:""},t)).id&&(t.id=t.id.toString()),null!=t.text&&(t.text=t.text.toString()),null==t._resultId&&t.id&&null!=this.container&&(t._resultId=this.generateResultId(this.container,t)),i.extend({},{selected:!1,disabled:!1},t)},n.prototype.matches=function(t,e){return this.options.get("matcher")(t,e)},n})),e.define("select2/data/array",["./select","../utils","jquery"],(function(t,e,i){function n(t,e){var i=e.get("data")||[];n.__super__.constructor.call(this,t,e),this.addOptions(this.convertToOptions(i))}return e.Extend(n,t),n.prototype.select=function(t){var e=this.$element.find("option").filter((function(e,i){return i.value==t.id.toString()}));0===e.length&&(e=this.option(t),this.addOptions(e)),n.__super__.select.call(this,t)},n.prototype.convertToOptions=function(t){function n(t){return function(){return i(this).val()==t.id}}for(var s=this,o=this.$element.find("option"),r=o.map((function(){return s.item(i(this)).id})).get(),a=[],l=0;l<t.length;l++){var c=this._normalizeItem(t[l]);if(i.inArray(c.id,r)>=0){var h=o.filter(n(c)),u=this.item(h),d=i.extend(!0,{},c,u),p=this.option(d);h.replaceWith(p)}else{var f=this.option(c);if(c.children){var g=this.convertToOptions(c.children);e.appendMany(f,g)}a.push(f)}}return a},n})),e.define("select2/data/ajax",["./array","../utils","jquery"],(function(t,e,i){function n(t,e){this.ajaxOptions=this._applyDefaults(e.get("ajax")),null!=this.ajaxOptions.processResults&&(this.processResults=this.ajaxOptions.processResults),n.__super__.constructor.call(this,t,e)}return e.Extend(n,t),n.prototype._applyDefaults=function(t){var e={data:function(t){return i.extend({},t,{q:t.term})},transport:function(t,e,n){var s=i.ajax(t);return s.then(e),s.fail(n),s}};return i.extend({},e,t,!0)},n.prototype.processResults=function(t){return t},n.prototype.query=function(t,e){function n(){var n=o.transport(o,(function(n){var o=s.processResults(n,t);s.options.get("debug")&&window.console&&console.error&&(o&&o.results&&i.isArray(o.results)||console.error("Select2: The AJAX results did not return an array in the `results` key of the response.")),e(o)}),(function(){"status"in n&&(0===n.status||"0"===n.status)||s.trigger("results:message",{message:"errorLoading"})}));s._request=n}var s=this;null!=this._request&&(i.isFunction(this._request.abort)&&this._request.abort(),this._request=null);var o=i.extend({type:"GET"},this.ajaxOptions);"function"==typeof o.url&&(o.url=o.url.call(this.$element,t)),"function"==typeof o.data&&(o.data=o.data.call(this.$element,t)),this.ajaxOptions.delay&&null!=t.term?(this._queryTimeout&&window.clearTimeout(this._queryTimeout),this._queryTimeout=window.setTimeout(n,this.ajaxOptions.delay)):n()},n})),e.define("select2/data/tags",["jquery"],(function(t){function e(e,i,n){var s=n.get("tags"),o=n.get("createTag");void 0!==o&&(this.createTag=o);var r=n.get("insertTag");if(void 0!==r&&(this.insertTag=r),e.call(this,i,n),t.isArray(s))for(var a=0;a<s.length;a++){var l=s[a],c=this._normalizeItem(l),h=this.option(c);this.$element.append(h)}}return e.prototype.query=function(t,e,i){var n=this;this._removeOldTags(),null!=e.term&&null==e.page?t.call(this,e,(function t(s,o){for(var r=s.results,a=0;a<r.length;a++){var l=r[a],c=null!=l.children&&!t({results:l.children},!0);if((l.text||"").toUpperCase()===(e.term||"").toUpperCase()||c)return!o&&(s.data=r,void i(s))}if(o)return!0;var h=n.createTag(e);if(null!=h){var u=n.option(h);u.attr("data-select2-tag",!0),n.addOptions([u]),n.insertTag(r,h)}s.results=r,i(s)})):t.call(this,e,i)},e.prototype.createTag=function(e,i){var n=t.trim(i.term);return""===n?null:{id:n,text:n}},e.prototype.insertTag=function(t,e,i){e.unshift(i)},e.prototype._removeOldTags=function(e){this._lastTag,this.$element.find("option[data-select2-tag]").each((function(){this.selected||t(this).remove()}))},e})),e.define("select2/data/tokenizer",["jquery"],(function(t){function e(t,e,i){var n=i.get("tokenizer");void 0!==n&&(this.tokenizer=n),t.call(this,e,i)}return e.prototype.bind=function(t,e,i){t.call(this,e,i),this.$search=e.dropdown.$search||e.selection.$search||i.find(".select2-search__field")},e.prototype.query=function(e,i,n){var s=this;i.term=i.term||"";var o=this.tokenizer(i,this.options,(function(e){var i=s._normalizeItem(e);if(!s.$element.find("option").filter((function(){return t(this).val()===i.id})).length){var n=s.option(i);n.attr("data-select2-tag",!0),s._removeOldTags(),s.addOptions([n])}!function(t){s.trigger("select",{data:t})}(i)}));o.term!==i.term&&(this.$search.length&&(this.$search.val(o.term),this.$search.focus()),i.term=o.term),e.call(this,i,n)},e.prototype.tokenizer=function(e,i,n,s){for(var o=n.get("tokenSeparators")||[],r=i.term,a=0,l=this.createTag||function(t){return{id:t.term,text:t.term}};a<r.length;){var c=r[a];if(-1!==t.inArray(c,o)){var h=r.substr(0,a),u=l(t.extend({},i,{term:h}));null!=u?(s(u),r=r.substr(a+1)||"",a=0):a++}else a++}return{term:r}},e})),e.define("select2/data/minimumInputLength",[],(function(){function t(t,e,i){this.minimumInputLength=i.get("minimumInputLength"),t.call(this,e,i)}return t.prototype.query=function(t,e,i){e.term=e.term||"",e.term.length<this.minimumInputLength?this.trigger("results:message",{message:"inputTooShort",args:{minimum:this.minimumInputLength,input:e.term,params:e}}):t.call(this,e,i)},t})),e.define("select2/data/maximumInputLength",[],(function(){function t(t,e,i){this.maximumInputLength=i.get("maximumInputLength"),t.call(this,e,i)}return t.prototype.query=function(t,e,i){e.term=e.term||"",this.maximumInputLength>0&&e.term.length>this.maximumInputLength?this.trigger("results:message",{message:"inputTooLong",args:{maximum:this.maximumInputLength,input:e.term,params:e}}):t.call(this,e,i)},t})),e.define("select2/data/maximumSelectionLength",[],(function(){function t(t,e,i){this.maximumSelectionLength=i.get("maximumSelectionLength"),t.call(this,e,i)}return t.prototype.query=function(t,e,i){var n=this;this.current((function(s){var o=null!=s?s.length:0;n.maximumSelectionLength>0&&o>=n.maximumSelectionLength?n.trigger("results:message",{message:"maximumSelected",args:{maximum:n.maximumSelectionLength}}):t.call(n,e,i)}))},t})),e.define("select2/dropdown",["jquery","./utils"],(function(t,e){function i(t,e){this.$element=t,this.options=e,i.__super__.constructor.call(this)}return e.Extend(i,e.Observable),i.prototype.render=function(){var e=t('<span class="select2-dropdown"><span class="select2-results"></span></span>');return e.attr("dir",this.options.get("dir")),this.$dropdown=e,e},i.prototype.bind=function(){},i.prototype.position=function(t,e){},i.prototype.destroy=function(){this.$dropdown.remove()},i})),e.define("select2/dropdown/search",["jquery","../utils"],(function(t,e){function i(){}return i.prototype.render=function(e){var i=e.call(this),n=t('<span class="select2-search select2-search--dropdown"><input class="select2-search__field" type="search" tabindex="-1" autocomplete="off" autocorrect="off" autocapitalize="none" spellcheck="false" role="textbox" /></span>');return this.$searchContainer=n,this.$search=n.find("input"),i.prepend(n),i},i.prototype.bind=function(e,i,n){var s=this;e.call(this,i,n),this.$search.on("keydown",(function(t){s.trigger("keypress",t),s._keyUpPrevented=t.isDefaultPrevented()})),this.$search.on("input",(function(e){t(this).off("keyup")})),this.$search.on("keyup input",(function(t){s.handleSearch(t)})),i.on("open",(function(){s.$search.attr("tabindex",0),s.$search.focus(),window.setTimeout((function(){s.$search.focus()}),0)})),i.on("close",(function(){s.$search.attr("tabindex",-1),s.$search.val(""),s.$search.blur()})),i.on("focus",(function(){i.isOpen()||s.$search.focus()})),i.on("results:all",(function(t){null!=t.query.term&&""!==t.query.term||(s.showSearch(t)?s.$searchContainer.removeClass("select2-search--hide"):s.$searchContainer.addClass("select2-search--hide"))}))},i.prototype.handleSearch=function(t){if(!this._keyUpPrevented){var e=this.$search.val();this.trigger("query",{term:e})}this._keyUpPrevented=!1},i.prototype.showSearch=function(t,e){return!0},i})),e.define("select2/dropdown/hidePlaceholder",[],(function(){function t(t,e,i,n){this.placeholder=this.normalizePlaceholder(i.get("placeholder")),t.call(this,e,i,n)}return t.prototype.append=function(t,e){e.results=this.removePlaceholder(e.results),t.call(this,e)},t.prototype.normalizePlaceholder=function(t,e){return"string"==typeof e&&(e={id:"",text:e}),e},t.prototype.removePlaceholder=function(t,e){for(var i=e.slice(0),n=e.length-1;n>=0;n--){var s=e[n];this.placeholder.id===s.id&&i.splice(n,1)}return i},t})),e.define("select2/dropdown/infiniteScroll",["jquery"],(function(t){function e(t,e,i,n){this.lastParams={},t.call(this,e,i,n),this.$loadingMore=this.createLoadingMore(),this.loading=!1}return e.prototype.append=function(t,e){this.$loadingMore.remove(),this.loading=!1,t.call(this,e),this.showLoadingMore(e)&&this.$results.append(this.$loadingMore)},e.prototype.bind=function(e,i,n){var s=this;e.call(this,i,n),i.on("query",(function(t){s.lastParams=t,s.loading=!0})),i.on("query:append",(function(t){s.lastParams=t,s.loading=!0})),this.$results.on("scroll",(function(){var e=t.contains(document.documentElement,s.$loadingMore[0]);!s.loading&&e&&s.$results.offset().top+s.$results.outerHeight(!1)+50>=s.$loadingMore.offset().top+s.$loadingMore.outerHeight(!1)&&s.loadMore()}))},e.prototype.loadMore=function(){this.loading=!0;var e=t.extend({},{page:1},this.lastParams);e.page++,this.trigger("query:append",e)},e.prototype.showLoadingMore=function(t,e){return e.pagination&&e.pagination.more},e.prototype.createLoadingMore=function(){var e=t('<li class="select2-results__option select2-results__option--load-more"role="treeitem" aria-disabled="true"></li>'),i=this.options.get("translations").get("loadingMore");return e.html(i(this.lastParams)),e},e})),e.define("select2/dropdown/attachBody",["jquery","../utils"],(function(t,e){function i(e,i,n){this.$dropdownParent=n.get("dropdownParent")||t(document.body),e.call(this,i,n)}return i.prototype.bind=function(t,e,i){var n=this,s=!1;t.call(this,e,i),e.on("open",(function(){n._showDropdown(),n._attachPositioningHandler(e),s||(s=!0,e.on("results:all",(function(){n._positionDropdown(),n._resizeDropdown()})),e.on("results:append",(function(){n._positionDropdown(),n._resizeDropdown()})))})),e.on("close",(function(){n._hideDropdown(),n._detachPositioningHandler(e)})),this.$dropdownContainer.on("mousedown",(function(t){t.stopPropagation()}))},i.prototype.destroy=function(t){t.call(this),this.$dropdownContainer.remove()},i.prototype.position=function(t,e,i){e.attr("class",i.attr("class")),e.removeClass("select2"),e.addClass("select2-container--open"),e.css({position:"absolute",top:-999999}),this.$container=i},i.prototype.render=function(e){var i=t("<span></span>"),n=e.call(this);return i.append(n),this.$dropdownContainer=i,i},i.prototype._hideDropdown=function(t){this.$dropdownContainer.detach()},i.prototype._attachPositioningHandler=function(i,n){var s=this,o="scroll.select2."+n.id,r="resize.select2."+n.id,a="orientationchange.select2."+n.id,l=this.$container.parents().filter(e.hasScroll);l.each((function(){e.StoreData(this,"select2-scroll-position",{x:t(this).scrollLeft(),y:t(this).scrollTop()})})),l.on(o,(function(i){var n=e.GetData(this,"select2-scroll-position");t(this).scrollTop(n.y)})),t(window).on(o+" "+r+" "+a,(function(t){s._positionDropdown(),s._resizeDropdown()}))},i.prototype._detachPositioningHandler=function(i,n){var s="scroll.select2."+n.id,o="resize.select2."+n.id,r="orientationchange.select2."+n.id;this.$container.parents().filter(e.hasScroll).off(s),t(window).off(s+" "+o+" "+r)},i.prototype._positionDropdown=function(){var e=t(window),i=this.$dropdown.hasClass("select2-dropdown--above"),n=this.$dropdown.hasClass("select2-dropdown--below"),s=null,o=this.$container.offset();o.bottom=o.top+this.$container.outerHeight(!1);var r={height:this.$container.outerHeight(!1)};r.top=o.top,r.bottom=o.top+r.height;var a=this.$dropdown.outerHeight(!1),l=e.scrollTop(),c=e.scrollTop()+e.height(),h=l<o.top-a,u=c>o.bottom+a,d={left:o.left,top:r.bottom},p=this.$dropdownParent;"static"===p.css("position")&&(p=p.offsetParent());var f=p.offset();d.top-=f.top,d.left-=f.left,i||n||(s="below"),u||!h||i?!h&&u&&i&&(s="below"):s="above",("above"==s||i&&"below"!==s)&&(d.top=r.top-f.top-a),null!=s&&(this.$dropdown.removeClass("select2-dropdown--below select2-dropdown--above").addClass("select2-dropdown--"+s),this.$container.removeClass("select2-container--below select2-container--above").addClass("select2-container--"+s)),this.$dropdownContainer.css(d)},i.prototype._resizeDropdown=function(){var t={width:this.$container.outerWidth(!1)+"px"};this.options.get("dropdownAutoWidth")&&(t.minWidth=t.width,t.position="relative",t.width="auto"),this.$dropdown.css(t)},i.prototype._showDropdown=function(t){this.$dropdownContainer.appendTo(this.$dropdownParent),this._positionDropdown(),this._resizeDropdown()},i})),e.define("select2/dropdown/minimumResultsForSearch",[],(function(){function t(e){for(var i=0,n=0;n<e.length;n++){var s=e[n];s.children?i+=t(s.children):i++}return i}function e(t,e,i,n){this.minimumResultsForSearch=i.get("minimumResultsForSearch"),this.minimumResultsForSearch<0&&(this.minimumResultsForSearch=1/0),t.call(this,e,i,n)}return e.prototype.showSearch=function(e,i){return!(t(i.data.results)<this.minimumResultsForSearch)&&e.call(this,i)},e})),e.define("select2/dropdown/selectOnClose",["../utils"],(function(t){function e(){}return e.prototype.bind=function(t,e,i){var n=this;t.call(this,e,i),e.on("close",(function(t){n._handleSelectOnClose(t)}))},e.prototype._handleSelectOnClose=function(e,i){if(i&&null!=i.originalSelect2Event){var n=i.originalSelect2Event;if("select"===n._type||"unselect"===n._type)return}var s=this.getHighlightedResults();if(!(s.length<1)){var o=t.GetData(s[0],"data");null!=o.element&&o.element.selected||null==o.element&&o.selected||this.trigger("select",{data:o})}},e})),e.define("select2/dropdown/closeOnSelect",[],(function(){function t(){}return t.prototype.bind=function(t,e,i){var n=this;t.call(this,e,i),e.on("select",(function(t){n._selectTriggered(t)})),e.on("unselect",(function(t){n._selectTriggered(t)}))},t.prototype._selectTriggered=function(t,e){var i=e.originalEvent;i&&i.ctrlKey||this.trigger("close",{originalEvent:i,originalSelect2Event:e})},t})),e.define("select2/i18n/en",[],(function(){return{errorLoading:function(){return"The results could not be loaded."},inputTooLong:function(t){var e=t.input.length-t.maximum,i="Please delete "+e+" character";return 1!=e&&(i+="s"),i},inputTooShort:function(t){return"Please enter "+(t.minimum-t.input.length)+" or more characters"},loadingMore:function(){return"Loading more results…"},maximumSelected:function(t){var e="You can only select "+t.maximum+" item";return 1!=t.maximum&&(e+="s"),e},noResults:function(){return"No results found"},searching:function(){return"Searching…"}}})),e.define("select2/defaults",["jquery","require","./results","./selection/single","./selection/multiple","./selection/placeholder","./selection/allowClear","./selection/search","./selection/eventRelay","./utils","./translation","./diacritics","./data/select","./data/array","./data/ajax","./data/tags","./data/tokenizer","./data/minimumInputLength","./data/maximumInputLength","./data/maximumSelectionLength","./dropdown","./dropdown/search","./dropdown/hidePlaceholder","./dropdown/infiniteScroll","./dropdown/attachBody","./dropdown/minimumResultsForSearch","./dropdown/selectOnClose","./dropdown/closeOnSelect","./i18n/en"],(function(t,e,i,n,s,o,r,a,l,c,h,u,d,p,f,g,m,v,y,b,w,_,x,C,$,k,D,S,A){function T(){this.reset()}return T.prototype.apply=function(u){if(null==(u=t.extend(!0,{},this.defaults,u)).dataAdapter){if(null!=u.ajax?u.dataAdapter=f:null!=u.data?u.dataAdapter=p:u.dataAdapter=d,u.minimumInputLength>0&&(u.dataAdapter=c.Decorate(u.dataAdapter,v)),u.maximumInputLength>0&&(u.dataAdapter=c.Decorate(u.dataAdapter,y)),u.maximumSelectionLength>0&&(u.dataAdapter=c.Decorate(u.dataAdapter,b)),u.tags&&(u.dataAdapter=c.Decorate(u.dataAdapter,g)),null==u.tokenSeparators&&null==u.tokenizer||(u.dataAdapter=c.Decorate(u.dataAdapter,m)),null!=u.query){var A=e(u.amdBase+"compat/query");u.dataAdapter=c.Decorate(u.dataAdapter,A)}if(null!=u.initSelection){var T=e(u.amdBase+"compat/initSelection");u.dataAdapter=c.Decorate(u.dataAdapter,T)}}if(null==u.resultsAdapter&&(u.resultsAdapter=i,null!=u.ajax&&(u.resultsAdapter=c.Decorate(u.resultsAdapter,C)),null!=u.placeholder&&(u.resultsAdapter=c.Decorate(u.resultsAdapter,x)),u.selectOnClose&&(u.resultsAdapter=c.Decorate(u.resultsAdapter,D))),null==u.dropdownAdapter){if(u.multiple)u.dropdownAdapter=w;else{var E=c.Decorate(w,_);u.dropdownAdapter=E}if(0!==u.minimumResultsForSearch&&(u.dropdownAdapter=c.Decorate(u.dropdownAdapter,k)),u.closeOnSelect&&(u.dropdownAdapter=c.Decorate(u.dropdownAdapter,S)),null!=u.dropdownCssClass||null!=u.dropdownCss||null!=u.adaptDropdownCssClass){var j=e(u.amdBase+"compat/dropdownCss");u.dropdownAdapter=c.Decorate(u.dropdownAdapter,j)}u.dropdownAdapter=c.Decorate(u.dropdownAdapter,$)}if(null==u.selectionAdapter){if(u.multiple?u.selectionAdapter=s:u.selectionAdapter=n,null!=u.placeholder&&(u.selectionAdapter=c.Decorate(u.selectionAdapter,o)),u.allowClear&&(u.selectionAdapter=c.Decorate(u.selectionAdapter,r)),u.multiple&&(u.selectionAdapter=c.Decorate(u.selectionAdapter,a)),null!=u.containerCssClass||null!=u.containerCss||null!=u.adaptContainerCssClass){var I=e(u.amdBase+"compat/containerCss");u.selectionAdapter=c.Decorate(u.selectionAdapter,I)}u.selectionAdapter=c.Decorate(u.selectionAdapter,l)}if("string"==typeof u.language)if(u.language.indexOf("-")>0){var O=u.language.split("-")[0];u.language=[u.language,O]}else u.language=[u.language];if(t.isArray(u.language)){var N=new h;u.language.push("en");for(var q=u.language,M=0;M<q.length;M++){var L=q[M],P={};try{P=h.loadPath(L)}catch(t){try{L=this.defaults.amdLanguageBase+L,P=h.loadPath(L)}catch(t){u.debug&&window.console&&console.warn&&console.warn('Select2: The language file for "'+L+'" could not be automatically loaded. A fallback will be used instead.');continue}}N.extend(P)}u.translations=N}else{var z=h.loadPath(this.defaults.amdLanguageBase+"en"),U=new h(u.language);U.extend(z),u.translations=U}return u},T.prototype.reset=function(){function e(t){return t.replace(/[^\u0000-\u007E]/g,(function(t){return u[t]||t}))}this.defaults={amdBase:"./",amdLanguageBase:"./i18n/",closeOnSelect:!0,debug:!1,dropdownAutoWidth:!1,escapeMarkup:c.escapeMarkup,language:A,matcher:function i(n,s){if(""===t.trim(n.term))return s;if(s.children&&s.children.length>0){for(var o=t.extend(!0,{},s),r=s.children.length-1;r>=0;r--)null==i(n,s.children[r])&&o.children.splice(r,1);return o.children.length>0?o:i(n,o)}var a=e(s.text).toUpperCase(),l=e(n.term).toUpperCase();return a.indexOf(l)>-1?s:null},minimumInputLength:0,maximumInputLength:0,maximumSelectionLength:0,minimumResultsForSearch:0,selectOnClose:!1,sorter:function(t){return t},templateResult:function(t){return t.text},templateSelection:function(t){return t.text},theme:"default",width:"resolve"}},T.prototype.set=function(e,i){var n={};n[t.camelCase(e)]=i;var s=c._convertData(n);t.extend(!0,this.defaults,s)},new T})),e.define("select2/options",["require","jquery","./defaults","./utils"],(function(t,e,i,n){function s(e,s){if(this.options=e,null!=s&&this.fromElement(s),this.options=i.apply(this.options),s&&s.is("input")){var o=t(this.get("amdBase")+"compat/inputData");this.options.dataAdapter=n.Decorate(this.options.dataAdapter,o)}}return s.prototype.fromElement=function(t){var i=["select2"];null==this.options.multiple&&(this.options.multiple=t.prop("multiple")),null==this.options.disabled&&(this.options.disabled=t.prop("disabled")),null==this.options.language&&(t.prop("lang")?this.options.language=t.prop("lang").toLowerCase():t.closest("[lang]").prop("lang")&&(this.options.language=t.closest("[lang]").prop("lang"))),null==this.options.dir&&(t.prop("dir")?this.options.dir=t.prop("dir"):t.closest("[dir]").prop("dir")?this.options.dir=t.closest("[dir]").prop("dir"):this.options.dir="ltr"),t.prop("disabled",this.options.disabled),t.prop("multiple",this.options.multiple),n.GetData(t[0],"select2Tags")&&(this.options.debug&&window.console&&console.warn&&console.warn('Select2: The `data-select2-tags` attribute has been changed to use the `data-data` and `data-tags="true"` attributes and will be removed in future versions of Select2.'),n.StoreData(t[0],"data",n.GetData(t[0],"select2Tags")),n.StoreData(t[0],"tags",!0)),n.GetData(t[0],"ajaxUrl")&&(this.options.debug&&window.console&&console.warn&&console.warn("Select2: The `data-ajax-url` attribute has been changed to `data-ajax--url` and support for the old attribute will be removed in future versions of Select2."),t.attr("ajax--url",n.GetData(t[0],"ajaxUrl")),n.StoreData(t[0],"ajax-Url",n.GetData(t[0],"ajaxUrl")));var s;s=e.fn.jquery&&"1."==e.fn.jquery.substr(0,2)&&t[0].dataset?e.extend(!0,{},t[0].dataset,n.GetData(t[0])):n.GetData(t[0]);var o=e.extend(!0,{},s);for(var r in o=n._convertData(o))e.inArray(r,i)>-1||(e.isPlainObject(this.options[r])?e.extend(this.options[r],o[r]):this.options[r]=o[r]);return this},s.prototype.get=function(t){return this.options[t]},s.prototype.set=function(t,e){this.options[t]=e},s})),e.define("select2/core",["jquery","./options","./utils","./keys"],(function(t,e,i,n){var s=function(t,n){null!=i.GetData(t[0],"select2")&&i.GetData(t[0],"select2").destroy(),this.$element=t,this.id=this._generateId(t),n=n||{},this.options=new e(n,t),s.__super__.constructor.call(this);var o=t.attr("tabindex")||0;i.StoreData(t[0],"old-tabindex",o),t.attr("tabindex","-1");var r=this.options.get("dataAdapter");this.dataAdapter=new r(t,this.options);var a=this.render();this._placeContainer(a);var l=this.options.get("selectionAdapter");this.selection=new l(t,this.options),this.$selection=this.selection.render(),this.selection.position(this.$selection,a);var c=this.options.get("dropdownAdapter");this.dropdown=new c(t,this.options),this.$dropdown=this.dropdown.render(),this.dropdown.position(this.$dropdown,a);var h=this.options.get("resultsAdapter");this.results=new h(t,this.options,this.dataAdapter),this.$results=this.results.render(),this.results.position(this.$results,this.$dropdown);var u=this;this._bindAdapters(),this._registerDomEvents(),this._registerDataEvents(),this._registerSelectionEvents(),this._registerDropdownEvents(),this._registerResultsEvents(),this._registerEvents(),this.dataAdapter.current((function(t){u.trigger("selection:update",{data:t})})),t.addClass("select2-hidden-accessible"),t.attr("aria-hidden","true"),this._syncAttributes(),i.StoreData(t[0],"select2",this)};return i.Extend(s,i.Observable),s.prototype._generateId=function(t){return"select2-"+(null!=t.attr("id")?t.attr("id"):null!=t.attr("name")?t.attr("name")+"-"+i.generateChars(2):i.generateChars(4)).replace(/(:|\.|\[|\]|,)/g,"")},s.prototype._placeContainer=function(t){t.insertAfter(this.$element);var e=this._resolveWidth(this.$element,this.options.get("width"));null!=e&&t.css("width",e)},s.prototype._resolveWidth=function(t,e){var i=/^width:(([-+]?([0-9]*\.)?[0-9]+)(px|em|ex|%|in|cm|mm|pt|pc))/i;if("resolve"==e){var n=this._resolveWidth(t,"style");return null!=n?n:this._resolveWidth(t,"element")}if("element"==e){var s=t.outerWidth(!1);return s<=0?"auto":s+"px"}if("style"==e){var o=t.attr("style");if("string"!=typeof o)return null;for(var r=o.split(";"),a=0,l=r.length;a<l;a+=1){var c=r[a].replace(/\s/g,"").match(i);if(null!==c&&c.length>=1)return c[1]}return null}return e},s.prototype._bindAdapters=function(){this.dataAdapter.bind(this,this.$container),this.selection.bind(this,this.$container),this.dropdown.bind(this,this.$container),this.results.bind(this,this.$container)},s.prototype._registerDomEvents=function(){var e=this;this.$element.on("change.select2",(function(){e.dataAdapter.current((function(t){e.trigger("selection:update",{data:t})}))})),this.$element.on("focus.select2",(function(t){e.trigger("focus",t)})),this._syncA=i.bind(this._syncAttributes,this),this._syncS=i.bind(this._syncSubtree,this),this.$element[0].attachEvent&&this.$element[0].attachEvent("onpropertychange",this._syncA);var n=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver;null!=n?(this._observer=new n((function(i){t.each(i,e._syncA),t.each(i,e._syncS)})),this._observer.observe(this.$element[0],{attributes:!0,childList:!0,subtree:!1})):this.$element[0].addEventListener&&(this.$element[0].addEventListener("DOMAttrModified",e._syncA,!1),this.$element[0].addEventListener("DOMNodeInserted",e._syncS,!1),this.$element[0].addEventListener("DOMNodeRemoved",e._syncS,!1))},s.prototype._registerDataEvents=function(){var t=this;this.dataAdapter.on("*",(function(e,i){t.trigger(e,i)}))},s.prototype._registerSelectionEvents=function(){var e=this,i=["toggle","focus"];this.selection.on("toggle",(function(){e.toggleDropdown()})),this.selection.on("focus",(function(t){e.focus(t)})),this.selection.on("*",(function(n,s){-1===t.inArray(n,i)&&e.trigger(n,s)}))},s.prototype._registerDropdownEvents=function(){var t=this;this.dropdown.on("*",(function(e,i){t.trigger(e,i)}))},s.prototype._registerResultsEvents=function(){var t=this;this.results.on("*",(function(e,i){t.trigger(e,i)}))},s.prototype._registerEvents=function(){var t=this;this.on("open",(function(){t.$container.addClass("select2-container--open")})),this.on("close",(function(){t.$container.removeClass("select2-container--open")})),this.on("enable",(function(){t.$container.removeClass("select2-container--disabled")})),this.on("disable",(function(){t.$container.addClass("select2-container--disabled")})),this.on("blur",(function(){t.$container.removeClass("select2-container--focus")})),this.on("query",(function(e){t.isOpen()||t.trigger("open",{}),this.dataAdapter.query(e,(function(i){t.trigger("results:all",{data:i,query:e})}))})),this.on("query:append",(function(e){this.dataAdapter.query(e,(function(i){t.trigger("results:append",{data:i,query:e})}))})),this.on("keypress",(function(e){var i=e.which;t.isOpen()?i===n.ESC||i===n.TAB||i===n.UP&&e.altKey?(t.close(),e.preventDefault()):i===n.ENTER?(t.trigger("results:select",{}),e.preventDefault()):i===n.SPACE&&e.ctrlKey?(t.trigger("results:toggle",{}),e.preventDefault()):i===n.UP?(t.trigger("results:previous",{}),e.preventDefault()):i===n.DOWN&&(t.trigger("results:next",{}),e.preventDefault()):(i===n.ENTER||i===n.SPACE||i===n.DOWN&&e.altKey)&&(t.open(),e.preventDefault())}))},s.prototype._syncAttributes=function(){this.options.set("disabled",this.$element.prop("disabled")),this.options.get("disabled")?(this.isOpen()&&this.close(),this.trigger("disable",{})):this.trigger("enable",{})},s.prototype._syncSubtree=function(t,e){var i=!1,n=this;if(!t||!t.target||"OPTION"===t.target.nodeName||"OPTGROUP"===t.target.nodeName){if(e)if(e.addedNodes&&e.addedNodes.length>0)for(var s=0;s<e.addedNodes.length;s++){e.addedNodes[s].selected&&(i=!0)}else e.removedNodes&&e.removedNodes.length>0&&(i=!0);else i=!0;i&&this.dataAdapter.current((function(t){n.trigger("selection:update",{data:t})}))}},s.prototype.trigger=function(t,e){var i=s.__super__.trigger,n={open:"opening",close:"closing",select:"selecting",unselect:"unselecting",clear:"clearing"};if(void 0===e&&(e={}),t in n){var o=n[t],r={prevented:!1,name:t,args:e};if(i.call(this,o,r),r.prevented)return void(e.prevented=!0)}i.call(this,t,e)},s.prototype.toggleDropdown=function(){this.options.get("disabled")||(this.isOpen()?this.close():this.open())},s.prototype.open=function(){this.isOpen()||this.trigger("query",{})},s.prototype.close=function(){this.isOpen()&&this.trigger("close",{})},s.prototype.isOpen=function(){return this.$container.hasClass("select2-container--open")},s.prototype.hasFocus=function(){return this.$container.hasClass("select2-container--focus")},s.prototype.focus=function(t){this.hasFocus()||(this.$container.addClass("select2-container--focus"),this.trigger("focus",{}))},s.prototype.enable=function(t){this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("enable")` method has been deprecated and will be removed in later Select2 versions. Use $element.prop("disabled") instead.'),null!=t&&0!==t.length||(t=[!0]);var e=!t[0];this.$element.prop("disabled",e)},s.prototype.data=function(){this.options.get("debug")&&arguments.length>0&&window.console&&console.warn&&console.warn('Select2: Data can no longer be set using `select2("data")`. You should consider setting the value instead using `$element.val()`.');var t=[];return this.dataAdapter.current((function(e){t=e})),t},s.prototype.val=function(e){if(this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("val")` method has been deprecated and will be removed in later Select2 versions. Use $element.val() instead.'),null==e||0===e.length)return this.$element.val();var i=e[0];t.isArray(i)&&(i=t.map(i,(function(t){return t.toString()}))),this.$element.val(i).trigger("change")},s.prototype.destroy=function(){this.$container.remove(),this.$element[0].detachEvent&&this.$element[0].detachEvent("onpropertychange",this._syncA),null!=this._observer?(this._observer.disconnect(),this._observer=null):this.$element[0].removeEventListener&&(this.$element[0].removeEventListener("DOMAttrModified",this._syncA,!1),this.$element[0].removeEventListener("DOMNodeInserted",this._syncS,!1),this.$element[0].removeEventListener("DOMNodeRemoved",this._syncS,!1)),this._syncA=null,this._syncS=null,this.$element.off(".select2"),this.$element.attr("tabindex",i.GetData(this.$element[0],"old-tabindex")),this.$element.removeClass("select2-hidden-accessible"),this.$element.attr("aria-hidden","false"),i.RemoveData(this.$element[0]),this.dataAdapter.destroy(),this.selection.destroy(),this.dropdown.destroy(),this.results.destroy(),this.dataAdapter=null,this.selection=null,this.dropdown=null,this.results=null},s.prototype.render=function(){var e=t('<span class="select2 select2-container"><span class="selection"></span><span class="dropdown-wrapper" aria-hidden="true"></span></span>');return e.attr("dir",this.options.get("dir")),this.$container=e,this.$container.addClass("select2-container--"+this.options.get("theme")),i.StoreData(e[0],"element",this.$element),e},s})),e.define("select2/compat/utils",["jquery"],(function(t){return{syncCssClasses:function(e,i,n){var s,o,r=[];(s=t.trim(e.attr("class")))&&t((s=""+s).split(/\s+/)).each((function(){0===this.indexOf("select2-")&&r.push(this)})),(s=t.trim(i.attr("class")))&&t((s=""+s).split(/\s+/)).each((function(){0!==this.indexOf("select2-")&&null!=(o=n(this))&&r.push(o)})),e.attr("class",r.join(" "))}}})),e.define("select2/compat/containerCss",["jquery","./utils"],(function(t,e){function i(t){return null}function n(){}return n.prototype.render=function(n){var s=n.call(this),o=this.options.get("containerCssClass")||"";t.isFunction(o)&&(o=o(this.$element));var r=this.options.get("adaptContainerCssClass");if(r=r||i,-1!==o.indexOf(":all:")){o=o.replace(":all:","");var a=r;r=function(t){var e=a(t);return null!=e?e+" "+t:t}}var l=this.options.get("containerCss")||{};return t.isFunction(l)&&(l=l(this.$element)),e.syncCssClasses(s,this.$element,r),s.css(l),s.addClass(o),s},n})),e.define("select2/compat/dropdownCss",["jquery","./utils"],(function(t,e){function i(t){return null}function n(){}return n.prototype.render=function(n){var s=n.call(this),o=this.options.get("dropdownCssClass")||"";t.isFunction(o)&&(o=o(this.$element));var r=this.options.get("adaptDropdownCssClass");if(r=r||i,-1!==o.indexOf(":all:")){o=o.replace(":all:","");var a=r;r=function(t){var e=a(t);return null!=e?e+" "+t:t}}var l=this.options.get("dropdownCss")||{};return t.isFunction(l)&&(l=l(this.$element)),e.syncCssClasses(s,this.$element,r),s.css(l),s.addClass(o),s},n})),e.define("select2/compat/initSelection",["jquery"],(function(t){function e(t,e,i){i.get("debug")&&window.console&&console.warn&&console.warn("Select2: The `initSelection` option has been deprecated in favor of a custom data adapter that overrides the `current` method. This method is now called multiple times instead of a single time when the instance is initialized. Support will be removed for the `initSelection` option in future versions of Select2"),this.initSelection=i.get("initSelection"),this._isInitialized=!1,t.call(this,e,i)}return e.prototype.current=function(e,i){var n=this;this._isInitialized?e.call(this,i):this.initSelection.call(null,this.$element,(function(e){n._isInitialized=!0,t.isArray(e)||(e=[e]),i(e)}))},e})),e.define("select2/compat/inputData",["jquery","../utils"],(function(t,e){function i(t,e,i){this._currentData=[],this._valueSeparator=i.get("valueSeparator")||",","hidden"===e.prop("type")&&i.get("debug")&&console&&console.warn&&console.warn("Select2: Using a hidden input with Select2 is no longer supported and may stop working in the future. It is recommended to use a `<select>` element instead."),t.call(this,e,i)}return i.prototype.current=function(e,i){function n(e,i){var s=[];return e.selected||-1!==t.inArray(e.id,i)?(e.selected=!0,s.push(e)):e.selected=!1,e.children&&s.push.apply(s,n(e.children,i)),s}for(var s=[],o=0;o<this._currentData.length;o++){var r=this._currentData[o];s.push.apply(s,n(r,this.$element.val().split(this._valueSeparator)))}i(s)},i.prototype.select=function(e,i){if(this.options.get("multiple")){var n=this.$element.val();n+=this._valueSeparator+i.id,this.$element.val(n),this.$element.trigger("change")}else this.current((function(e){t.map(e,(function(t){t.selected=!1}))})),this.$element.val(i.id),this.$element.trigger("change")},i.prototype.unselect=function(t,e){var i=this;e.selected=!1,this.current((function(t){for(var n=[],s=0;s<t.length;s++){var o=t[s];e.id!=o.id&&n.push(o.id)}i.$element.val(n.join(i._valueSeparator)),i.$element.trigger("change")}))},i.prototype.query=function(t,e,i){for(var n=[],s=0;s<this._currentData.length;s++){var o=this._currentData[s],r=this.matches(e,o);null!==r&&n.push(r)}i({results:n})},i.prototype.addOptions=function(i,n){var s=t.map(n,(function(t){return e.GetData(t[0],"data")}));this._currentData.push.apply(this._currentData,s)},i})),e.define("select2/compat/matcher",["jquery"],(function(t){return function(e){return function(i,n){var s=t.extend(!0,{},n);if(null==i.term||""===t.trim(i.term))return s;if(n.children){for(var o=n.children.length-1;o>=0;o--){var r=n.children[o];e(i.term,r.text,r)||s.children.splice(o,1)}if(s.children.length>0)return s}return e(i.term,n.text,n)?s:null}}})),e.define("select2/compat/query",[],(function(){function t(t,e,i){i.get("debug")&&window.console&&console.warn&&console.warn("Select2: The `query` option has been deprecated in favor of a custom data adapter that overrides the `query` method. Support will be removed for the `query` option in future versions of Select2."),t.call(this,e,i)}return t.prototype.query=function(t,e,i){e.callback=i,this.options.get("query").call(null,e)},t})),e.define("select2/dropdown/attachContainer",[],(function(){function t(t,e,i){t.call(this,e,i)}return t.prototype.position=function(t,e,i){i.find(".dropdown-wrapper").append(e),e.addClass("select2-dropdown--below"),i.addClass("select2-container--below")},t})),e.define("select2/dropdown/stopPropagation",[],(function(){function t(){}return t.prototype.bind=function(t,e,i){t.call(this,e,i);this.$dropdown.on(["blur","change","click","dblclick","focus","focusin","focusout","input","keydown","keyup","keypress","mousedown","mouseenter","mouseleave","mousemove","mouseover","mouseup","search","touchend","touchstart"].join(" "),(function(t){t.stopPropagation()}))},t})),e.define("select2/selection/stopPropagation",[],(function(){function t(){}return t.prototype.bind=function(t,e,i){t.call(this,e,i);this.$selection.on(["blur","change","click","dblclick","focus","focusin","focusout","input","keydown","keyup","keypress","mousedown","mouseenter","mouseleave","mousemove","mouseover","mouseup","search","touchend","touchstart"].join(" "),(function(t){t.stopPropagation()}))},t})),function(i){"function"==typeof e.define&&e.define.amd?e.define("jquery-mousewheel",["jquery"],i):"object"==typeof exports?module.exports=i:i(t)}((function(t){function e(e){var r=e||window.event,a=l.call(arguments,1),c=0,u=0,d=0,p=0,f=0,g=0;if((e=t.event.fix(r)).type="mousewheel","detail"in r&&(d=-1*r.detail),"wheelDelta"in r&&(d=r.wheelDelta),"wheelDeltaY"in r&&(d=r.wheelDeltaY),"wheelDeltaX"in r&&(u=-1*r.wheelDeltaX),"axis"in r&&r.axis===r.HORIZONTAL_AXIS&&(u=-1*d,d=0),c=0===d?u:d,"deltaY"in r&&(c=d=-1*r.deltaY),"deltaX"in r&&(u=r.deltaX,0===d&&(c=-1*u)),0!==d||0!==u){if(1===r.deltaMode){var m=t.data(this,"mousewheel-line-height");c*=m,d*=m,u*=m}else if(2===r.deltaMode){var v=t.data(this,"mousewheel-page-height");c*=v,d*=v,u*=v}if(p=Math.max(Math.abs(d),Math.abs(u)),(!o||p<o)&&(o=p,n(r,p)&&(o/=40)),n(r,p)&&(c/=40,u/=40,d/=40),c=Math[c>=1?"floor":"ceil"](c/o),u=Math[u>=1?"floor":"ceil"](u/o),d=Math[d>=1?"floor":"ceil"](d/o),h.settings.normalizeOffset&&this.getBoundingClientRect){var y=this.getBoundingClientRect();f=e.clientX-y.left,g=e.clientY-y.top}return e.deltaX=u,e.deltaY=d,e.deltaFactor=o,e.offsetX=f,e.offsetY=g,e.deltaMode=0,a.unshift(e,c,u,d),s&&clearTimeout(s),s=setTimeout(i,200),(t.event.dispatch||t.event.handle).apply(this,a)}}function i(){o=null}function n(t,e){return h.settings.adjustOldDeltas&&"mousewheel"===t.type&&e%120==0}var s,o,r=["wheel","mousewheel","DOMMouseScroll","MozMousePixelScroll"],a="onwheel"in document||document.documentMode>=9?["wheel"]:["mousewheel","DomMouseScroll","MozMousePixelScroll"],l=Array.prototype.slice;if(t.event.fixHooks)for(var c=r.length;c;)t.event.fixHooks[r[--c]]=t.event.mouseHooks;var h=t.event.special.mousewheel={version:"3.1.12",setup:function(){if(this.addEventListener)for(var i=a.length;i;)this.addEventListener(a[--i],e,!1);else this.onmousewheel=e;t.data(this,"mousewheel-line-height",h.getLineHeight(this)),t.data(this,"mousewheel-page-height",h.getPageHeight(this))},teardown:function(){if(this.removeEventListener)for(var i=a.length;i;)this.removeEventListener(a[--i],e,!1);else this.onmousewheel=null;t.removeData(this,"mousewheel-line-height"),t.removeData(this,"mousewheel-page-height")},getLineHeight:function(e){var i=t(e),n=i["offsetParent"in t.fn?"offsetParent":"parent"]();return n.length||(n=t("body")),parseInt(n.css("fontSize"),10)||parseInt(i.css("fontSize"),10)||16},getPageHeight:function(e){return t(e).height()},settings:{adjustOldDeltas:!0,normalizeOffset:!0}};t.fn.extend({mousewheel:function(t){return t?this.bind("mousewheel",t):this.trigger("mousewheel")},unmousewheel:function(t){return this.unbind("mousewheel",t)}})})),e.define("jquery.select2",["jquery","jquery-mousewheel","./select2/core","./select2/defaults","./select2/utils"],(function(t,e,i,n,s){if(null==t.fn.select2){var o=["open","close","destroy"];t.fn.select2=function(e){if("object"==typeof(e=e||{}))return this.each((function(){var n=t.extend(!0,{},e);new i(t(this),n)})),this;if("string"==typeof e){var n,r=Array.prototype.slice.call(arguments,1);return this.each((function(){var t=s.GetData(this,"select2");null==t&&window.console&&console.error&&console.error("The select2('"+e+"') method was called on an element that is not using Select2."),n=t[e].apply(t,r)})),t.inArray(e,o)>-1?this:n}throw new Error("Invalid arguments for Select2: "+e)}}return null==t.fn.select2.defaults&&(t.fn.select2.defaults=n),i})),{define:e.define,require:e.require}}(),i=e.require("jquery.select2");return t.fn.select2.amd=e,i})),function(t,e){"function"==typeof define&&define.amd?define("bloodhound",["jquery"],(function(i){return t.Bloodhound=e(i)})):"object"==typeof exports?module.exports=e(require("jquery")):t.Bloodhound=e(jQuery)}(this,(function(t){var e=function(){"use strict";return{isMsie:function(){return!!/(msie|trident)/i.test(navigator.userAgent)&&navigator.userAgent.match(/(msie |rv:)(\d+(.\d+)?)/i)[2]},isBlankString:function(t){return!t||/^\s*$/.test(t)},escapeRegExChars:function(t){return t.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")},isString:function(t){return"string"==typeof t},isNumber:function(t){return"number"==typeof t},isArray:t.isArray,isFunction:t.isFunction,isObject:t.isPlainObject,isUndefined:function(t){return void 0===t},isElement:function(t){return!(!t||1!==t.nodeType)},isJQuery:function(e){return e instanceof t},toStr:function(t){return e.isUndefined(t)||null===t?"":t+""},bind:t.proxy,each:function(e,i){t.each(e,(function(t,e){return i(e,t)}))},map:t.map,filter:t.grep,every:function(e,i){var n=!0;return e?(t.each(e,(function(t,s){return!!(n=i.call(null,s,t,e))&&void 0})),!!n):n},some:function(e,i){var n=!1;return e?(t.each(e,(function(t,s){return!(n=i.call(null,s,t,e))&&void 0})),!!n):n},mixin:t.extend,identity:function(t){return t},clone:function(e){return t.extend(!0,{},e)},getIdGenerator:function(){var t=0;return function(){return t++}},templatify:function(e){return t.isFunction(e)?e:function(){return String(e)}},defer:function(t){setTimeout(t,0)},debounce:function(t,e,i){var n,s;return function(){var o,r,a=this,l=arguments;return o=function(){n=null,i||(s=t.apply(a,l))},r=i&&!n,clearTimeout(n),n=setTimeout(o,e),r&&(s=t.apply(a,l)),s}},throttle:function(t,e){var i,n,s,o,r,a;return r=0,a=function(){r=new Date,s=null,o=t.apply(i,n)},function(){var l=new Date,c=e-(l-r);return i=this,n=arguments,0>=c?(clearTimeout(s),s=null,r=l,o=t.apply(i,n)):s||(s=setTimeout(a,c)),o}},stringify:function(t){return e.isString(t)?t:JSON.stringify(t)},noop:function(){}}}(),i=function(){"use strict";function t(t){return(t=e.toStr(t))?t.split(/\s+/):[]}function i(t){return(t=e.toStr(t))?t.split(/\W+/):[]}function n(t){return function(i){return i=e.isArray(i)?i:[].slice.call(arguments,0),function(n){var s=[];return e.each(i,(function(i){s=s.concat(t(e.toStr(n[i])))})),s}}}return{nonword:i,whitespace:t,obj:{nonword:n(i),whitespace:n(t)}}}(),n=function(){"use strict";function i(i){this.maxSize=e.isNumber(i)?i:100,this.reset(),this.maxSize<=0&&(this.set=this.get=t.noop)}function n(){this.head=this.tail=null}function s(t,e){this.key=t,this.val=e,this.prev=this.next=null}return e.mixin(i.prototype,{set:function(t,e){var i,n=this.list.tail;this.size>=this.maxSize&&(this.list.remove(n),delete this.hash[n.key],this.size--),(i=this.hash[t])?(i.val=e,this.list.moveToFront(i)):(i=new s(t,e),this.list.add(i),this.hash[t]=i,this.size++)},get:function(t){var e=this.hash[t];return e?(this.list.moveToFront(e),e.val):void 0},reset:function(){this.size=0,this.hash={},this.list=new n}}),e.mixin(n.prototype,{add:function(t){this.head&&(t.next=this.head,this.head.prev=t),this.head=t,this.tail=this.tail||t},remove:function(t){t.prev?t.prev.next=t.next:this.head=t.next,t.next?t.next.prev=t.prev:this.tail=t.prev},moveToFront:function(t){this.remove(t),this.add(t)}}),i}(),s=function(){"use strict";function i(t,i){this.prefix=["__",t,"__"].join(""),this.ttlKey="__ttl__",this.keyMatcher=new RegExp("^"+e.escapeRegExChars(this.prefix)),this.ls=i||r,!this.ls&&this._noop()}function n(){return(new Date).getTime()}function s(t){return JSON.stringify(e.isUndefined(t)?null:t)}function o(e){return t.parseJSON(e)}var r;try{(r=window.localStorage).setItem("~~~","!"),r.removeItem("~~~")}catch(t){r=null}return e.mixin(i.prototype,{_prefix:function(t){return this.prefix+t},_ttlKey:function(t){return this._prefix(t)+this.ttlKey},_noop:function(){this.get=this.set=this.remove=this.clear=this.isExpired=e.noop},_safeSet:function(t,e){try{this.ls.setItem(t,e)}catch(t){"QuotaExceededError"===t.name&&(this.clear(),this._noop())}},get:function(t){return this.isExpired(t)&&this.remove(t),o(this.ls.getItem(this._prefix(t)))},set:function(t,i,o){return e.isNumber(o)?this._safeSet(this._ttlKey(t),s(n()+o)):this.ls.removeItem(this._ttlKey(t)),this._safeSet(this._prefix(t),s(i))},remove:function(t){return this.ls.removeItem(this._ttlKey(t)),this.ls.removeItem(this._prefix(t)),this},clear:function(){var t,e=function(t){var e,i,n=[],s=r.length;for(e=0;s>e;e++)(i=r.key(e)).match(t)&&n.push(i.replace(t,""));return n}(this.keyMatcher);for(t=e.length;t--;)this.remove(e[t]);return this},isExpired:function(t){var i=o(this.ls.getItem(this._ttlKey(t)));return!!(e.isNumber(i)&&n()>i)}}),i}(),o=function(){"use strict";function i(t){t=t||{},this.cancelled=!1,this.lastReq=null,this._send=t.transport,this._get=t.limiter?t.limiter(this._get):this._get,this._cache=!1===t.cache?new n(0):a}var s=0,o={},r=6,a=new n(10);return i.setMaxPendingRequests=function(t){r=t},i.resetCache=function(){a.reset()},e.mixin(i.prototype,{_fingerprint:function(e){return(e=e||{}).url+e.type+t.param(e.data||{})},_get:function(t,e){function i(t){e(null,t),c._cache.set(a,t)}function n(){e(!0)}var a,l,c=this;a=this._fingerprint(t),this.cancelled||a!==this.lastReq||((l=o[a])?l.done(i).fail(n):r>s?(s++,o[a]=this._send(t).done(i).fail(n).always((function(){s--,delete o[a],c.onDeckRequestArgs&&(c._get.apply(c,c.onDeckRequestArgs),c.onDeckRequestArgs=null)}))):this.onDeckRequestArgs=[].slice.call(arguments,0))},get:function(i,n){var s,o;n=n||t.noop,i=e.isString(i)?{url:i}:i||{},o=this._fingerprint(i),this.cancelled=!1,this.lastReq=o,(s=this._cache.get(o))?n(null,s):this._get(i,n)},cancel:function(){this.cancelled=!0}}),i}(),r=window.SearchIndex=function(){"use strict";function i(i){(i=i||{}).datumTokenizer&&i.queryTokenizer||t.error("datumTokenizer and queryTokenizer are both required"),this.identify=i.identify||e.stringify,this.datumTokenizer=i.datumTokenizer,this.queryTokenizer=i.queryTokenizer,this.reset()}function n(t){return t=e.filter(t,(function(t){return!!t})),e.map(t,(function(t){return t.toLowerCase()}))}function s(){var t={};return t[r]=[],t[o]={},t}var o="c",r="i";return e.mixin(i.prototype,{bootstrap:function(t){this.datums=t.datums,this.trie=t.trie},add:function(t){var i=this;t=e.isArray(t)?t:[t],e.each(t,(function(t){var a,l;i.datums[a=i.identify(t)]=t,l=n(i.datumTokenizer(t)),e.each(l,(function(t){var e,n,l;for(e=i.trie,n=t.split("");l=n.shift();)(e=e[o][l]||(e[o][l]=s()))[r].push(a)}))}))},get:function(t){var i=this;return e.map(t,(function(t){return i.datums[t]}))},search:function(t){var i,s,a=this;return i=n(this.queryTokenizer(t)),e.each(i,(function(t){var e,i,n,l;if(s&&0===s.length)return!1;for(e=a.trie,i=t.split("");e&&(n=i.shift());)e=e[o][n];return e&&0===i.length?(l=e[r].slice(0),void(s=s?function(t,e){var i=0,n=0,s=[];t=t.sort(),e=e.sort();for(var o=t.length,r=e.length;o>i&&r>n;)t[i]<e[n]?i++:(t[i]>e[n]||(s.push(t[i]),i++),n++);return s}(s,l):l)):(s=[],!1)})),s?e.map(function(t){for(var e={},i=[],n=0,s=t.length;s>n;n++)e[t[n]]||(e[t[n]]=!0,i.push(t[n]));return i}(s),(function(t){return a.datums[t]})):[]},all:function(){var t=[];for(var e in this.datums)t.push(this.datums[e]);return t},reset:function(){this.datums={},this.trie=s()},serialize:function(){return{datums:this.datums,trie:this.trie}}}),i}(),a=function(){"use strict";function t(t){this.url=t.url,this.ttl=t.ttl,this.cache=t.cache,this.prepare=t.prepare,this.transform=t.transform,this.transport=t.transport,this.thumbprint=t.thumbprint,this.storage=new s(t.cacheKey)}var i;return i={data:"data",protocol:"protocol",thumbprint:"thumbprint"},e.mixin(t.prototype,{_settings:function(){return{url:this.url,type:"GET",dataType:"json"}},store:function(t){this.cache&&(this.storage.set(i.data,t,this.ttl),this.storage.set(i.protocol,location.protocol,this.ttl),this.storage.set(i.thumbprint,this.thumbprint,this.ttl))},fromCache:function(){var t,e={};return this.cache?(e.data=this.storage.get(i.data),e.protocol=this.storage.get(i.protocol),e.thumbprint=this.storage.get(i.thumbprint),t=e.thumbprint!==this.thumbprint||e.protocol!==location.protocol,e.data&&!t?e.data:null):null},fromNetwork:function(t){var e,i=this;t&&(e=this.prepare(this._settings()),this.transport(e).fail((function(){t(!0)})).done((function(e){t(null,i.transform(e))})))},clear:function(){return this.storage.clear(),this}}),t}(),l=function(){"use strict";function t(t){this.url=t.url,this.prepare=t.prepare,this.transform=t.transform,this.transport=new o({cache:t.cache,limiter:t.limiter,transport:t.transport})}return e.mixin(t.prototype,{_settings:function(){return{url:this.url,type:"GET",dataType:"json"}},get:function(t,e){var i,n=this;if(e)return t=t||"",i=this.prepare(t,this._settings()),this.transport.get(i,(function(t,i){e(t?[]:n.transform(i))}))},cancelLastRequest:function(){this.transport.cancel()}}),t}(),c=function(){"use strict";function i(i){var n;return i?(n={url:null,ttl:864e5,cache:!0,cacheKey:null,thumbprint:"",prepare:e.identity,transform:e.identity,transport:null},i=e.isString(i)?{url:i}:i,!(i=e.mixin(n,i)).url&&t.error("prefetch requires url to be set"),i.transform=i.filter||i.transform,i.cacheKey=i.cacheKey||i.url,i.thumbprint="0.11.1"+i.thumbprint,i.transport=i.transport?s(i.transport):t.ajax,i):null}function n(i){var n;if(i)return n={url:null,cache:!0,prepare:null,replace:null,wildcard:null,limiter:null,rateLimitBy:"debounce",rateLimitWait:300,transform:e.identity,transport:null},i=e.isString(i)?{url:i}:i,!(i=e.mixin(n,i)).url&&t.error("remote requires url to be set"),i.transform=i.filter||i.transform,i.prepare=function(t){function e(t,e){return e.url=o(e.url,t),e}function i(t,e){return e.url=e.url.replace(r,encodeURIComponent(t)),e}function n(t,e){return e}var s,o,r;return s=t.prepare,o=t.replace,r=t.wildcard,s||(s=o?e:t.wildcard?i:n)}(i),i.limiter=function(t){function i(t){return function(i){return e.debounce(i,t)}}function n(t){return function(i){return e.throttle(i,t)}}var s,o,r;return s=t.limiter,o=t.rateLimitBy,r=t.rateLimitWait,s||(s=/^throttle$/i.test(o)?n(r):i(r)),s}(i),i.transport=i.transport?s(i.transport):t.ajax,delete i.replace,delete i.wildcard,delete i.rateLimitBy,delete i.rateLimitWait,i}function s(i){return function(n){var s=t.Deferred();return i(n,(function(t){e.defer((function(){s.resolve(t)}))}),(function(t){e.defer((function(){s.reject(t)}))})),s}}return function(s){var o,r;return o={initialize:!0,identify:e.stringify,datumTokenizer:null,queryTokenizer:null,sufficient:5,sorter:null,local:[],prefetch:null,remote:null},!(s=e.mixin(o,s||{})).datumTokenizer&&t.error("datumTokenizer is required"),!s.queryTokenizer&&t.error("queryTokenizer is required"),r=s.sorter,s.sorter=r?function(t){return t.sort(r)}:e.identity,s.local=e.isFunction(s.local)?s.local():s.local,s.prefetch=i(s.prefetch),s.remote=n(s.remote),s}}(),h=function(){"use strict";function n(t){t=c(t),this.sorter=t.sorter,this.identify=t.identify,this.sufficient=t.sufficient,this.local=t.local,this.remote=t.remote?new l(t.remote):null,this.prefetch=t.prefetch?new a(t.prefetch):null,this.index=new r({identify:this.identify,datumTokenizer:t.datumTokenizer,queryTokenizer:t.queryTokenizer}),!1!==t.initialize&&this.initialize()}var s;return s=window&&window.Bloodhound,n.noConflict=function(){return window&&(window.Bloodhound=s),n},n.tokenizers=i,e.mixin(n.prototype,{__ttAdapter:function(){var t=this;return this.remote?function(e,i,n){return t.search(e,i,n)}:function(e,i){return t.search(e,i)}},_loadPrefetch:function(){var e,i,n=this;return e=t.Deferred(),this.prefetch?(i=this.prefetch.fromCache())?(this.index.bootstrap(i),e.resolve()):this.prefetch.fromNetwork((function(t,i){return t?e.reject():(n.add(i),n.prefetch.store(n.index.serialize()),void e.resolve())})):e.resolve(),e.promise()},_initialize:function(){var t=this;return this.clear(),(this.initPromise=this._loadPrefetch()).done((function(){t.add(t.local)})),this.initPromise},initialize:function(t){return!this.initPromise||t?this._initialize():this.initPromise},add:function(t){return this.index.add(t),this},get:function(t){return t=e.isArray(t)?t:[].slice.call(arguments),this.index.get(t)},search:function(t,i,n){var s,o=this;return s=this.sorter(this.index.search(t)),i(this.remote?s.slice():s),this.remote&&s.length<this.sufficient?this.remote.get(t,(function(t){var i=[];e.each(t,(function(t){!e.some(s,(function(e){return o.identify(t)===o.identify(e)}))&&i.push(t)})),n&&n(i)})):this.remote&&this.remote.cancelLastRequest(),this},all:function(){return this.index.all()},clear:function(){return this.index.reset(),this},clearPrefetchCache:function(){return this.prefetch&&this.prefetch.clear(),this},clearRemoteCache:function(){return o.resetCache(),this},ttAdapter:function(){return this.__ttAdapter()}}),n}();return h})),function(t,e){"function"==typeof define&&define.amd?define("typeahead.js",["jquery"],(function(t){return e(t)})):"object"==typeof exports?module.exports=e(require("jquery")):e(jQuery)}(0,(function(t){var e=function(){"use strict";return{isMsie:function(){return!!/(msie|trident)/i.test(navigator.userAgent)&&navigator.userAgent.match(/(msie |rv:)(\d+(.\d+)?)/i)[2]},isBlankString:function(t){return!t||/^\s*$/.test(t)},escapeRegExChars:function(t){return t.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")},isString:function(t){return"string"==typeof t},isNumber:function(t){return"number"==typeof t},isArray:t.isArray,isFunction:t.isFunction,isObject:t.isPlainObject,isUndefined:function(t){return void 0===t},isElement:function(t){return!(!t||1!==t.nodeType)},isJQuery:function(e){return e instanceof t},toStr:function(t){return e.isUndefined(t)||null===t?"":t+""},bind:t.proxy,each:function(e,i){t.each(e,(function(t,e){return i(e,t)}))},map:t.map,filter:t.grep,every:function(e,i){var n=!0;return e?(t.each(e,(function(t,s){return!!(n=i.call(null,s,t,e))&&void 0})),!!n):n},some:function(e,i){var n=!1;return e?(t.each(e,(function(t,s){return!(n=i.call(null,s,t,e))&&void 0})),!!n):n},mixin:t.extend,identity:function(t){return t},clone:function(e){return t.extend(!0,{},e)},getIdGenerator:function(){var t=0;return function(){return t++}},templatify:function(e){return t.isFunction(e)?e:function(){return String(e)}},defer:function(t){setTimeout(t,0)},debounce:function(t,e,i){var n,s;return function(){var o,r,a=this,l=arguments;return o=function(){n=null,i||(s=t.apply(a,l))},r=i&&!n,clearTimeout(n),n=setTimeout(o,e),r&&(s=t.apply(a,l)),s}},throttle:function(t,e){var i,n,s,o,r,a;return r=0,a=function(){r=new Date,s=null,o=t.apply(i,n)},function(){var l=new Date,c=e-(l-r);return i=this,n=arguments,0>=c?(clearTimeout(s),s=null,r=l,o=t.apply(i,n)):s||(s=setTimeout(a,c)),o}},stringify:function(t){return e.isString(t)?t:JSON.stringify(t)},noop:function(){}}}(),i=function(){"use strict";function t(t){return{wrapper:'<span class="'+t.wrapper+'"></span>',menu:'<div class="'+t.menu+'"></div>'}}function i(t){var i={};return e.each(t,(function(t,e){i[e]="."+t})),i}function n(){var t={wrapper:{position:"relative",display:"inline-block"},hint:{position:"absolute",top:"0",left:"0",borderColor:"transparent",boxShadow:"none",opacity:"1"},input:{position:"relative",verticalAlign:"top",backgroundColor:"transparent"},inputWithNoHint:{position:"relative",verticalAlign:"top"},menu:{position:"absolute",top:"100%",left:"0",zIndex:"100",display:"none"},ltr:{left:"0",right:"auto"},rtl:{left:"auto",right:" 0"}};return e.isMsie()&&e.mixin(t.input,{backgroundImage:"url(data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)"}),t}var s={wrapper:"twitter-typeahead",input:"tt-input",hint:"tt-hint",menu:"tt-menu",dataset:"tt-dataset",suggestion:"tt-suggestion",selectable:"tt-selectable",empty:"tt-empty",open:"tt-open",cursor:"tt-cursor",highlight:"tt-highlight"};return function(o){var r,a;return a=e.mixin({},s,o),{css:(r={css:n(),classes:a,html:t(a),selectors:i(a)}).css,html:r.html,classes:r.classes,selectors:r.selectors,mixin:function(t){e.mixin(t,r)}}}}(),n=function(){"use strict";function i(e){e&&e.el||t.error("EventBus initialized without el"),this.$el=t(e.el)}var n;return"typeahead:",n={render:"rendered",cursorchange:"cursorchanged",select:"selected",autocomplete:"autocompleted"},e.mixin(i.prototype,{_trigger:function(e,i){var n;return n=t.Event("typeahead:"+e),(i=i||[]).unshift(n),this.$el.trigger.apply(this.$el,i),n},before:function(t){var e;return e=[].slice.call(arguments,1),this._trigger("before"+t,e).isDefaultPrevented()},trigger:function(t){var e;this._trigger(t,[].slice.call(arguments,1)),(e=n[t])&&this._trigger(e,[].slice.call(arguments,1))}}),i}(),s=function(){"use strict";function t(t,e,n,s){var o;if(!n)return this;for(e=e.split(i),n=s?function(t,e){return t.bind?t.bind(e):function(){t.apply(e,[].slice.call(arguments,0))}}(n,s):n,this._callbacks=this._callbacks||{};o=e.shift();)this._callbacks[o]=this._callbacks[o]||{sync:[],async:[]},this._callbacks[o][t].push(n);return this}function e(t,e,i){return function(){for(var n,s=0,o=t.length;!n&&o>s;s+=1)n=!1===t[s].apply(e,i);return!n}}var i=/\s+/,n=window.setImmediate?function(t){setImmediate((function(){t()}))}:function(t){setTimeout((function(){t()}),0)};return{onSync:function(e,i,n){return t.call(this,"sync",e,i,n)},onAsync:function(e,i,n){return t.call(this,"async",e,i,n)},off:function(t){var e;if(!this._callbacks)return this;for(t=t.split(i);e=t.shift();)delete this._callbacks[e];return this},trigger:function(t){var s,o,r,a,l;if(!this._callbacks)return this;for(t=t.split(i),r=[].slice.call(arguments,1);(s=t.shift())&&(o=this._callbacks[s]);)a=e(o.sync,this,[s].concat(r)),l=e(o.async,this,[s].concat(r)),a()&&n(l);return this}}}(),o=function(t){"use strict";var i={node:null,pattern:null,tagName:"strong",className:null,wordsOnly:!1,caseSensitive:!1};return function(n){var s;(n=e.mixin({},i,n)).node&&n.pattern&&(n.pattern=e.isArray(n.pattern)?n.pattern:[n.pattern],s=function(t,i,n){for(var s,o=[],r=0,a=t.length;a>r;r++)o.push(e.escapeRegExChars(t[r]));return s=n?"\\b("+o.join("|")+")\\b":"("+o.join("|")+")",i?new RegExp(s):new RegExp(s,"i")}(n.pattern,n.caseSensitive,n.wordsOnly),function t(e,i){for(var n,s=0;s<e.childNodes.length;s++)3===(n=e.childNodes[s]).nodeType?s+=i(n)?1:0:t(n,i)}(n.node,(function(e){var i,o,r;return(i=s.exec(e.data))&&(r=t.createElement(n.tagName),n.className&&(r.className=n.className),(o=e.splitText(i.index)).splitText(i[0].length),r.appendChild(o.cloneNode(!0)),e.parentNode.replaceChild(r,o)),!!i})))}}(window.document),r=function(){"use strict";function i(i,n){(i=i||{}).input||t.error("input is missing"),n.mixin(this),this.$hint=t(i.hint),this.$input=t(i.input),this.query=this.$input.val(),this.queryWhenFocused=this.hasFocus()?this.query:null,this.$overflowHelper=function(e){return t('<pre aria-hidden="true"></pre>').css({position:"absolute",visibility:"hidden",whiteSpace:"pre",fontFamily:e.css("font-family"),fontSize:e.css("font-size"),fontStyle:e.css("font-style"),fontVariant:e.css("font-variant"),fontWeight:e.css("font-weight"),wordSpacing:e.css("word-spacing"),letterSpacing:e.css("letter-spacing"),textIndent:e.css("text-indent"),textRendering:e.css("text-rendering"),textTransform:e.css("text-transform")}).insertAfter(e)}(this.$input),this._checkLanguageDirection(),0===this.$hint.length&&(this.setHint=this.getHint=this.clearHint=this.clearHintIfInvalid=e.noop)}function n(t,e){return i.normalizeQuery(t)===i.normalizeQuery(e)}function o(t){return t.altKey||t.ctrlKey||t.metaKey||t.shiftKey}var r;return r={9:"tab",27:"esc",37:"left",39:"right",13:"enter",38:"up",40:"down"},i.normalizeQuery=function(t){return e.toStr(t).replace(/^\s*/g,"").replace(/\s{2,}/g," ")},e.mixin(i.prototype,s,{_onBlur:function(){this.resetInputValue(),this.trigger("blurred")},_onFocus:function(){this.queryWhenFocused=this.query,this.trigger("focused")},_onKeydown:function(t){var e=r[t.which||t.keyCode];this._managePreventDefault(e,t),e&&this._shouldTrigger(e,t)&&this.trigger(e+"Keyed",t)},_onInput:function(){this._setQuery(this.getInputValue()),this.clearHintIfInvalid(),this._checkLanguageDirection()},_managePreventDefault:function(t,e){var i;switch(t){case"up":case"down":i=!o(e);break;default:i=!1}i&&e.preventDefault()},_shouldTrigger:function(t,e){var i;if("tab"===t)i=!o(e);else i=!0;return i},_checkLanguageDirection:function(){var t=(this.$input.css("direction")||"ltr").toLowerCase();this.dir!==t&&(this.dir=t,this.$hint.attr("dir",t),this.trigger("langDirChanged",t))},_setQuery:function(t,e){var i,s;s=!!(i=n(t,this.query))&&this.query.length!==t.length,this.query=t,e||i?!e&&s&&this.trigger("whitespaceChanged",this.query):this.trigger("queryChanged",this.query)},bind:function(){var t,i,n,s,o=this;return t=e.bind(this._onBlur,this),i=e.bind(this._onFocus,this),n=e.bind(this._onKeydown,this),s=e.bind(this._onInput,this),this.$input.on("blur.tt",t).on("focus.tt",i).on("keydown.tt",n),!e.isMsie()||e.isMsie()>9?this.$input.on("input.tt",s):this.$input.on("keydown.tt keypress.tt cut.tt paste.tt",(function(t){r[t.which||t.keyCode]||e.defer(e.bind(o._onInput,o,t))})),this},focus:function(){this.$input.focus()},blur:function(){this.$input.blur()},getLangDir:function(){return this.dir},getQuery:function(){return this.query||""},setQuery:function(t,e){this.setInputValue(t),this._setQuery(t,e)},hasQueryChangedSinceLastFocus:function(){return this.query!==this.queryWhenFocused},getInputValue:function(){return this.$input.val()},setInputValue:function(t){this.$input.val(t),this.clearHintIfInvalid(),this._checkLanguageDirection()},resetInputValue:function(){this.setInputValue(this.query)},getHint:function(){return this.$hint.val()},setHint:function(t){this.$hint.val(t)},clearHint:function(){this.setHint("")},clearHintIfInvalid:function(){var t,e,i;i=(t=this.getInputValue())!==(e=this.getHint())&&0===e.indexOf(t),!(""!==t&&i&&!this.hasOverflow())&&this.clearHint()},hasFocus:function(){return this.$input.is(":focus")},hasOverflow:function(){var t=this.$input.width()-2;return this.$overflowHelper.text(this.getInputValue()),this.$overflowHelper.width()>=t},isCursorAtEnd:function(){var t,i,n;return t=this.$input.val().length,i=this.$input[0].selectionStart,e.isNumber(i)?i===t:!document.selection||((n=document.selection.createRange()).moveStart("character",-t),t===n.text.length)},destroy:function(){this.$hint.off(".tt"),this.$input.off(".tt"),this.$overflowHelper.remove(),this.$hint=this.$input=this.$overflowHelper=t("<div>")}}),i}(),a=function(){"use strict";function i(i,n){(i=i||{}).templates=i.templates||{},i.templates.notFound=i.templates.notFound||i.templates.empty,i.source||t.error("missing source"),i.node||t.error("missing node"),i.name&&!function(t){return/^[_a-zA-Z0-9-]+$/.test(t)}(i.name)&&t.error("invalid dataset name: "+i.name),n.mixin(this),this.highlight=!!i.highlight,this.name=i.name||r(),this.limit=i.limit||5,this.displayFn=function(t){function i(e){return e[t]}return t=t||e.stringify,e.isFunction(t)?t:i}(i.display||i.displayKey),this.templates=function(i,n){function s(e){return t("<div>").text(n(e))}return{notFound:i.notFound&&e.templatify(i.notFound),pending:i.pending&&e.templatify(i.pending),header:i.header&&e.templatify(i.header),footer:i.footer&&e.templatify(i.footer),suggestion:i.suggestion||s}}(i.templates,this.displayFn),this.source=i.source.__ttAdapter?i.source.__ttAdapter():i.source,this.async=e.isUndefined(i.async)?this.source.length>2:!!i.async,this._resetLastSuggestion(),this.$el=t(i.node).addClass(this.classes.dataset).addClass(this.classes.dataset+"-"+this.name)}var n,r;return n={val:"tt-selectable-display",obj:"tt-selectable-object"},r=e.getIdGenerator(),i.extractData=function(e){var i=t(e);return i.data(n.obj)?{val:i.data(n.val)||"",obj:i.data(n.obj)||null}:null},e.mixin(i.prototype,s,{_overwrite:function(t,e){(e=e||[]).length?this._renderSuggestions(t,e):this.async&&this.templates.pending?this._renderPending(t):!this.async&&this.templates.notFound?this._renderNotFound(t):this._empty(),this.trigger("rendered",this.name,e,!1)},_append:function(t,e){(e=e||[]).length&&this.$lastSuggestion.length?this._appendSuggestions(t,e):e.length?this._renderSuggestions(t,e):!this.$lastSuggestion.length&&this.templates.notFound&&this._renderNotFound(t),this.trigger("rendered",this.name,e,!0)},_renderSuggestions:function(t,e){var i;i=this._getSuggestionsFragment(t,e),this.$lastSuggestion=i.children().last(),this.$el.html(i).prepend(this._getHeader(t,e)).append(this._getFooter(t,e))},_appendSuggestions:function(t,e){var i,n;n=(i=this._getSuggestionsFragment(t,e)).children().last(),this.$lastSuggestion.after(i),this.$lastSuggestion=n},_renderPending:function(t){var e=this.templates.pending;this._resetLastSuggestion(),e&&this.$el.html(e({query:t,dataset:this.name}))},_renderNotFound:function(t){var e=this.templates.notFound;this._resetLastSuggestion(),e&&this.$el.html(e({query:t,dataset:this.name}))},_empty:function(){this.$el.empty(),this._resetLastSuggestion()},_getSuggestionsFragment:function(i,s){var r,a=this;return r=document.createDocumentFragment(),e.each(s,(function(e){var s,o;o=a._injectQuery(i,e),s=t(a.templates.suggestion(o)).data(n.obj,e).data(n.val,a.displayFn(e)).addClass(a.classes.suggestion+" "+a.classes.selectable),r.appendChild(s[0])})),this.highlight&&o({className:this.classes.highlight,node:r,pattern:i}),t(r)},_getFooter:function(t,e){return this.templates.footer?this.templates.footer({query:t,suggestions:e,dataset:this.name}):null},_getHeader:function(t,e){return this.templates.header?this.templates.header({query:t,suggestions:e,dataset:this.name}):null},_resetLastSuggestion:function(){this.$lastSuggestion=t()},_injectQuery:function(t,i){return e.isObject(i)?e.mixin({_query:t},i):i},update:function(e){function i(t){o||(o=!0,t=(t||[]).slice(0,n.limit),r=t.length,n._overwrite(e,t),r<n.limit&&n.async&&n.trigger("asyncRequested",e))}var n=this,s=!1,o=!1,r=0;this.cancel(),this.cancel=function(){s=!0,n.cancel=t.noop,n.async&&n.trigger("asyncCanceled",e)},this.source(e,i,(function(i){i=i||[],!s&&r<n.limit&&(n.cancel=t.noop,r+=i.length,n._append(e,i.slice(0,n.limit-r)),n.async&&n.trigger("asyncReceived",e))})),!o&&i([])},cancel:t.noop,clear:function(){this._empty(),this.cancel(),this.trigger("cleared")},isEmpty:function(){return this.$el.is(":empty")},destroy:function(){this.$el=t("<div>")}}),i}(),l=function(){"use strict";function i(i,n){var s=this;(i=i||{}).node||t.error("node is required"),n.mixin(this),this.$node=t(i.node),this.query=null,this.datasets=e.map(i.datasets,(function(e){var i=s.$node.find(e.node).first();return e.node=i.length?i:t("<div>").appendTo(s.$node),new a(e,n)}))}return e.mixin(i.prototype,s,{_onSelectableClick:function(e){this.trigger("selectableClicked",t(e.currentTarget))},_onRendered:function(t,e,i,n){this.$node.toggleClass(this.classes.empty,this._allDatasetsEmpty()),this.trigger("datasetRendered",e,i,n)},_onCleared:function(){this.$node.toggleClass(this.classes.empty,this._allDatasetsEmpty()),this.trigger("datasetCleared")},_propagate:function(){this.trigger.apply(this,arguments)},_allDatasetsEmpty:function(){return e.every(this.datasets,(function(t){return t.isEmpty()}))},_getSelectables:function(){return this.$node.find(this.selectors.selectable)},_removeCursor:function(){var t=this.getActiveSelectable();t&&t.removeClass(this.classes.cursor)},_ensureVisible:function(t){var e,i,n,s;i=(e=t.position().top)+t.outerHeight(!0),n=this.$node.scrollTop(),s=this.$node.height()+parseInt(this.$node.css("paddingTop"),10)+parseInt(this.$node.css("paddingBottom"),10),0>e?this.$node.scrollTop(n+e):i>s&&this.$node.scrollTop(n+(i-s))},bind:function(){var t,i=this;return t=e.bind(this._onSelectableClick,this),this.$node.on("click.tt",this.selectors.selectable,t),e.each(this.datasets,(function(t){t.onSync("asyncRequested",i._propagate,i).onSync("asyncCanceled",i._propagate,i).onSync("asyncReceived",i._propagate,i).onSync("rendered",i._onRendered,i).onSync("cleared",i._onCleared,i)})),this},isOpen:function(){return this.$node.hasClass(this.classes.open)},open:function(){this.$node.addClass(this.classes.open)},close:function(){this.$node.removeClass(this.classes.open),this._removeCursor()},setLanguageDirection:function(t){this.$node.attr("dir",t)},selectableRelativeToCursor:function(t){var e,i,n;return i=this.getActiveSelectable(),e=this._getSelectables(),-1===(n=-1>(n=((n=(i?e.index(i):-1)+t)+1)%(e.length+1)-1)?e.length-1:n)?null:e.eq(n)},setCursor:function(t){this._removeCursor(),(t=t&&t.first())&&(t.addClass(this.classes.cursor),this._ensureVisible(t))},getSelectableData:function(t){return t&&t.length?a.extractData(t):null},getActiveSelectable:function(){var t=this._getSelectables().filter(this.selectors.cursor).first();return t.length?t:null},getTopSelectable:function(){var t=this._getSelectables().first();return t.length?t:null},update:function(t){var i=t!==this.query;return i&&(this.query=t,e.each(this.datasets,(function(e){e.update(t)}))),i},empty:function(){e.each(this.datasets,(function(t){t.clear()})),this.query=null,this.$node.addClass(this.classes.empty)},destroy:function(){this.$node.off(".tt"),this.$node=t("<div>"),e.each(this.datasets,(function(t){t.destroy()}))}}),i}(),c=function(){"use strict";function t(){l.apply(this,[].slice.call(arguments,0))}var i=l.prototype;return e.mixin(t.prototype,l.prototype,{open:function(){return!this._allDatasetsEmpty()&&this._show(),i.open.apply(this,[].slice.call(arguments,0))},close:function(){return this._hide(),i.close.apply(this,[].slice.call(arguments,0))},_onRendered:function(){return this._allDatasetsEmpty()?this._hide():this.isOpen()&&this._show(),i._onRendered.apply(this,[].slice.call(arguments,0))},_onCleared:function(){return this._allDatasetsEmpty()?this._hide():this.isOpen()&&this._show(),i._onCleared.apply(this,[].slice.call(arguments,0))},setLanguageDirection:function(t){return this.$node.css("ltr"===t?this.css.ltr:this.css.rtl),i.setLanguageDirection.apply(this,[].slice.call(arguments,0))},_hide:function(){this.$node.hide()},_show:function(){this.$node.css("display","block")}}),t}(),h=function(){"use strict";function i(i,s){var o,r,a,l,c,h,u,d,p,f,g;(i=i||{}).input||t.error("missing input"),i.menu||t.error("missing menu"),i.eventBus||t.error("missing event bus"),s.mixin(this),this.eventBus=i.eventBus,this.minLength=e.isNumber(i.minLength)?i.minLength:1,this.input=i.input,this.menu=i.menu,this.enabled=!0,this.active=!1,this.input.hasFocus()&&this.activate(),this.dir=this.input.getLangDir(),this._hacks(),this.menu.bind().onSync("selectableClicked",this._onSelectableClicked,this).onSync("asyncRequested",this._onAsyncRequested,this).onSync("asyncCanceled",this._onAsyncCanceled,this).onSync("asyncReceived",this._onAsyncReceived,this).onSync("datasetRendered",this._onDatasetRendered,this).onSync("datasetCleared",this._onDatasetCleared,this),o=n(this,"activate","open","_onFocused"),r=n(this,"deactivate","_onBlurred"),a=n(this,"isActive","isOpen","_onEnterKeyed"),l=n(this,"isActive","isOpen","_onTabKeyed"),c=n(this,"isActive","_onEscKeyed"),h=n(this,"isActive","open","_onUpKeyed"),u=n(this,"isActive","open","_onDownKeyed"),d=n(this,"isActive","isOpen","_onLeftKeyed"),p=n(this,"isActive","isOpen","_onRightKeyed"),f=n(this,"_openIfActive","_onQueryChanged"),g=n(this,"_openIfActive","_onWhitespaceChanged"),this.input.bind().onSync("focused",o,this).onSync("blurred",r,this).onSync("enterKeyed",a,this).onSync("tabKeyed",l,this).onSync("escKeyed",c,this).onSync("upKeyed",h,this).onSync("downKeyed",u,this).onSync("leftKeyed",d,this).onSync("rightKeyed",p,this).onSync("queryChanged",f,this).onSync("whitespaceChanged",g,this).onSync("langDirChanged",this._onLangDirChanged,this)}function n(t){var i=[].slice.call(arguments,1);return function(){var n=[].slice.call(arguments);e.each(i,(function(e){return t[e].apply(t,n)}))}}return e.mixin(i.prototype,{_hacks:function(){var i,n;i=this.input.$input||t("<div>"),n=this.menu.$node||t("<div>"),i.on("blur.tt",(function(t){var s,o,r;s=document.activeElement,o=n.is(s),r=n.has(s).length>0,e.isMsie()&&(o||r)&&(t.preventDefault(),t.stopImmediatePropagation(),e.defer((function(){i.focus()})))})),n.on("mousedown.tt",(function(t){t.preventDefault()}))},_onSelectableClicked:function(t,e){this.select(e)},_onDatasetCleared:function(){this._updateHint()},_onDatasetRendered:function(t,e,i,n){this._updateHint(),this.eventBus.trigger("render",i,n,e)},_onAsyncRequested:function(t,e,i){this.eventBus.trigger("asyncrequest",i,e)},_onAsyncCanceled:function(t,e,i){this.eventBus.trigger("asynccancel",i,e)},_onAsyncReceived:function(t,e,i){this.eventBus.trigger("asyncreceive",i,e)},_onFocused:function(){this._minLengthMet()&&this.menu.update(this.input.getQuery())},_onBlurred:function(){this.input.hasQueryChangedSinceLastFocus()&&this.eventBus.trigger("change",this.input.getQuery())},_onEnterKeyed:function(t,e){var i;(i=this.menu.getActiveSelectable())&&this.select(i)&&e.preventDefault()},_onTabKeyed:function(t,e){var i;(i=this.menu.getActiveSelectable())?this.select(i)&&e.preventDefault():(i=this.menu.getTopSelectable())&&this.autocomplete(i)&&e.preventDefault()},_onEscKeyed:function(){this.close()},_onUpKeyed:function(){this.moveCursor(-1)},_onDownKeyed:function(){this.moveCursor(1)},_onLeftKeyed:function(){"rtl"===this.dir&&this.input.isCursorAtEnd()&&this.autocomplete(this.menu.getTopSelectable())},_onRightKeyed:function(){"ltr"===this.dir&&this.input.isCursorAtEnd()&&this.autocomplete(this.menu.getTopSelectable())},_onQueryChanged:function(t,e){this._minLengthMet(e)?this.menu.update(e):this.menu.empty()},_onWhitespaceChanged:function(){this._updateHint()},_onLangDirChanged:function(t,e){this.dir!==e&&(this.dir=e,this.menu.setLanguageDirection(e))},_openIfActive:function(){this.isActive()&&this.open()},_minLengthMet:function(t){return(t=e.isString(t)?t:this.input.getQuery()||"").length>=this.minLength},_updateHint:function(){var t,i,n,s,o,a;t=this.menu.getTopSelectable(),i=this.menu.getSelectableData(t),n=this.input.getInputValue(),!i||e.isBlankString(n)||this.input.hasOverflow()?this.input.clearHint():(s=r.normalizeQuery(n),o=e.escapeRegExChars(s),(a=new RegExp("^(?:"+o+")(.+$)","i").exec(i.val))&&this.input.setHint(n+a[1]))},isEnabled:function(){return this.enabled},enable:function(){this.enabled=!0},disable:function(){this.enabled=!1},isActive:function(){return this.active},activate:function(){return!!this.isActive()||!(!this.isEnabled()||this.eventBus.before("active"))&&(this.active=!0,this.eventBus.trigger("active"),!0)},deactivate:function(){return!this.isActive()||!this.eventBus.before("idle")&&(this.active=!1,this.close(),this.eventBus.trigger("idle"),!0)},isOpen:function(){return this.menu.isOpen()},open:function(){return this.isOpen()||this.eventBus.before("open")||(this.menu.open(),this._updateHint(),this.eventBus.trigger("open")),this.isOpen()},close:function(){return this.isOpen()&&!this.eventBus.before("close")&&(this.menu.close(),this.input.clearHint(),this.input.resetInputValue(),this.eventBus.trigger("close")),!this.isOpen()},setVal:function(t){this.input.setQuery(e.toStr(t))},getVal:function(){return this.input.getQuery()},select:function(t){var e=this.menu.getSelectableData(t);return!(!e||this.eventBus.before("select",e.obj))&&(this.input.setQuery(e.val,!0),this.eventBus.trigger("select",e.obj),this.close(),!0)},autocomplete:function(t){var e,i;return e=this.input.getQuery(),!(!((i=this.menu.getSelectableData(t))&&e!==i.val)||this.eventBus.before("autocomplete",i.obj))&&(this.input.setQuery(i.val),this.eventBus.trigger("autocomplete",i.obj),!0)},moveCursor:function(t){var e,i,n,s;return e=this.input.getQuery(),i=this.menu.selectableRelativeToCursor(t),s=(n=this.menu.getSelectableData(i))?n.obj:null,!(this._minLengthMet()&&this.menu.update(e))&&!this.eventBus.before("cursorchange",s)&&(this.menu.setCursor(i),n?this.input.setInputValue(n.val):(this.input.resetInputValue(),this._updateHint()),this.eventBus.trigger("cursorchange",s),!0)},destroy:function(){this.input.destroy(),this.menu.destroy()}}),i}();!function(){"use strict";function s(e,i){e.each((function(){var e,n=t(this);(e=n.data(p.typeahead))&&i(e,n)}))}function o(t,e){return t.clone().addClass(e.classes.hint).removeData().css(e.css.hint).css(function(t){return{backgroundAttachment:t.css("background-attachment"),backgroundClip:t.css("background-clip"),backgroundColor:t.css("background-color"),backgroundImage:t.css("background-image"),backgroundOrigin:t.css("background-origin"),backgroundPosition:t.css("background-position"),backgroundRepeat:t.css("background-repeat"),backgroundSize:t.css("background-size")}}(t)).prop("readonly",!0).removeAttr("id name placeholder required").attr({autocomplete:"off",spellcheck:"false",tabindex:-1})}function a(t){var i,n;i=t.data(p.www),n=t.parent().filter(i.selectors.wrapper),e.each(t.data(p.attrs),(function(i,n){e.isUndefined(i)?t.removeAttr(n):t.attr(n,i)})),t.removeData(p.typeahead).removeData(p.www).removeData(p.attr).removeClass(i.classes.input),n.length&&(t.detach().insertAfter(n),n.remove())}function u(i){var n;return(n=e.isJQuery(i)||e.isElement(i)?t(i).first():[]).length?n:null}var d,p,f;d=t.fn.typeahead,p={www:"tt-www",attrs:"tt-attrs",typeahead:"tt-typeahead"},f={initialize:function(s,a){var d;return a=e.isArray(a)?a:[].slice.call(arguments,1),d=i((s=s||{}).classNames),this.each((function(){var i,f,g,m,v,y,b,w,_,x,C;e.each(a,(function(t){t.highlight=!!s.highlight})),i=t(this),f=t(d.html.wrapper),g=u(s.hint),m=u(s.menu),v=!1!==s.hint&&!g,y=!1!==s.menu&&!m,v&&(g=o(i,d)),y&&(m=t(d.html.menu).css(d.css.menu)),g&&g.val(""),i=function(t,e){t.data(p.attrs,{dir:t.attr("dir"),autocomplete:t.attr("autocomplete"),spellcheck:t.attr("spellcheck"),style:t.attr("style")}),t.addClass(e.classes.input).attr({autocomplete:"off",spellcheck:!1});try{!t.attr("dir")&&t.attr("dir","auto")}catch(t){}return t}(i,d),(v||y)&&(f.css(d.css.wrapper),i.css(v?d.css.input:d.css.inputWithNoHint),i.wrap(f).parent().prepend(v?g:null).append(y?m:null)),C=y?c:l,b=new n({el:i}),w=new r({hint:g,input:i},d),_=new C({node:m,datasets:a},d),x=new h({input:w,menu:_,eventBus:b,minLength:s.minLength},d),i.data(p.www,d),i.data(p.typeahead,x)}))},isEnabled:function(){var t;return s(this.first(),(function(e){t=e.isEnabled()})),t},enable:function(){return s(this,(function(t){t.enable()})),this},disable:function(){return s(this,(function(t){t.disable()})),this},isActive:function(){var t;return s(this.first(),(function(e){t=e.isActive()})),t},activate:function(){return s(this,(function(t){t.activate()})),this},deactivate:function(){return s(this,(function(t){t.deactivate()})),this},isOpen:function(){var t;return s(this.first(),(function(e){t=e.isOpen()})),t},open:function(){return s(this,(function(t){t.open()})),this},close:function(){return s(this,(function(t){t.close()})),this},select:function(e){var i=!1,n=t(e);return s(this.first(),(function(t){i=t.select(n)})),i},autocomplete:function(e){var i=!1,n=t(e);return s(this.first(),(function(t){i=t.autocomplete(n)})),i},moveCursor:function(t){var e=!1;return s(this.first(),(function(i){e=i.moveCursor(t)})),e},val:function(t){var e;return arguments.length?(s(this,(function(e){e.setVal(t)})),this):(s(this.first(),(function(t){e=t.getVal()})),e)},destroy:function(){return s(this,(function(t,e){a(e),t.destroy()})),this}},t.fn.typeahead=function(t){return f[t]?f[t].apply(this,[].slice.call(arguments,1)):f.initialize.apply(this,arguments)},t.fn.typeahead.noConflict=function(){return t.fn.typeahead=d,this}}()}));
